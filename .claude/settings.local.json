{"permissions": {"allow": ["Bash(ls:*)", "<PERSON><PERSON>(mv:*)", "Bash(npm run lint)", "Bash(pnpm lint)", "Bash(rm:*)", "Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(pnpm test:*)", "Bash(npm ls:*)", "mcp__ide__getDiagnostics", "Bash(npx nuxt typecheck)", "<PERSON><PERSON>(cat:*)", "Bash(npm run typecheck:*)", "Bash(node:*)", "Bash(npm run dev:*)", "Bash(pnpm typecheck:*)", "<PERSON><PERSON>(touch:*)", "Bash(git add:*)", "<PERSON><PERSON>(vitest run:*)", "Bash(pnpm vitest run:*)", "Bash(npx vitest:*)", "Bash(pnpm build:*)", "Bash(npx vitest run:*)", "Bash(pnpm vitest:*)", "Bash(pnpm lint:*)", "Bash(npx vue-tsc:*)", "Bash(npx tsc:*)", "Bash(rg:*)", "Bash(npx eslint:*)", "Bash(pnpm tsc:*)"], "deny": []}}