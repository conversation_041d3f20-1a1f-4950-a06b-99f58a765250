# Flow Module - Agent Knowledge Update

**Date:** 2024-12-19  
**Module Name:** flow  
**BMAD Agent:** BMAD Orchestrator  
**Status:** Agent Knowledge Update Complete

## Executive Summary

This document provides comprehensive knowledge updates for all BMAD agents regarding the Flow module - a sophisticated visual workflow builder that integrates LangGraph's stateful AI capabilities into the PartnersInBiz platform. All agents should understand this module's architecture, integration patterns, and development guidelines to effectively work with or extend the Flow module.

## Flow Module Overview

### Core Purpose
The Flow module is a **visual workflow builder** that enables business users to create, execute, and manage complex AI workflows through an intuitive drag-and-drop interface. It transforms sophisticated LangGraph capabilities into accessible business automation tools.

### Target Users
- **Business Analysts**: Data analysis and reporting workflows
- **Content Creators**: Multi-stage content creation and optimization
- **Customer Success Managers**: Customer interaction and support automation

### Key Capabilities
- **Visual Workflow Creation**: Drag-and-drop canvas with Vue Flow integration
- **Stateful AI Execution**: LangGraph-powered workflows with persistent state
- **Real-time Visualization**: Live execution tracking with WebSocket updates
- **Template System**: Persona-specific workflow templates
- **AI-Assisted Creation**: Natural language flow generation
- **Testing & Debugging**: Business-user-friendly testing interface

## Technical Architecture Knowledge

### Technology Stack
```typescript
// Core Framework Stack
Vue 3.4+ with Composition API
Nuxt 3.8+ for SSR and module architecture
TypeScript 5.8+ in strict mode

// UI & Visualization
Vue Flow 1.3+ for visual workflow canvas
Shuriken UI components for consistent design
Tailwind CSS for utility-first styling

// State Management
Pinia 2.1+ for complex state management
Nuxt useState for reactive global state

// Backend Integration
LangGraph.js for workflow execution
Firebase Firestore via useDataApi patterns
WebSocket for real-time communication
```

### Module Structure Pattern
```
layers/flow/
├── components/          # Vue components (canvas, nodes, panels)
├── composables/         # Vue composables (state, execution, templates)
├── types/              # TypeScript definitions
├── utils/              # Utility functions
├── server/api/         # Server-side API routes
├── stores/             # Pinia stores
└── plugins/            # Nuxt plugins
```

### Core Data Models
```typescript
// Primary entities agents should understand
interface FlowGraph {
  id: string
  workspace_id: string
  user_id: string
  nodes: FlowNode[]
  edges: FlowEdge[]
  is_stateful: boolean
  execution_count: number
}

interface FlowExecution {
  id: string
  flow_id: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  execution_state: Record<string, any>
  real_time_updates: boolean
}

interface FlowTemplate {
  id: string
  category: TemplateCategory
  target_personas: string[]
  template_flow: FlowGraph
  usage_count: number
}
```

### Integration Patterns

#### Platform Integration
- **Authentication**: Uses existing `useAuth` and `useUserSession` from @layers/core
- **Data Access**: Follows `useDataApi` patterns for Firestore operations
- **UI Components**: Extends Shuriken UI design system
- **Vector Search**: Integrates with existing `useVectorSearch` capabilities

#### Cross-Module Communication
```typescript
// Event-driven integration pattern
const { eventBus } = useModuleEventBus()

// Flow execution triggers
eventBus.emit('flow:execution_completed', {
  flowId,
  executionId,
  results,
  notify_chat: true,
  update_crm: true
})

// Other modules can trigger flows
eventBus.on('chat:flow_trigger_requested', async (event) => {
  await executeFlow(event.flowId, event.context)
})
```

### Security Architecture
- **API Key Encryption**: AES-256-GCM with workspace-specific keys
- **Access Control**: Role-based permissions with workspace isolation
- **Approval Workflows**: Multi-level approval for AI-generated flows
- **Audit Logging**: Comprehensive security audit trails

### Performance Optimization
- **Virtual Canvas**: Handles 500+ nodes with virtual scrolling
- **Smart Caching**: Multi-level caching with TTL management
- **Real-time Updates**: Efficient WebSocket communication
- **Memory Management**: Automatic cleanup and optimization

## API Endpoints & Integration Points

### Core API Endpoints
```typescript
// Flow CRUD operations (via useDataApi)
POST /api/data/flows              // Create/update flows
GET  /api/data/flows              // List flows
GET  /api/data/flows/{id}         // Get specific flow

// Flow-specific operations
POST /api/flow/execute/{id}       // Execute flow
POST /api/flow/test/start         // Start test execution
POST /api/flow/ai/generate        // AI-assisted generation
POST /api/flow/keys/encrypt       // Secure API key storage

// WebSocket endpoints
WS   /api/flow/execution/stream   // Real-time execution updates
WS   /api/flow/events/sync        // Module synchronization
```

### Integration Composables
```typescript
// Key composables other modules should use
useFlowExecution()     // Execute and monitor flows
useFlowTemplates()     // Access template library
useFlowEventBus()      // Cross-module communication
useModuleIntegration() // Register module integrations
```

## Development Guidelines for Agents

### For Developer Agents
- **Follow Vue 3 Composition API patterns** with `<script setup>` syntax
- **Use TypeScript strict mode** for all Flow module code
- **Implement proper error boundaries** and loading states
- **Follow existing useDataApi patterns** for data operations
- **Use Shuriken UI components** for consistency

### For QA/Testing Agents
- **Test Framework**: Vitest + Vue Test Utils for unit tests
- **E2E Testing**: Playwright for user flow testing
- **Accessibility**: axe-core for WCAG 2.1 AA compliance
- **Performance**: Lighthouse CI for performance monitoring
- **Coverage Target**: 80%+ for core composables and utilities

### For DevOps Agents
- **Infrastructure**: Nuxt 3 server with WebSocket support
- **Database**: Firebase Firestore with compound indexes
- **Monitoring**: Real-time execution metrics and error tracking
- **Security**: API key encryption and audit logging
- **Scaling**: Queue management for concurrent executions

### For Design Agents
- **Design System**: Extend Shuriken UI with Flow-specific components
- **Accessibility**: WCAG 2.1 AA compliance with keyboard navigation
- **Responsive**: Desktop-first with tablet support
- **Performance**: Virtual rendering for large workflows
- **User Experience**: Persona-specific interfaces and progressive disclosure

## Node Types & Workflow Patterns

### Core Node Types (Tier 1)
- **input**: Data input from user/system
- **llm_prompt**: LLM interaction with prompt templates
- **conditional**: If/then logic branching
- **output**: Final result output
- **data_transform**: Data manipulation/formatting
- **vector_search**: Semantic search using existing vector DB

### Business Node Types
- **approval_gate**: Human-in-the-loop approval
- **email_send**: Email notifications
- **data_filter**: Filter/sort data operations
- **template_fill**: Document template filling

### Workflow Patterns
- **Linear Workflows**: Sequential processing with state passing
- **Conditional Workflows**: Branching logic based on data/conditions
- **Cyclical Workflows**: Loops and iterative processing
- **Parallel Workflows**: Concurrent execution paths
- **Human-in-the-Loop**: Approval gates and manual interventions

## Testing & Quality Assurance

### Testing Strategy
```typescript
// Unit Testing
- Location: Co-located with source files (*.test.ts)
- Framework: Vitest + Vue Test Utils
- Coverage: 80%+ for core composables

// Integration Testing
- Flow execution end-to-end testing
- Template creation and usage testing
- Security validation testing
- Performance benchmarking

// User Acceptance Testing
- Persona-specific workflow validation
- Template usability testing
- Real-time collaboration testing
```

### Quality Gates
- **Performance**: Lighthouse score 90+, <3s time to interactive
- **Accessibility**: Zero axe violations, 100% keyboard accessible
- **Security**: Comprehensive security audit passing
- **Browser Support**: Modern browsers (Chrome, Firefox, Safari, Edge)

## Deployment & Infrastructure

### Infrastructure Requirements
- **Nuxt 3 Server**: API endpoints and WebSocket handling
- **Firebase Firestore**: Data persistence with workspace isolation
- **Vector Database**: Integration for semantic search capabilities
- **LangGraph Environment**: Workflow execution infrastructure

### Monitoring & Observability
- **Execution Metrics**: Performance and success rate monitoring
- **Security Auditing**: Comprehensive audit log analysis
- **User Analytics**: Interaction patterns and usage metrics
- **Error Tracking**: Real-time error monitoring and alerting

## Future Integration Roadmap

### Planned Module Integrations
- **Chat Module**: Flow triggers from chat conversations
- **CRM Module**: Customer data integration and workflow triggers
- **Book Writing Module**: Content generation workflow integration
- **Analytics Module**: Advanced workflow performance analytics

### Extension Points
- **Custom Node SDK**: Framework for third-party node development
- **Webhook Integration**: External system integration capabilities
- **Advanced Templates**: Industry-specific workflow templates
- **Enterprise Features**: Advanced compliance and governance tools

---

## Agent-Specific Knowledge Updates

### Developer Agent Customization
```
Specialized in Vue 3 + Nuxt 3 + TypeScript for visual workflow builder. Using LangGraph, Firebase Firestore, and following modular layer architecture. Expert in real-time WebSocket communication, state management with Pinia, and Shuriken UI design system integration.
```

### QA Tester Agent Customization
```
Testing visual workflow builder with focus on accessibility, performance, and real-time functionality. Using Vitest + Vue Test Utils for unit testing, Playwright for E2E testing, and axe-core for accessibility validation. Specialized in workflow execution testing and template validation.
```

### DevOps Engineer Customization
```
Managing deployment of Nuxt 3 application with WebSocket support and LangGraph integration. Infrastructure includes Firebase Firestore, vector database, and real-time execution monitoring. Specialized in workflow execution scaling and security audit logging.
```

### Design Architect Customization
```
Designing visual workflow builder interfaces with focus on accessibility and persona-specific user experiences. Using Shuriken UI design system with Flow-specific extensions. Expert in real-time execution visualization and progressive complexity disclosure.
```

---

**Knowledge Update Status**: ✅ **COMPLETE**  
**All Agents Updated**: ✅ **YES**  
**Integration Ready**: ✅ **YES**  
**Next Phase**: SM - Documentation Sharding

---

**BMAD Orchestrator:** BMAD Orchestrator Agent  
**Knowledge Update Date:** 2024-12-19  
**Knowledge Version:** 1.0  
**Update Status:** All Agents Successfully Updated
