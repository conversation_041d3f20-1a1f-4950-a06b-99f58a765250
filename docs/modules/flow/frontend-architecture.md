# Flow Module - Frontend Architecture Document

**Date:** 2024-12-19
**Module Name:** flow
**Design Architect:** <PERSON> (Design Architect Agent)
**Status:** Frontend Architecture Complete - Ready for Implementation

## Executive Summary

The Flow module frontend architecture delivers a world-class visual workflow builder that transforms complex AI automation into an intuitive, accessible experience for business users. This architecture combines sophisticated technical capabilities with exceptional user experience design, featuring real-time execution visualization, persona-specific interfaces, and seamless integration with the PartnersInBiz design system.

## Frontend Philosophy & Design Principles

### 1. User-Centric Design Philosophy

**"Complexity Hidden, Power Revealed"**
- **Progressive Disclosure**: Advanced features emerge naturally as users gain expertise
- **Contextual Intelligence**: Interface adapts to user role and workflow complexity
- **Immediate Feedback**: Real-time visual feedback for all user interactions
- **Error Prevention**: Proactive validation and guidance to prevent user mistakes

### 2. Core Design Principles

#### **Accessibility-First Design**
- **WCAG 2.1 AA Compliance**: Full keyboard navigation, screen reader support, color contrast
- **Inclusive Interactions**: Touch-friendly targets, clear focus indicators, alternative input methods
- **Cognitive Accessibility**: Clear language, consistent patterns, reduced cognitive load

#### **Performance-Optimized Experience**
- **Perceived Performance**: Instant feedback, skeleton loading, optimistic updates
- **Scalable Visualization**: Smooth performance with 500+ node workflows
- **Efficient Rendering**: Virtual scrolling, lazy loading, smart caching

#### **Responsive & Adaptive Design**
- **Desktop-First**: Optimized for complex workflow creation
- **Tablet-Friendly**: Touch interactions for review and monitoring
- **Context-Aware**: Interface adapts to screen size and interaction method

## Overall Frontend Architecture

### 1. Technology Stack & Framework Choices

#### **Core Framework Stack**
```typescript
// Primary Framework
Vue 3.4+ with Composition API
Nuxt 3.8+ for SSR and module architecture
TypeScript 5.8+ in strict mode

// UI & Visualization
Vue Flow 1.3+ for visual workflow canvas
Shuriken UI components for consistent design
Tailwind CSS for utility-first styling
Headless UI for accessible primitives

// State Management
Pinia 2.1+ for complex state management
Nuxt useState for reactive global state
VueUse for composable utilities

// Real-time Communication
WebSocket native API with reconnection logic
EventSource for server-sent events
Custom WebSocket composables for Flow-specific needs
```

#### **Specialized Libraries**
```typescript
// Canvas & Visualization
@vue-flow/core - Core workflow canvas
@vue-flow/controls - Canvas controls (zoom, pan, minimap)
@vue-flow/background - Grid and pattern backgrounds
d3-selection - Advanced SVG manipulations for custom nodes

// Animation & Interactions
@vueuse/motion - Smooth animations and transitions
@vueuse/gesture - Touch and gesture support
framer-motion-vue - Complex animation sequences

// Accessibility & Testing
@vue/test-utils - Component testing
@testing-library/vue - User-centric testing
axe-core - Accessibility testing
```

### 2. High-Level Component Architecture

```mermaid
graph TB
    subgraph "Flow Module Frontend"
        subgraph "Layout Layer"
            FlowLayout[FlowLayout.vue]
            Sidebar[FlowSidebar.vue]
            Toolbar[FlowToolbar.vue]
            StatusBar[FlowStatusBar.vue]
        end

        subgraph "Canvas Layer"
            Canvas[FlowCanvas.vue]
            Viewport[CanvasViewport.vue]
            Grid[CanvasGrid.vue]
            Minimap[CanvasMinimap.vue]
        end

        subgraph "Node System"
            NodeRenderer[NodeRenderer.vue]
            NodeLibrary[NodeLibrary.vue]
            NodeConfig[NodeConfigPanel.vue]
            NodeTypes[Node Type Components]
        end

        subgraph "Execution Layer"
            ExecVisualization[ExecutionVisualization.vue]
            TestingPanel[TestingPanel.vue]
            DebugConsole[DebugConsole.vue]
            StatusIndicators[StatusIndicators.vue]
        end

        subgraph "Template System"
            TemplateLibrary[TemplateLibrary.vue]
            TemplateCard[TemplateCard.vue]
            TemplateWizard[TemplateWizard.vue]
            PersonaFilter[PersonaFilter.vue]
        end

        subgraph "Shared Components"
            Modal[FlowModal.vue]
            Toast[FlowToast.vue]
            Loading[FlowLoading.vue]
            ErrorBoundary[FlowErrorBoundary.vue]
        end
    end

    subgraph "Shuriken UI System"
        BaseButton[BaseButton]
        BaseInput[BaseInput]
        BaseCard[BaseCard]
        BaseModal[BaseModal]
    end

    FlowLayout --> Canvas
    FlowLayout --> Sidebar
    FlowLayout --> Toolbar

    Canvas --> NodeRenderer
    Canvas --> ExecVisualization

    Sidebar --> NodeLibrary
    Sidebar --> TemplateLibrary

    NodeTypes --> BaseButton
    TemplateCard --> BaseCard
    Modal --> BaseModal
```

## Detailed Frontend Directory Structure

```
layers/flow/
├── components/
│   ├── canvas/                     # Core canvas components
│   │   ├── FlowCanvas.vue         # Main workflow canvas
│   │   ├── CanvasViewport.vue     # Viewport management
│   │   ├── CanvasGrid.vue         # Background grid
│   │   ├── CanvasMinimap.vue      # Navigation minimap
│   │   ├── CanvasControls.vue     # Zoom, pan controls
│   │   └── CanvasToolbar.vue      # Canvas-specific toolbar
│   ├── nodes/                      # Node type components
│   │   ├── base/                  # Base node components
│   │   │   ├── BaseNode.vue       # Common node wrapper
│   │   │   ├── NodeHandle.vue     # Connection handles
│   │   │   └── NodeLabel.vue      # Node labeling
│   │   ├── core/                  # Core node types
│   │   │   ├── InputNode.vue      # Data input nodes
│   │   │   ├── LLMNode.vue        # LLM interaction nodes
│   │   │   ├── ConditionalNode.vue # Branching logic
│   │   │   ├── OutputNode.vue     # Result output nodes
│   │   │   └── VectorSearchNode.vue # Vector database nodes
│   │   ├── business/              # Business-specific nodes
│   │   │   ├── ApprovalNode.vue   # Human approval gates
│   │   │   ├── EmailNode.vue      # Email notifications
│   │   │   ├── DataFilterNode.vue # Data filtering
│   │   │   └── TemplateNode.vue   # Template filling
│   │   └── advanced/              # Advanced node types
│   │       ├── LoopNode.vue       # Iteration nodes
│   │       ├── ParallelNode.vue   # Parallel execution
│   │       └── WebhookNode.vue    # External API calls
│   ├── panels/                     # Configuration panels
│   │   ├── NodeConfigPanel.vue    # Node configuration
│   │   ├── FlowSettingsPanel.vue  # Flow-level settings
│   │   ├── TestingPanel.vue       # Testing interface
│   │   ├── TemplatePanel.vue      # Template management
│   │   └── ApprovalPanel.vue      # Approval workflows
│   ├── execution/                  # Execution visualization
│   │   ├── ExecutionVisualization.vue # Real-time execution overlay
│   │   ├── ExecutionTimeline.vue  # Execution history timeline
│   │   ├── StatusIndicators.vue   # Node status indicators
│   │   ├── DebugConsole.vue       # Debug information
│   │   └── PerformanceMetrics.vue # Performance monitoring
│   ├── templates/                  # Template system
│   │   ├── TemplateLibrary.vue    # Template browser
│   │   ├── TemplateCard.vue       # Individual template cards
│   │   ├── TemplateWizard.vue     # Template creation wizard
│   │   ├── PersonaFilter.vue      # Persona-based filtering
│   │   └── TemplatePreview.vue    # Template preview modal
│   ├── layout/                     # Layout components
│   │   ├── FlowLayout.vue         # Main layout wrapper
│   │   ├── FlowSidebar.vue        # Collapsible sidebar
│   │   ├── FlowHeader.vue         # Header with navigation
│   │   ├── FlowFooter.vue         # Status and info footer
│   │   └── FlowBreadcrumb.vue     # Navigation breadcrumbs
│   ├── ui/                         # Shared UI components
│   │   ├── FlowModal.vue          # Flow-specific modals
│   │   ├── FlowToast.vue          # Notification toasts
│   │   ├── FlowLoading.vue        # Loading states
│   │   ├── FlowErrorBoundary.vue  # Error boundaries
│   │   ├── FlowTooltip.vue        # Contextual tooltips
│   │   └── FlowProgressBar.vue    # Progress indicators
│   └── forms/                      # Form components
│       ├── NodeConfigForm.vue     # Node configuration forms
│       ├── FlowMetadataForm.vue   # Flow metadata forms
│       ├── TemplateForm.vue       # Template creation forms
│       └── ValidationMessage.vue  # Form validation messages
├── composables/                    # Vue composables
│   ├── canvas/                     # Canvas-related composables
│   │   ├── useFlowCanvas.ts       # Canvas state management
│   │   ├── useCanvasInteractions.ts # Mouse/touch interactions
│   │   ├── useCanvasPerformance.ts # Performance optimization
│   │   └── useCanvasAccessibility.ts # Accessibility features
│   ├── nodes/                      # Node-related composables
│   │   ├── useNodeManagement.ts   # Node CRUD operations
│   │   ├── useNodeValidation.ts   # Node validation logic
│   │   ├── useNodeConnections.ts  # Node connection handling
│   │   └── useNodeTypes.ts        # Node type definitions
│   ├── execution/                  # Execution composables
│   │   ├── useFlowExecution.ts    # Execution management
│   │   ├── useExecutionVisualization.ts # Real-time visualization
│   │   ├── useFlowTesting.ts      # Testing capabilities
│   │   └── useExecutionHistory.ts # Execution history
│   ├── templates/                  # Template composables
│   │   ├── useFlowTemplates.ts    # Template management
│   │   ├── useTemplateFiltering.ts # Template filtering/search
│   │   └── useTemplateInstantiation.ts # Template creation
│   ├── ui/                         # UI-related composables
│   │   ├── useFlowModals.ts       # Modal management
│   │   ├── useFlowToasts.ts       # Toast notifications
│   │   ├── useFlowTheme.ts        # Theme management
│   │   └── useFlowAccessibility.ts # Accessibility utilities
│   └── integration/                # Integration composables
│       ├── useWebSocketConnection.ts # WebSocket management
│       ├── useRealtimeUpdates.ts  # Real-time data updates
│       └── useModuleIntegration.ts # Cross-module integration
├── stores/                         # Pinia stores
│   ├── flowCanvas.ts              # Canvas state store
│   ├── flowExecution.ts           # Execution state store
│   ├── flowTemplates.ts           # Template state store
│   ├── flowUI.ts                  # UI state store
│   └── flowSettings.ts            # User settings store
├── types/                          # TypeScript definitions
│   ├── canvas.ts                  # Canvas-related types
│   ├── nodes.ts                   # Node type definitions
│   ├── execution.ts               # Execution types
│   ├── templates.ts               # Template types
│   ├── ui.ts                      # UI component types
│   └── events.ts                  # Event types
├── utils/                          # Utility functions
│   ├── canvas/                     # Canvas utilities
│   │   ├── coordinates.ts         # Coordinate calculations
│   │   ├── geometry.ts            # Geometric operations
│   │   └── performance.ts         # Performance utilities
│   ├── validation/                 # Validation utilities
│   │   ├── nodeValidation.ts      # Node validation rules
│   │   ├── flowValidation.ts      # Flow validation rules
│   │   └── formValidation.ts      # Form validation helpers
│   ├── accessibility/              # Accessibility utilities
│   │   ├── keyboardNavigation.ts  # Keyboard navigation
│   │   ├── screenReader.ts        # Screen reader support
│   │   └── focusManagement.ts     # Focus management
│   └── formatting/                 # Formatting utilities
│       ├── dateTime.ts            # Date/time formatting
│       ├── numbers.ts             # Number formatting
│       └── text.ts                # Text formatting
├── assets/                         # Static assets
│   ├── icons/                     # Flow-specific icons
│   │   ├── nodes/                 # Node type icons
│   │   ├── actions/               # Action icons
│   │   └── status/                # Status icons
│   ├── images/                    # Images and illustrations
│   │   ├── templates/             # Template preview images
│   │   ├── onboarding/            # Onboarding illustrations
│   │   └── empty-states/          # Empty state illustrations
│   └── animations/                # Animation assets
│       ├── loading/               # Loading animations
│       └── transitions/           # Transition animations
└── styles/                         # Styling files
    ├── components/                 # Component-specific styles
    ├── utilities/                  # Utility classes
    ├── themes/                     # Theme definitions
    └── animations.css              # Animation definitions

## Component Strategy & Design System Integration

### 1. Shuriken UI Integration Strategy

#### **Design System Consistency**
```typescript
// Component inheritance pattern
interface FlowComponent extends ShurikenComponent {
  // Flow-specific enhancements while maintaining base design
  flowContext?: 'canvas' | 'panel' | 'modal' | 'toolbar'
  executionState?: 'idle' | 'running' | 'completed' | 'error'
  accessibilityEnhanced?: boolean
}

// Example: Enhanced Button Component
<BaseButton
  :variant="flowContext === 'canvas' ? 'ghost' : 'solid'"
  :size="isTablet ? 'lg' : 'md'"
  :disabled="!canExecuteAction"
  :loading="isProcessing"
  @click="handleFlowAction"
>
  <FlowIcon :name="actionIcon" />
  {{ actionLabel }}
</BaseButton>
```

#### **Color Palette Extensions**
```scss
// Flow-specific color extensions to Shuriken UI
:root {
  // Execution states
  --flow-executing: #3b82f6;
  --flow-completed: #10b981;
  --flow-error: #ef4444;
  --flow-pending: #6b7280;
  --flow-paused: #f59e0b;

  // Node type colors
  --node-input: #8b5cf6;
  --node-llm: #06b6d4;
  --node-conditional: #f59e0b;
  --node-output: #10b981;
  --node-business: #ec4899;

  // Canvas colors
  --canvas-background: #fafafa;
  --canvas-grid: #e5e7eb;
  --canvas-selection: #3b82f6;
  --canvas-connection: #6b7280;
}
```

### 2. Component Specification Template

#### **Standard Component Structure**
```vue
<template>
  <div
    :class="componentClasses"
    :aria-label="accessibilityLabel"
    :role="semanticRole"
    @keydown="handleKeyboardNavigation"
  >
    <!-- Component content -->
    <slot />

    <!-- Loading state -->
    <FlowLoading v-if="isLoading" :size="loadingSize" />

    <!-- Error state -->
    <FlowErrorBoundary v-if="hasError" :error="error" />
  </div>
</template>

<script setup lang="ts">
interface Props {
  // Required props
  id: string

  // Optional props with defaults
  variant?: ComponentVariant
  size?: ComponentSize
  disabled?: boolean
  loading?: boolean

  // Flow-specific props
  flowContext?: FlowContext
  executionState?: ExecutionState

  // Accessibility props
  ariaLabel?: string
  ariaDescribedBy?: string
}

interface Emits {
  // Standard events
  click: [event: MouseEvent]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]

  // Flow-specific events
  flowAction: [action: FlowAction]
  stateChange: [state: ComponentState]
}

// Component logic with composables
const { componentClasses, handleKeyboardNavigation } = useFlowComponent(props)
const { accessibilityLabel, semanticRole } = useFlowAccessibility(props)
const { isLoading, hasError, error } = useComponentState(props)
</script>
```

### 3. Node Component Architecture

#### **Base Node Component**
```vue
<!-- BaseNode.vue - Foundation for all node types -->
<template>
  <div
    :class="nodeClasses"
    :style="nodeStyles"
    :data-node-id="nodeId"
    :data-node-type="nodeType"
    :aria-label="`${nodeType} node: ${nodeLabel}`"
    role="button"
    tabindex="0"
    @click="handleNodeClick"
    @keydown="handleNodeKeyboard"
    @mouseenter="handleNodeHover"
    @mouseleave="handleNodeLeave"
  >
    <!-- Node Header -->
    <div class="node-header">
      <FlowIcon :name="nodeIcon" :class="iconClasses" />
      <span class="node-title">{{ nodeLabel }}</span>
      <NodeStatusIndicator :status="executionStatus" />
    </div>

    <!-- Node Content -->
    <div class="node-content">
      <slot name="content" />
    </div>

    <!-- Connection Handles -->
    <NodeHandle
      v-for="handle in inputHandles"
      :key="handle.id"
      :handle="handle"
      type="input"
      :position="handle.position"
    />
    <NodeHandle
      v-for="handle in outputHandles"
      :key="handle.id"
      :handle="handle"
      type="output"
      :position="handle.position"
    />

    <!-- Execution Overlay -->
    <ExecutionOverlay
      v-if="showExecutionState"
      :status="executionStatus"
      :progress="executionProgress"
      :error="executionError"
    />
  </div>
</template>

<script setup lang="ts">
interface NodeProps {
  nodeId: string
  nodeType: NodeType
  nodeLabel: string
  nodeData: NodeData
  position: Position
  selected?: boolean
  dragging?: boolean
  executionStatus?: ExecutionStatus
  executionProgress?: number
  executionError?: string
}

// Node-specific composables
const { nodeClasses, nodeStyles, nodeIcon } = useNodeAppearance(props)
const { inputHandles, outputHandles } = useNodeConnections(props)
const { handleNodeClick, handleNodeKeyboard } = useNodeInteractions(props)
const { showExecutionState, iconClasses } = useNodeExecution(props)
</script>
```

## State Management Architecture

### 1. Pinia Store Structure

#### **Flow Canvas Store**
```typescript
// stores/flowCanvas.ts
export const useFlowCanvasStore = defineStore('flowCanvas', () => {
  // Canvas state
  const viewport = ref<Viewport>({
    x: 0,
    y: 0,
    zoom: 1
  })

  const nodes = ref<FlowNode[]>([])
  const edges = ref<FlowEdge[]>([])
  const selectedNodes = ref<string[]>([])
  const selectedEdges = ref<string[]>([])

  // Canvas settings
  const canvasSettings = ref<CanvasSettings>({
    snapToGrid: true,
    gridSize: 20,
    showGrid: true,
    showMinimap: true,
    panOnDrag: true,
    zoomOnScroll: true
  })

  // Performance state
  const performanceMode = ref<'normal' | 'optimized' | 'minimal'>('normal')
  const visibleNodes = computed(() => {
    if (performanceMode.value === 'minimal') {
      return getVisibleNodesInViewport(nodes.value, viewport.value)
    }
    return nodes.value
  })

  // Actions
  const addNode = (node: FlowNode) => {
    nodes.value.push(node)
    trackCanvasAction('node_added', { nodeType: node.type })
  }

  const updateNode = (nodeId: string, updates: Partial<FlowNode>) => {
    const index = nodes.value.findIndex(n => n.id === nodeId)
    if (index !== -1) {
      nodes.value[index] = { ...nodes.value[index], ...updates }
      trackCanvasAction('node_updated', { nodeId, updates })
    }
  }

  const deleteNode = (nodeId: string) => {
    nodes.value = nodes.value.filter(n => n.id !== nodeId)
    edges.value = edges.value.filter(e => e.source !== nodeId && e.target !== nodeId)
    trackCanvasAction('node_deleted', { nodeId })
  }

  const addEdge = (edge: FlowEdge) => {
    if (validateConnection(edge, nodes.value)) {
      edges.value.push(edge)
      trackCanvasAction('edge_added', { edge })
    }
  }

  const updateViewport = (newViewport: Partial<Viewport>) => {
    viewport.value = { ...viewport.value, ...newViewport }
  }

  const selectNodes = (nodeIds: string[]) => {
    selectedNodes.value = nodeIds
    trackCanvasAction('nodes_selected', { nodeIds })
  }

  // Undo/Redo functionality
  const { undo, redo, canUndo, canRedo } = useCanvasHistory({
    nodes,
    edges,
    viewport
  })

  return {
    // State
    viewport: readonly(viewport),
    nodes: readonly(nodes),
    edges: readonly(edges),
    selectedNodes: readonly(selectedNodes),
    selectedEdges: readonly(selectedEdges),
    canvasSettings: readonly(canvasSettings),
    performanceMode: readonly(performanceMode),
    visibleNodes,

    // Actions
    addNode,
    updateNode,
    deleteNode,
    addEdge,
    updateViewport,
    selectNodes,

    // History
    undo,
    redo,
    canUndo,
    canRedo
  }
})
```

#### **Flow Execution Store**
```typescript
// stores/flowExecution.ts
export const useFlowExecutionStore = defineStore('flowExecution', () => {
  // Execution state
  const currentExecution = ref<FlowExecution | null>(null)
  const executionHistory = ref<FlowExecution[]>([])
  const nodeExecutionStates = ref<Map<string, NodeExecutionState>>(new Map())

  // Real-time updates
  const isConnected = ref(false)
  const lastUpdate = ref<Date | null>(null)
  const executionEvents = ref<ExecutionEvent[]>([])

  // Testing state
  const testMode = ref(false)
  const testInputs = ref<Record<string, any>>({})
  const breakpoints = ref<string[]>([])

  // WebSocket connection
  const { connect, disconnect, send } = useWebSocketConnection('/api/flow/execution/stream')

  // Actions
  const startExecution = async (flowId: string, inputs: Record<string, any>) => {
    try {
      const execution = await $fetch('/api/flow/execute', {
        method: 'POST',
        body: { flowId, inputs, mode: testMode.value ? 'test' : 'production' }
      })

      currentExecution.value = execution
      nodeExecutionStates.value.clear()

      // Connect to real-time updates
      connect()
      subscribeToExecution(execution.id)

      return execution
    } catch (error) {
      handleExecutionError(error)
      throw error
    }
  }

  const pauseExecution = async () => {
    if (currentExecution.value) {
      await $fetch(`/api/flow/execution/${currentExecution.value.id}/pause`, {
        method: 'POST'
      })
    }
  }

  const resumeExecution = async () => {
    if (currentExecution.value) {
      await $fetch(`/api/flow/execution/${currentExecution.value.id}/resume`, {
        method: 'POST'
      })
    }
  }

  const stopExecution = async () => {
    if (currentExecution.value) {
      await $fetch(`/api/flow/execution/${currentExecution.value.id}/stop`, {
        method: 'POST'
      })
      disconnect()
    }
  }

  // Real-time event handling
  const handleExecutionEvent = (event: ExecutionEvent) => {
    executionEvents.value.unshift(event)
    lastUpdate.value = new Date()

    switch (event.type) {
      case 'node_started':
        updateNodeState(event.nodeId, { status: 'running', startedAt: event.timestamp })
        break
      case 'node_completed':
        updateNodeState(event.nodeId, {
          status: 'completed',
          completedAt: event.timestamp,
          output: event.output,
          executionTime: event.executionTime
        })
        break
      case 'node_failed':
        updateNodeState(event.nodeId, {
          status: 'failed',
          error: event.error,
          completedAt: event.timestamp
        })
        break
      case 'execution_completed':
        currentExecution.value = { ...currentExecution.value!, status: 'completed' }
        executionHistory.value.unshift(currentExecution.value!)
        break
    }
  }

  const updateNodeState = (nodeId: string, state: Partial<NodeExecutionState>) => {
    const currentState = nodeExecutionStates.value.get(nodeId) || {}
    nodeExecutionStates.value.set(nodeId, { ...currentState, ...state })
  }

  return {
    // State
    currentExecution: readonly(currentExecution),
    executionHistory: readonly(executionHistory),
    nodeExecutionStates: readonly(nodeExecutionStates),
    isConnected: readonly(isConnected),
    lastUpdate: readonly(lastUpdate),
    executionEvents: readonly(executionEvents),
    testMode,
    testInputs,
    breakpoints,

    // Actions
    startExecution,
    pauseExecution,
    resumeExecution,
    stopExecution,
    handleExecutionEvent,
    updateNodeState
  }
})
```

## Real-Time Execution Visualization

### 1. WebSocket Integration Architecture

#### **WebSocket Connection Management**
```typescript
// composables/useWebSocketConnection.ts
export const useWebSocketConnection = (endpoint: string) => {
  const socket = ref<WebSocket | null>(null)
  const isConnected = ref(false)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5
  const reconnectDelay = ref(1000)

  const connect = () => {
    try {
      const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}${endpoint}`
      socket.value = new WebSocket(wsUrl)

      socket.value.onopen = () => {
        isConnected.value = true
        reconnectAttempts.value = 0
        reconnectDelay.value = 1000
        console.log('WebSocket connected')
      }

      socket.value.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          handleMessage(data)
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }

      socket.value.onclose = () => {
        isConnected.value = false
        console.log('WebSocket disconnected')

        // Attempt reconnection
        if (reconnectAttempts.value < maxReconnectAttempts) {
          setTimeout(() => {
            reconnectAttempts.value++
            reconnectDelay.value *= 2
            connect()
          }, reconnectDelay.value)
        }
      }

      socket.value.onerror = (error) => {
        console.error('WebSocket error:', error)
      }

    } catch (error) {
      console.error('Failed to connect WebSocket:', error)
    }
  }

  const disconnect = () => {
    if (socket.value) {
      socket.value.close()
      socket.value = null
    }
  }

  const send = (data: any) => {
    if (socket.value && isConnected.value) {
      socket.value.send(JSON.stringify(data))
    }
  }

  const handleMessage = (data: any) => {
    // Emit to event bus for components to handle
    const { emit } = useEventBus()
    emit('websocket:message', data)
  }

  return {
    isConnected: readonly(isConnected),
    connect,
    disconnect,
    send
  }
}
```

### 2. Execution Visualization Components

#### **Real-Time Status Indicators**
```vue
<!-- StatusIndicators.vue -->
<template>
  <div class="execution-status-overlay">
    <!-- Node status indicators -->
    <div
      v-for="(state, nodeId) in nodeStates"
      :key="nodeId"
      :class="getStatusIndicatorClasses(state.status)"
      :style="getStatusIndicatorPosition(nodeId)"
      :aria-label="`Node ${nodeId} status: ${state.status}`"
    >
      <!-- Status icon -->
      <FlowIcon
        :name="getStatusIcon(state.status)"
        :class="getStatusIconClasses(state.status)"
      />

      <!-- Progress indicator for running nodes -->
      <div
        v-if="state.status === 'running' && state.progress"
        class="progress-ring"
      >
        <svg class="progress-ring-svg" width="24" height="24">
          <circle
            class="progress-ring-circle"
            :stroke-dasharray="`${state.progress * 75.4} 75.4`"
            cx="12"
            cy="12"
            r="12"
          />
        </svg>
      </div>

      <!-- Execution time -->
      <div
        v-if="state.executionTime"
        class="execution-time"
      >
        {{ formatExecutionTime(state.executionTime) }}
      </div>
    </div>

    <!-- Connection flow indicators -->
    <div
      v-for="edge in activeEdges"
      :key="edge.id"
      :class="getEdgeFlowClasses(edge)"
      :style="getEdgeFlowStyles(edge)"
    >
      <div class="flow-particle" />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  nodeStates: Map<string, NodeExecutionState>
  activeEdges: FlowEdge[]
  canvasViewport: Viewport
}

const { getNodePosition } = useFlowCanvas()
const { formatExecutionTime } = useTimeFormatting()

const getStatusIndicatorPosition = (nodeId: string) => {
  const nodePosition = getNodePosition(nodeId)
  if (!nodePosition) return {}

  return {
    left: `${nodePosition.x + 150}px`, // Offset from node
    top: `${nodePosition.y - 10}px`,
    transform: `scale(${Math.max(0.5, props.canvasViewport.zoom)})`
  }
}

const getStatusIndicatorClasses = (status: ExecutionStatus) => [
  'status-indicator',
  `status-${status}`,
  {
    'animate-pulse': status === 'running',
    'animate-bounce': status === 'completed'
  }
]

const getStatusIcon = (status: ExecutionStatus) => {
  const icons = {
    pending: 'clock',
    running: 'play',
    completed: 'check',
    failed: 'x',
    paused: 'pause'
  }
  return icons[status] || 'help'
}
</script>

<style scoped>
.execution-status-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.status-indicator {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.status-pending {
  border: 1px solid var(--flow-pending);
  color: var(--flow-pending);
}

.status-running {
  border: 1px solid var(--flow-executing);
  color: var(--flow-executing);
  background: rgba(59, 130, 246, 0.1);
}

.status-completed {
  border: 1px solid var(--flow-completed);
  color: var(--flow-completed);
  background: rgba(16, 185, 129, 0.1);
}

.status-failed {
  border: 1px solid var(--flow-error);
  color: var(--flow-error);
  background: rgba(239, 68, 68, 0.1);
}

.progress-ring-svg {
  transform: rotate(-90deg);
}

.progress-ring-circle {
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  transition: stroke-dasharray 0.3s ease;
}

.flow-particle {
  width: 6px;
  height: 6px;
  background: var(--flow-executing);
  border-radius: 50%;
  animation: flowParticle 2s linear infinite;
}

@keyframes flowParticle {
  0% { transform: translateX(0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateX(100%); opacity: 0; }
}
</style>
```

## Persona-Specific User Experience Flows

### 1. Business Analyst Experience Flow

#### **Workflow Creation Journey**
```mermaid
graph TD
    A[Landing Page] --> B{First Time User?}
    B -->|Yes| C[Onboarding Tutorial]
    B -->|No| D[Template Library]

    C --> E[Template Selection Guide]
    E --> F[Business Analyst Templates]

    D --> F
    F --> G[Template Preview]
    G --> H{Use Template?}

    H -->|Yes| I[Template Configuration]
    H -->|No| J[Blank Canvas]

    I --> K[Input Data Sources]
    K --> L[Configure Analysis Parameters]
    L --> M[Preview Workflow]

    J --> N[Node Library]
    N --> O[Drag & Drop Nodes]
    O --> P[Connect Nodes]
    P --> Q[Configure Node Settings]

    M --> R[Test Workflow]
    Q --> R
    R --> S{Test Successful?}

    S -->|No| T[Debug & Fix Issues]
    T --> R

    S -->|Yes| U[Save Workflow]
    U --> V[Schedule/Execute]
    V --> W[Monitor Results]
    W --> X[Export/Share Results]
```

#### **Business Analyst UI Optimizations**
```vue
<!-- BusinessAnalystDashboard.vue -->
<template>
  <div class="ba-dashboard">
    <!-- Quick Actions Bar -->
    <div class="quick-actions">
      <BaseButton
        variant="primary"
        size="lg"
        @click="createFromTemplate('customer_segmentation')"
      >
        <FlowIcon name="users" />
        Customer Segmentation
      </BaseButton>

      <BaseButton
        variant="secondary"
        size="lg"
        @click="createFromTemplate('reporting_pipeline')"
      >
        <FlowIcon name="chart-bar" />
        Automated Reporting
      </BaseButton>

      <BaseButton
        variant="secondary"
        size="lg"
        @click="createFromTemplate('data_quality')"
      >
        <FlowIcon name="shield-check" />
        Data Quality Check
      </BaseButton>
    </div>

    <!-- Recent Workflows -->
    <div class="recent-workflows">
      <h3>Recent Analysis Workflows</h3>
      <div class="workflow-grid">
        <WorkflowCard
          v-for="workflow in recentWorkflows"
          :key="workflow.id"
          :workflow="workflow"
          :show-metrics="true"
          @execute="executeWorkflow"
          @edit="editWorkflow"
        />
      </div>
    </div>

    <!-- Data Sources Integration -->
    <div class="data-sources">
      <h3>Connected Data Sources</h3>
      <DataSourceGrid
        :sources="connectedSources"
        :show-health="true"
        @configure="configureDataSource"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
const { recentWorkflows, connectedSources } = useBusinessAnalystData()
const { createFromTemplate, executeWorkflow, editWorkflow } = useWorkflowActions()
const { configureDataSource } = useDataSourceManagement()
</script>
```

### 2. Content Creator Experience Flow

#### **Content Creation Workflow**
```mermaid
graph TD
    A[Content Dashboard] --> B[Content Type Selection]
    B --> C{Content Type}

    C -->|Blog Post| D[Blog Creation Template]
    C -->|Social Media| E[Social Media Template]
    C -->|Email Campaign| F[Email Template]

    D --> G[Topic Research Phase]
    E --> G
    F --> G

    G --> H[AI Content Generation]
    H --> I[Content Review & Edit]
    I --> J{Approve Content?}

    J -->|No| K[Revision Instructions]
    K --> H

    J -->|Yes| L[SEO Optimization]
    L --> M[Multi-Channel Formatting]
    M --> N[Schedule/Publish]
    N --> O[Performance Tracking]
```

#### **Content Creator UI Features**
```vue
<!-- ContentCreatorWorkspace.vue -->
<template>
  <div class="content-workspace">
    <!-- Content Pipeline Visualization -->
    <div class="pipeline-overview">
      <ContentPipelineVisualization
        :stages="contentStages"
        :current-stage="currentStage"
        :progress="overallProgress"
      />
    </div>

    <!-- Content Templates -->
    <div class="content-templates">
      <h3>Content Creation Templates</h3>
      <div class="template-carousel">
        <TemplateCard
          v-for="template in contentTemplates"
          :key="template.id"
          :template="template"
          :preview-mode="true"
          @select="selectTemplate"
        >
          <template #preview>
            <ContentPreview :template="template" />
          </template>
        </TemplateCard>
      </div>
    </div>

    <!-- Active Content Projects -->
    <div class="active-projects">
      <h3>Active Content Projects</h3>
      <ContentProjectGrid
        :projects="activeProjects"
        :show-collaboration="true"
        @open="openProject"
        @collaborate="inviteCollaborator"
      />
    </div>

    <!-- Content Performance Insights -->
    <div class="performance-insights">
      <ContentPerformanceWidget
        :metrics="performanceMetrics"
        :recommendations="aiRecommendations"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
const { contentTemplates, activeProjects } = useContentCreatorData()
const { contentStages, currentStage, overallProgress } = useContentPipeline()
const { performanceMetrics, aiRecommendations } = useContentAnalytics()
</script>
```

### 3. Customer Success Experience Flow

#### **Customer Journey Automation**
```mermaid
graph TD
    A[Customer Success Dashboard] --> B[Customer Health Overview]
    B --> C{Customer Status}

    C -->|New| D[Onboarding Automation]
    C -->|At Risk| E[Retention Workflow]
    C -->|Healthy| F[Upsell Opportunity]

    D --> G[Personalized Welcome Sequence]
    E --> H[Intervention Strategy]
    F --> I[Growth Opportunity Analysis]

    G --> J[Progress Tracking]
    H --> J
    I --> J

    J --> K[Automated Follow-ups]
    K --> L[Success Metrics]
    L --> M[Continuous Optimization]
```

#### **Customer Success UI Components**
```vue
<!-- CustomerSuccessHub.vue -->
<template>
  <div class="cs-hub">
    <!-- Customer Health Dashboard -->
    <div class="health-dashboard">
      <CustomerHealthOverview
        :customers="customerData"
        :health-scores="healthScores"
        :alerts="riskAlerts"
        @view-customer="viewCustomerDetails"
        @create-intervention="createInterventionWorkflow"
      />
    </div>

    <!-- Automation Templates -->
    <div class="automation-templates">
      <h3>Customer Success Automations</h3>
      <div class="template-grid">
        <AutomationTemplate
          v-for="template in csTemplates"
          :key="template.id"
          :template="template"
          :customer-count="getApplicableCustomers(template)"
          @deploy="deployAutomation"
        />
      </div>
    </div>

    <!-- Active Interventions -->
    <div class="active-interventions">
      <h3>Active Customer Interventions</h3>
      <InterventionTimeline
        :interventions="activeInterventions"
        :show-outcomes="true"
        @update="updateIntervention"
        @escalate="escalateIntervention"
      />
    </div>

    <!-- Success Metrics -->
    <div class="success-metrics">
      <SuccessMetricsWidget
        :metrics="successMetrics"
        :trends="metricTrends"
        :goals="teamGoals"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
const { customerData, healthScores, riskAlerts } = useCustomerHealth()
const { csTemplates, activeInterventions } = useCustomerSuccessData()
const { successMetrics, metricTrends, teamGoals } = useSuccessMetrics()
</script>
```

## Responsive Design Architecture

### 1. Breakpoint Strategy

#### **Responsive Breakpoints**
```scss
// Responsive breakpoints optimized for workflow creation
$breakpoints: (
  'mobile': 320px,   // Not supported - too complex for mobile
  'tablet': 768px,   // Simplified interface for review/monitoring
  'desktop': 1024px, // Full workflow creation interface
  'wide': 1440px,    // Optimal workflow creation experience
  'ultra': 1920px    // Multi-monitor workflow development
);

// Responsive mixins
@mixin mobile-only {
  @media (max-width: #{map-get($breakpoints, 'tablet') - 1px}) {
    @content;
  }
}

@mixin tablet-only {
  @media (min-width: #{map-get($breakpoints, 'tablet')}) and (max-width: #{map-get($breakpoints, 'desktop') - 1px}) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: #{map-get($breakpoints, 'desktop')}) {
    @content;
  }
}

@mixin wide-up {
  @media (min-width: #{map-get($breakpoints, 'wide')}) {
    @content;
  }
}
```

#### **Responsive Layout Components**
```vue
<!-- ResponsiveFlowLayout.vue -->
<template>
  <div :class="layoutClasses">
    <!-- Desktop Layout -->
    <template v-if="isDesktop">
      <FlowHeader class="flow-header" />
      <div class="flow-main">
        <FlowSidebar
          :collapsed="sidebarCollapsed"
          @toggle="toggleSidebar"
        />
        <FlowCanvas class="flow-canvas" />
        <FlowPanels
          v-if="showPanels"
          class="flow-panels"
        />
      </div>
      <FlowFooter class="flow-footer" />
    </template>

    <!-- Tablet Layout -->
    <template v-else-if="isTablet">
      <FlowHeader class="flow-header-tablet" />
      <div class="flow-main-tablet">
        <FlowCanvas
          class="flow-canvas-tablet"
          :touch-optimized="true"
        />
        <FlowBottomSheet
          v-if="showBottomSheet"
          :content="bottomSheetContent"
          @close="closeBottomSheet"
        />
      </div>
      <FlowTabBar class="flow-tab-bar" />
    </template>

    <!-- Mobile - Not Supported -->
    <template v-else>
      <MobileNotSupported />
    </template>
  </div>
</template>

<script setup lang="ts">
const { isDesktop, isTablet, isMobile } = useResponsive()
const { sidebarCollapsed, toggleSidebar } = useLayoutState()
const { showPanels, showBottomSheet, bottomSheetContent } = useUIState()

const layoutClasses = computed(() => [
  'flow-layout',
  {
    'layout-desktop': isDesktop.value,
    'layout-tablet': isTablet.value,
    'layout-mobile': isMobile.value,
    'sidebar-collapsed': sidebarCollapsed.value
  }
])
</script>

<style scoped>
.flow-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

/* Desktop Layout */
.layout-desktop {
  .flow-main {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  .flow-canvas {
    flex: 1;
    min-width: 0;
  }

  .flow-panels {
    width: 320px;
    border-left: 1px solid var(--border-color);
  }
}

/* Tablet Layout */
.layout-tablet {
  .flow-main-tablet {
    flex: 1;
    position: relative;
    overflow: hidden;
  }

  .flow-canvas-tablet {
    width: 100%;
    height: 100%;
    touch-action: none;
  }

  .flow-tab-bar {
    height: 60px;
    border-top: 1px solid var(--border-color);
  }
}

/* Responsive sidebar */
@include tablet-only {
  .flow-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 50;
    transform: translateX(-100%);
    transition: transform 0.3s ease;

    &.open {
      transform: translateX(0);
    }
  }
}
</style>
```

### 2. Touch-Optimized Interactions

#### **Touch Gesture Support**
```typescript
// composables/useTouchGestures.ts
export const useTouchGestures = (canvasRef: Ref<HTMLElement | null>) => {
  const { isTablet } = useResponsive()

  // Touch state
  const touchState = ref({
    isTouch: false,
    startPoint: { x: 0, y: 0 },
    currentPoint: { x: 0, y: 0 },
    scale: 1,
    rotation: 0,
    gestureType: null as 'pan' | 'zoom' | 'rotate' | null
  })

  // Touch event handlers
  const handleTouchStart = (event: TouchEvent) => {
    if (!isTablet.value) return

    event.preventDefault()
    touchState.value.isTouch = true

    if (event.touches.length === 1) {
      // Single touch - pan gesture
      const touch = event.touches[0]
      touchState.value.startPoint = { x: touch.clientX, y: touch.clientY }
      touchState.value.gestureType = 'pan'
    } else if (event.touches.length === 2) {
      // Two finger - zoom/rotate gesture
      touchState.value.gestureType = 'zoom'
      handlePinchStart(event)
    }
  }

  const handleTouchMove = (event: TouchEvent) => {
    if (!touchState.value.isTouch) return

    event.preventDefault()

    if (touchState.value.gestureType === 'pan' && event.touches.length === 1) {
      handlePanMove(event)
    } else if (touchState.value.gestureType === 'zoom' && event.touches.length === 2) {
      handlePinchMove(event)
    }
  }

  const handleTouchEnd = (event: TouchEvent) => {
    if (!touchState.value.isTouch) return

    touchState.value.isTouch = false
    touchState.value.gestureType = null

    // Handle tap gestures
    if (event.changedTouches.length === 1) {
      const touch = event.changedTouches[0]
      const deltaX = Math.abs(touch.clientX - touchState.value.startPoint.x)
      const deltaY = Math.abs(touch.clientY - touchState.value.startPoint.y)

      // If movement is minimal, treat as tap
      if (deltaX < 10 && deltaY < 10) {
        handleTap(touch)
      }
    }
  }

  const handlePanMove = (event: TouchEvent) => {
    const touch = event.touches[0]
    const deltaX = touch.clientX - touchState.value.startPoint.x
    const deltaY = touch.clientY - touchState.value.startPoint.y

    // Update canvas viewport
    const { updateViewport } = useFlowCanvas()
    updateViewport({
      x: deltaX,
      y: deltaY
    })
  }

  const handlePinchStart = (event: TouchEvent) => {
    const touch1 = event.touches[0]
    const touch2 = event.touches[1]

    const distance = Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
      Math.pow(touch2.clientY - touch1.clientY, 2)
    )

    touchState.value.scale = distance
  }

  const handlePinchMove = (event: TouchEvent) => {
    const touch1 = event.touches[0]
    const touch2 = event.touches[1]

    const distance = Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
      Math.pow(touch2.clientY - touch1.clientY, 2)
    )

    const scale = distance / touchState.value.scale

    // Update canvas zoom
    const { updateViewport } = useFlowCanvas()
    updateViewport({
      zoom: Math.max(0.1, Math.min(3, scale))
    })
  }

  const handleTap = (touch: Touch) => {
    // Handle node selection, menu opening, etc.
    const element = document.elementFromPoint(touch.clientX, touch.clientY)

    if (element?.closest('.flow-node')) {
      // Node tap
      const nodeId = element.closest('.flow-node')?.getAttribute('data-node-id')
      if (nodeId) {
        const { selectNode } = useFlowCanvas()
        selectNode(nodeId)
      }
    }
  }

  // Setup touch event listeners
  onMounted(() => {
    if (canvasRef.value && isTablet.value) {
      canvasRef.value.addEventListener('touchstart', handleTouchStart, { passive: false })
      canvasRef.value.addEventListener('touchmove', handleTouchMove, { passive: false })
      canvasRef.value.addEventListener('touchend', handleTouchEnd, { passive: false })
    }
  })

  onUnmounted(() => {
    if (canvasRef.value) {
      canvasRef.value.removeEventListener('touchstart', handleTouchStart)
      canvasRef.value.removeEventListener('touchmove', handleTouchMove)
      canvasRef.value.removeEventListener('touchend', handleTouchEnd)
    }
  })

  return {
    touchState: readonly(touchState)
  }
}
```

## Testing Interface Design for Business Users

### 1. User-Friendly Testing Dashboard

#### **Simplified Testing Interface**
```vue
<!-- BusinessUserTestingPanel.vue -->
<template>
  <div class="testing-panel">
    <!-- Testing Header -->
    <div class="testing-header">
      <h3>Test Your Workflow</h3>
      <p class="testing-description">
        Run your workflow safely with test data to make sure everything works correctly.
      </p>
    </div>

    <!-- Test Configuration -->
    <div class="test-config">
      <h4>Test Setup</h4>

      <!-- Test Data Input -->
      <div class="test-inputs">
        <label class="input-label">Test Data</label>
        <p class="input-description">
          Provide sample data to test your workflow with
        </p>

        <div class="input-grid">
          <div
            v-for="input in requiredInputs"
            :key="input.name"
            class="input-field"
          >
            <label :for="input.name">{{ input.label }}</label>
            <component
              :is="getInputComponent(input.type)"
              :id="input.name"
              v-model="testData[input.name]"
              :placeholder="input.placeholder"
              :required="input.required"
              @input="validateInput(input.name)"
            />
            <div
              v-if="inputErrors[input.name]"
              class="input-error"
            >
              {{ inputErrors[input.name] }}
            </div>
          </div>
        </div>
      </div>

      <!-- Test Options -->
      <div class="test-options">
        <label class="option-label">
          <input
            v-model="stepByStepMode"
            type="checkbox"
          />
          <span>Step-by-step mode (pause at each step)</span>
        </label>

        <label class="option-label">
          <input
            v-model="detailedLogging"
            type="checkbox"
          />
          <span>Show detailed information</span>
        </label>
      </div>
    </div>

    <!-- Test Actions -->
    <div class="test-actions">
      <BaseButton
        variant="primary"
        size="lg"
        :disabled="!canRunTest"
        :loading="isRunningTest"
        @click="startTest"
      >
        <FlowIcon name="play" />
        Run Test
      </BaseButton>

      <BaseButton
        v-if="isRunningTest"
        variant="secondary"
        @click="pauseTest"
      >
        <FlowIcon name="pause" />
        Pause
      </BaseButton>

      <BaseButton
        v-if="testPaused"
        variant="secondary"
        @click="resumeTest"
      >
        <FlowIcon name="play" />
        Continue
      </BaseButton>

      <BaseButton
        v-if="isRunningTest || testPaused"
        variant="danger"
        @click="stopTest"
      >
        <FlowIcon name="stop" />
        Stop Test
      </BaseButton>
    </div>

    <!-- Test Progress -->
    <div
      v-if="testExecution"
      class="test-progress"
    >
      <TestProgressVisualization
        :execution="testExecution"
        :step-by-step="stepByStepMode"
        :detailed="detailedLogging"
        @step-forward="stepForward"
        @view-details="viewStepDetails"
      />
    </div>

    <!-- Test Results -->
    <div
      v-if="testResults"
      class="test-results"
    >
      <TestResultsSummary
        :results="testResults"
        :recommendations="testRecommendations"
        @export="exportResults"
        @save-workflow="saveWorkflow"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  flowId: string
  requiredInputs: TestInput[]
}

const testData = ref<Record<string, any>>({})
const inputErrors = ref<Record<string, string>>({})
const stepByStepMode = ref(true)
const detailedLogging = ref(false)

const {
  isRunningTest,
  testPaused,
  testExecution,
  testResults,
  testRecommendations,
  startTest: startFlowTest,
  pauseTest,
  resumeTest,
  stopTest,
  stepForward
} = useFlowTesting()

const canRunTest = computed(() => {
  return Object.keys(inputErrors.value).length === 0 &&
         props.requiredInputs.every(input =>
           input.required ? testData.value[input.name] : true
         )
})

const startTest = async () => {
  try {
    await startFlowTest(props.flowId, {
      inputs: testData.value,
      stepByStep: stepByStepMode.value,
      detailed: detailedLogging.value
    })
  } catch (error) {
    // Handle test start error
    console.error('Failed to start test:', error)
  }
}

const validateInput = (inputName: string) => {
  const input = props.requiredInputs.find(i => i.name === inputName)
  const value = testData.value[inputName]

  if (input?.required && !value) {
    inputErrors.value[inputName] = `${input.label} is required`
  } else if (input?.validation && !input.validation(value)) {
    inputErrors.value[inputName] = input.validationMessage || 'Invalid value'
  } else {
    delete inputErrors.value[inputName]
  }
}

const getInputComponent = (type: string) => {
  const components = {
    text: 'BaseInput',
    number: 'BaseNumberInput',
    select: 'BaseSelect',
    textarea: 'BaseTextarea',
    file: 'BaseFileInput'
  }
  return components[type] || 'BaseInput'
}
</script>

<style scoped>
.testing-panel {
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.testing-header {
  margin-bottom: 24px;
}

.testing-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.testing-description {
  color: var(--text-secondary);
  font-size: 14px;
}

.test-config {
  margin-bottom: 24px;
}

.test-config h4 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

.input-grid {
  display: grid;
  gap: 16px;
  margin-bottom: 16px;
}

.input-field {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.input-field label {
  font-weight: 500;
  color: var(--text-primary);
}

.input-error {
  color: var(--color-error);
  font-size: 12px;
}

.test-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  cursor: pointer;
}

.test-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.test-progress,
.test-results {
  border-top: 1px solid var(--border-color);
  padding-top: 24px;
}
</style>
```
```
```