# API Development Guide - Flow Module

**Target Audience:** Backend Developers, Full-Stack Developers  
**Prerequisites:** Nuxt 3, TypeScript, Firebase, LangGraph  
**Estimated Time:** 3-5 hours setup, ongoing development

## Overview

This guide provides comprehensive instructions for implementing the Flow module's server-side API endpoints, including flow execution, template management, and real-time communication.

## Prerequisites

### Required Knowledge
- Nuxt 3 server API patterns
- TypeScript strict mode
- Firebase Firestore operations
- LangGraph workflow execution
- WebSocket communication

### Environment Setup
```bash
# Install dependencies
pnpm --filter=flow install

# Environment variables
FIREBASE_PROJECT_ID=partners-in-biz-85059
LANGRAPH_API_KEY=your_langraph_key
ENCRYPTION_KEY=your_encryption_key
```

## API Architecture Overview

### Endpoint Structure
```
/api/
├── data/                    # CRUD operations via useDataApi
│   ├── flows/              # Flow graph management
│   ├── flow_templates/     # Template management
│   ├── flow_executions/    # Execution history
│   └── flow_api_keys/      # Encrypted API keys
├── flow/                   # Flow-specific operations
│   ├── execute/[id].post.ts    # Execute flow
│   ├── test/start.post.ts      # Start test execution
│   ├── ai/generate.post.ts     # AI-assisted generation
│   └── keys/encrypt.post.ts    # API key encryption
└── websocket/              # Real-time communication
    └── flow-execution.ts   # Execution updates
```

## Core API Implementation

### 1. Flow Execution Endpoint

```typescript
// server/api/flow/execute/[id].post.ts
export default defineEventHandler(async (event) => {
  try {
    // Get user session and validate
    const session = await getUserSession(event)
    if (!session.valid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized'
      })
    }

    // Get flow ID and request body
    const flowId = getRouterParam(event, 'id')
    const { inputs, mode = 'production', testConfig } = await readBody(event)

    // Validate flow access
    const flow = await validateFlowAccess(flowId, session.workspace_id, session.user_id)
    if (!flow) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Flow not found or access denied'
      })
    }

    // Create execution record
    const execution = await createExecution({
      flow_id: flowId,
      workspace_id: session.workspace_id,
      user_id: session.user_id,
      mode,
      input_data: inputs,
      test_config: testConfig,
      status: 'pending',
      created_at: new Date().toISOString()
    })

    // Start async execution
    await startFlowExecution(execution, flow)

    return {
      success: true,
      execution_id: execution.id,
      status: execution.status
    }

  } catch (error) {
    console.error('Flow execution error:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to execute flow'
    })
  }
})

// Helper functions
async function validateFlowAccess(flowId: string, workspaceId: string, userId: string) {
  const { useEntityApi } = useDataApi()
  const flowsApi = useEntityApi('flows')
  
  const flow = await flowsApi.getById(flowId)
  
  if (!flow || flow.workspace_id !== workspaceId) {
    return null
  }
  
  return flow
}

async function createExecution(executionData: Partial<FlowExecution>) {
  const { useEntityApi } = useDataApi()
  const executionsApi = useEntityApi('flow_executions')
  
  return await executionsApi.create({
    id: generateId(),
    ...executionData
  })
}
```

### 2. LangGraph Integration

```typescript
// server/utils/FlowLangGraphExecutor.ts
export class FlowLangGraphExecutor {
  private flow: FlowGraph
  private execution: FlowExecution
  private securityContext: FlowSecurityContext
  private wsConnections: Set<WebSocket> = new Set()

  constructor(config: ExecutorConfig) {
    this.flow = config.flow
    this.execution = config.execution
    this.securityContext = config.securityContext
  }

  async execute() {
    try {
      // Update execution status
      await this.updateExecutionStatus('running')
      
      // Build LangGraph from flow definition
      const langGraph = await this.buildLangGraph()
      
      // Execute with real-time updates
      const result = await this.executeWithTracking(langGraph)
      
      await this.updateExecutionStatus('completed', { output_data: result })
      
    } catch (error) {
      await this.updateExecutionStatus('failed', { error_message: error.message })
      this.broadcastUpdate({ type: 'execution_failed', error: error.message })
    }
  }

  private async buildLangGraph() {
    const { StateGraph } = await import('@langchain/langgraph')
    
    const workflow = new StateGraph({
      channels: this.getStateChannels()
    })

    // Add nodes from flow definition
    for (const node of this.flow.nodes) {
      const nodeFunction = await this.createNodeFunction(node)
      workflow.addNode(node.id, nodeFunction)
    }

    // Add edges from flow definition
    for (const edge of this.flow.edges) {
      workflow.addEdge(edge.source, edge.target)
    }

    return workflow.compile()
  }

  private async createNodeFunction(node: FlowNode) {
    switch (node.type) {
      case 'llm_prompt':
        return this.createLLMNode(node)
      case 'vector_search':
        return this.createVectorSearchNode(node)
      case 'conditional':
        return this.createConditionalNode(node)
      default:
        throw new Error(`Unknown node type: ${node.type}`)
    }
  }

  private createLLMNode(node: FlowNode) {
    return async (state: any) => {
      // Broadcast node start
      this.broadcastUpdate({
        type: 'node_started',
        nodeId: node.id,
        timestamp: new Date().toISOString()
      })

      try {
        // Get API key securely
        const apiKey = await this.getDecryptedApiKey(node.data.api_key_id)
        
        // Execute LLM call
        const result = await this.executeLLMCall(node.data, state, apiKey)
        
        // Broadcast completion
        this.broadcastUpdate({
          type: 'node_completed',
          nodeId: node.id,
          output: result,
          timestamp: new Date().toISOString()
        })

        return { [node.data.output_field]: result }
        
      } catch (error) {
        this.broadcastUpdate({
          type: 'node_failed',
          nodeId: node.id,
          error: error.message,
          timestamp: new Date().toISOString()
        })
        throw error
      }
    }
  }

  private async getDecryptedApiKey(keyId: string) {
    const { useEntityApi } = useDataApi()
    const keysApi = useEntityApi('flow_api_keys')
    
    const keyRecord = await keysApi.getById(keyId)
    if (!keyRecord || keyRecord.workspace_id !== this.securityContext.workspace_id) {
      throw new Error('API key not found or access denied')
    }
    
    return await decryptApiKey(keyRecord)
  }

  private broadcastUpdate(update: any) {
    const message = JSON.stringify(update)
    this.wsConnections.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(message)
      }
    })
  }
}
```

### 3. API Key Encryption

```typescript
// server/api/flow/keys/encrypt.post.ts
export default defineEventHandler(async (event) => {
  const session = await getUserSession(event)
  const { provider, apiKey, scope } = await readBody(event)
  
  // Validate input
  if (!provider || !apiKey) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Provider and API key are required'
    })
  }

  // Encrypt API key with workspace-specific encryption
  const encryptedKey = await encryptApiKey(apiKey, {
    workspaceId: session.workspace_id,
    userId: session.user_id,
    provider,
    scope
  })

  // Store encrypted key
  const { useEntityApi } = useDataApi()
  const keysApi = useEntityApi('flow_api_keys')
  
  const keyRecord = await keysApi.create({
    id: generateId(),
    workspace_id: session.workspace_id,
    user_id: session.user_id,
    provider,
    encrypted_key: encryptedKey.data,
    salt: encryptedKey.salt,
    iv: encryptedKey.iv,
    scope,
    created_at: new Date().toISOString(),
    active: true
  })

  return {
    success: true,
    key_id: keyRecord.id
  }
})

// Encryption utilities
async function encryptApiKey(apiKey: string, context: EncryptionContext) {
  const crypto = await import('crypto')
  
  // Generate workspace-specific key using PBKDF2
  const salt = crypto.randomBytes(32)
  const key = crypto.pbkdf2Sync(
    process.env.ENCRYPTION_KEY + context.workspaceId,
    salt,
    100000,
    32,
    'sha256'
  )
  
  // Encrypt with AES-256-GCM
  const iv = crypto.randomBytes(16)
  const cipher = crypto.createCipher('aes-256-gcm', key)
  cipher.setAAD(Buffer.from(context.workspaceId))
  
  let encrypted = cipher.update(apiKey, 'utf8', 'hex')
  encrypted += cipher.final('hex')
  
  const authTag = cipher.getAuthTag()
  
  return {
    data: encrypted,
    salt: salt.toString('hex'),
    iv: iv.toString('hex'),
    authTag: authTag.toString('hex')
  }
}

async function decryptApiKey(keyRecord: EncryptedKeyRecord) {
  const crypto = await import('crypto')
  
  // Recreate encryption key
  const salt = Buffer.from(keyRecord.salt, 'hex')
  const key = crypto.pbkdf2Sync(
    process.env.ENCRYPTION_KEY + keyRecord.workspace_id,
    salt,
    100000,
    32,
    'sha256'
  )
  
  // Decrypt
  const iv = Buffer.from(keyRecord.iv, 'hex')
  const authTag = Buffer.from(keyRecord.authTag, 'hex')
  const decipher = crypto.createDecipher('aes-256-gcm', key)
  
  decipher.setAAD(Buffer.from(keyRecord.workspace_id))
  decipher.setAuthTag(authTag)
  
  let decrypted = decipher.update(keyRecord.encrypted_key, 'hex', 'utf8')
  decrypted += decipher.final('utf8')
  
  return decrypted
}
```

## WebSocket Implementation

### 1. Real-time Execution Updates

```typescript
// server/api/websocket/flow-execution.ts
export default defineWebSocketHandler({
  async open(peer) {
    const query = getQuery(peer.url)
    const workspaceId = query.workspace_id as string
    const userId = query.user_id as string
    
    // Validate session
    const session = await validateWebSocketSession(peer, { workspaceId, userId })
    if (!session.valid) {
      peer.close(1008, 'Invalid session')
      return
    }
    
    // Subscribe to workspace events
    peer.subscribe(`workspace:${workspaceId}`)
    peer.subscribe(`user:${userId}`)
    
    console.log(`WebSocket connected: ${userId}@${workspaceId}`)
  },

  async message(peer, message) {
    try {
      const data = JSON.parse(message.text())
      const { type, payload } = data
      
      switch (type) {
        case 'subscribe_execution':
          await handleExecutionSubscription(peer, payload)
          break
        case 'execution_control':
          await handleExecutionControl(peer, payload)
          break
      }
    } catch (error) {
      peer.send(JSON.stringify({
        type: 'error',
        message: 'Invalid message format'
      }))
    }
  },

  async close(peer) {
    console.log('WebSocket disconnected')
  }
})

async function handleExecutionSubscription(peer: any, payload: any) {
  const { executionId } = payload
  
  // Validate access to execution
  const execution = await validateExecutionAccess(executionId, peer.session)
  if (!execution) {
    peer.send(JSON.stringify({
      type: 'error',
      message: 'Execution not found or access denied'
    }))
    return
  }
  
  // Subscribe to execution updates
  peer.subscribe(`execution:${executionId}`)
  
  // Send current execution state
  peer.send(JSON.stringify({
    type: 'execution_state',
    execution
  }))
}
```

## Data Validation & Security

### 1. Input Validation

```typescript
// utils/validation.ts
import { z } from 'zod'

export const FlowExecutionSchema = z.object({
  inputs: z.record(z.any()),
  mode: z.enum(['test', 'production']).default('production'),
  testConfig: z.object({
    stepByStep: z.boolean().optional(),
    breakpoints: z.array(z.string()).optional(),
    mocks: z.record(z.any()).optional()
  }).optional()
})

export const ApiKeySchema = z.object({
  provider: z.enum(['openai', 'anthropic', 'gemini']),
  apiKey: z.string().min(1),
  scope: z.object({
    permissions: z.array(z.enum(['read', 'write', 'execute'])),
    rateLimits: z.object({
      requestsPerMinute: z.number().positive()
    }).optional()
  })
})

// Usage in API endpoints
export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const validatedData = FlowExecutionSchema.parse(body)
  // ... rest of handler
})
```

### 2. Rate Limiting

```typescript
// utils/rateLimiting.ts
const rateLimitStore = new Map<string, RateLimitData>()

export async function checkRateLimit(
  key: string, 
  options: RateLimitOptions
): Promise<RateLimitResult> {
  const now = Date.now()
  const windowStart = now - options.windowMs
  
  let data = rateLimitStore.get(key)
  if (!data) {
    data = { requests: [], resetTime: now + options.windowMs }
    rateLimitStore.set(key, data)
  }
  
  // Remove old requests
  data.requests = data.requests.filter(time => time > windowStart)
  
  if (data.requests.length >= options.max) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: data.resetTime
    }
  }
  
  data.requests.push(now)
  
  return {
    allowed: true,
    remaining: options.max - data.requests.length,
    resetTime: data.resetTime
  }
}
```

## Error Handling

### 1. Structured Error Responses

```typescript
// utils/errorHandling.ts
export class FlowApiError extends Error {
  constructor(
    public statusCode: number,
    public statusMessage: string,
    public details?: any
  ) {
    super(statusMessage)
    this.name = 'FlowApiError'
  }
}

export function handleApiError(error: any) {
  if (error instanceof FlowApiError) {
    throw createError({
      statusCode: error.statusCode,
      statusMessage: error.statusMessage,
      data: error.details
    })
  }
  
  console.error('Unexpected API error:', error)
  throw createError({
    statusCode: 500,
    statusMessage: 'Internal server error'
  })
}
```

## Testing API Endpoints

### 1. Unit Testing

```typescript
// tests/api/flow-execution.test.ts
import { describe, it, expect, beforeEach } from 'vitest'
import { createMockEvent } from '../utils/mockEvent'

describe('/api/flow/execute/[id]', () => {
  beforeEach(() => {
    // Setup test database
  })

  it('should execute flow successfully', async () => {
    const event = createMockEvent({
      method: 'POST',
      url: '/api/flow/execute/test-flow-id',
      body: {
        inputs: { test: 'data' },
        mode: 'test'
      },
      session: {
        user_id: 'test-user',
        workspace_id: 'test-workspace'
      }
    })

    const response = await executeFlowHandler(event)
    
    expect(response.success).toBe(true)
    expect(response.execution_id).toBeDefined()
  })

  it('should reject unauthorized requests', async () => {
    const event = createMockEvent({
      method: 'POST',
      url: '/api/flow/execute/test-flow-id',
      session: null
    })

    await expect(executeFlowHandler(event)).rejects.toThrow('Unauthorized')
  })
})
```

## Performance Optimization

### 1. Caching Strategies

```typescript
// utils/caching.ts
const cache = new Map<string, CacheEntry>()

export async function getCachedOrFetch<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttl: number = 300000 // 5 minutes
): Promise<T> {
  const cached = cache.get(key)
  
  if (cached && Date.now() - cached.timestamp < ttl) {
    return cached.data
  }
  
  const data = await fetcher()
  cache.set(key, { data, timestamp: Date.now() })
  
  return data
}
```

## Next Steps

1. **[LangGraph Integration Guide](langraph-integration-guide.md)** - Deep dive into workflow execution
2. **[Security Implementation Guide](security-implementation-guide.md)** - Advanced security patterns
3. **[WebSocket Implementation Guide](websocket-implementation-guide.md)** - Real-time communication

---

**Implementation Status**: Ready for Development  
**Complexity**: Advanced  
**Estimated Development Time**: 2-3 weeks for core APIs
