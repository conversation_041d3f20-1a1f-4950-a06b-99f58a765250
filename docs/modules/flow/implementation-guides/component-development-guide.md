# Component Development Guide - Flow Module

**Target Audience:** Frontend Developers  
**Prerequisites:** Vue 3, TypeScript, Nuxt 3, Shuriken UI  
**Estimated Time:** 2-4 hours setup, ongoing development

## Overview

This guide provides step-by-step instructions for developing Vue 3 components within the Flow module, following established PartnersInBiz patterns and integrating with the Shuriken UI design system.

## Prerequisites

### Required Knowledge
- Vue 3 Composition API with `<script setup>` syntax
- TypeScript 5.8+ in strict mode
- Nuxt 3 layer architecture patterns
- Shuriken UI component library
- Pinia state management

### Development Environment
```bash
# Ensure you have the correct versions
node --version    # v18.0.0+
pnpm --version    # v8.0.0+
```

## Component Architecture Patterns

### 1. Standard Component Structure

```vue
<template>
  <div 
    :class="componentClasses"
    :aria-label="accessibilityLabel"
    :role="semanticRole"
    @keydown="handleKeyboardNavigation"
  >
    <!-- Component content -->
    <slot />
    
    <!-- Loading state -->
    <FlowLoading v-if="isLoading" :size="loadingSize" />
    
    <!-- Error state -->
    <FlowErrorBoundary v-if="hasError" :error="error" />
  </div>
</template>

<script setup lang="ts">
interface Props {
  // Required props
  id: string
  
  // Optional props with defaults
  variant?: ComponentVariant
  size?: ComponentSize
  disabled?: boolean
  loading?: boolean
  
  // Flow-specific props
  flowContext?: FlowContext
  executionState?: ExecutionState
  
  // Accessibility props
  ariaLabel?: string
  ariaDescribedBy?: string
}

interface Emits {
  // Standard events
  click: [event: MouseEvent]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
  
  // Flow-specific events
  flowAction: [action: FlowAction]
  stateChange: [state: ComponentState]
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'md',
  disabled: false,
  loading: false
})

const emit = defineEmits<Emits>()

// Component logic with composables
const { componentClasses, handleKeyboardNavigation } = useFlowComponent(props)
const { accessibilityLabel, semanticRole } = useFlowAccessibility(props)
const { isLoading, hasError, error } = useComponentState(props)
</script>

<style scoped>
/* Component-specific styles */
</style>
```

### 2. Shuriken UI Integration Pattern

```vue
<template>
  <BaseButton
    :variant="flowContext === 'canvas' ? 'ghost' : 'solid'"
    :size="isTablet ? 'lg' : 'md'"
    :disabled="!canExecuteAction"
    :loading="isProcessing"
    @click="handleFlowAction"
  >
    <FlowIcon :name="actionIcon" />
    {{ actionLabel }}
  </BaseButton>
</template>

<script setup lang="ts">
// Import Shuriken UI components
import { BaseButton } from '@layers/core/components'

// Flow-specific enhancements
interface FlowButtonProps extends ShurikenButtonProps {
  flowContext?: 'canvas' | 'panel' | 'modal' | 'toolbar'
  executionState?: 'idle' | 'running' | 'completed' | 'error'
  actionIcon?: string
  actionLabel?: string
}

const { isTablet } = useResponsive()
const { canExecuteAction, isProcessing } = useFlowActions()
</script>
```

## Node Component Development

### 1. Base Node Component Pattern

```vue
<!-- BaseNode.vue - Foundation for all node types -->
<template>
  <div 
    :class="nodeClasses"
    :style="nodeStyles"
    :data-node-id="nodeId"
    :data-node-type="nodeType"
    :aria-label="`${nodeType} node: ${nodeLabel}`"
    role="button"
    tabindex="0"
    @click="handleNodeClick"
    @keydown="handleNodeKeyboard"
  >
    <!-- Node Header -->
    <div class="node-header">
      <FlowIcon :name="nodeIcon" :class="iconClasses" />
      <span class="node-title">{{ nodeLabel }}</span>
      <NodeStatusIndicator :status="executionStatus" />
    </div>
    
    <!-- Node Content -->
    <div class="node-content">
      <slot name="content" />
    </div>
    
    <!-- Connection Handles -->
    <NodeHandle
      v-for="handle in inputHandles"
      :key="handle.id"
      :handle="handle"
      type="input"
      :position="handle.position"
    />
    <NodeHandle
      v-for="handle in outputHandles"
      :key="handle.id"
      :handle="handle"
      type="output"
      :position="handle.position"
    />
  </div>
</template>

<script setup lang="ts">
interface NodeProps {
  nodeId: string
  nodeType: NodeType
  nodeLabel: string
  nodeData: NodeData
  position: Position
  selected?: boolean
  dragging?: boolean
  executionStatus?: ExecutionStatus
}

// Node-specific composables
const { nodeClasses, nodeStyles, nodeIcon } = useNodeAppearance(props)
const { inputHandles, outputHandles } = useNodeConnections(props)
const { handleNodeClick, handleNodeKeyboard } = useNodeInteractions(props)
</script>
```

### 2. Specific Node Type Implementation

```vue
<!-- LLMNode.vue - LLM interaction node -->
<template>
  <BaseNode v-bind="nodeProps">
    <template #content>
      <div class="llm-node-content">
        <!-- Prompt Configuration -->
        <div class="prompt-section">
          <label class="prompt-label">Prompt Template</label>
          <BaseTextarea
            v-model="promptTemplate"
            placeholder="Enter your prompt template..."
            :rows="3"
            @input="updateNodeData"
          />
        </div>
        
        <!-- Model Selection -->
        <div class="model-section">
          <label class="model-label">AI Model</label>
          <BaseSelect
            v-model="selectedModel"
            :options="availableModels"
            @change="updateNodeData"
          />
        </div>
        
        <!-- Parameters -->
        <div class="parameters-section">
          <ParameterInput
            v-for="param in modelParameters"
            :key="param.name"
            :parameter="param"
            @update="updateParameter"
          />
        </div>
      </div>
    </template>
  </BaseNode>
</template>

<script setup lang="ts">
interface LLMNodeProps extends NodeProps {
  nodeType: 'llm_prompt'
}

const promptTemplate = ref('')
const selectedModel = ref('gpt-4')
const modelParameters = ref([])

const { availableModels } = useLLMModels()
const { updateNodeData } = useNodeConfiguration()

// Watch for changes and update node data
watch([promptTemplate, selectedModel, modelParameters], () => {
  updateNodeData(props.nodeId, {
    promptTemplate: promptTemplate.value,
    model: selectedModel.value,
    parameters: modelParameters.value
  })
}, { deep: true })
</script>
```

## Composable Integration

### 1. Using Flow-Specific Composables

```typescript
// In your component
const { 
  currentFlow, 
  canvasState, 
  saveFlow 
} = useFlowCanvas()

const { 
  startExecution, 
  executionState, 
  nodeExecutionStates 
} = useFlowExecution()

const { 
  templates, 
  loadTemplates, 
  createFlowFromTemplate 
} = useFlowTemplates()
```

### 2. Creating Custom Composables

```typescript
// composables/useNodeConfiguration.ts
export const useNodeConfiguration = (nodeId: string) => {
  const { updateNode } = useFlowCanvas()
  
  const updateNodeData = (data: Partial<NodeData>) => {
    updateNode(nodeId, { data })
  }
  
  const validateNodeConfig = (config: NodeConfiguration) => {
    // Validation logic
    return { valid: true, errors: [] }
  }
  
  return {
    updateNodeData,
    validateNodeConfig
  }
}
```

## State Management Integration

### 1. Using Pinia Stores

```typescript
// In your component
import { useFlowCanvasStore } from '@/stores/flowCanvas'

const canvasStore = useFlowCanvasStore()

// Reactive state access
const nodes = computed(() => canvasStore.nodes)
const selectedNodes = computed(() => canvasStore.selectedNodes)

// Actions
const addNode = (node: FlowNode) => {
  canvasStore.addNode(node)
}
```

### 2. Reactive State Patterns

```typescript
// Using Nuxt's useState for global state
const currentExecution = useState<FlowExecution | null>('flow.execution.current')

// Computed properties for derived state
const isExecuting = computed(() => 
  currentExecution.value?.status === 'running'
)

// Watchers for side effects
watch(currentExecution, (execution) => {
  if (execution?.status === 'completed') {
    // Handle completion
  }
})
```

## Accessibility Implementation

### 1. Keyboard Navigation

```typescript
const handleKeyDown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'Enter':
    case ' ':
      event.preventDefault()
      handleActivation()
      break
    case 'Escape':
      event.preventDefault()
      handleCancel()
      break
    case 'ArrowUp':
    case 'ArrowDown':
    case 'ArrowLeft':
    case 'ArrowRight':
      event.preventDefault()
      handleNavigation(event.key)
      break
  }
}
```

### 2. Screen Reader Support

```vue
<template>
  <div
    :aria-label="accessibleLabel"
    :aria-describedby="descriptionId"
    :aria-expanded="isExpanded"
    role="button"
    tabindex="0"
  >
    <!-- Component content -->
    
    <!-- Hidden description for screen readers -->
    <div :id="descriptionId" class="sr-only">
      {{ detailedDescription }}
    </div>
  </div>
</template>
```

## Testing Components

### 1. Unit Testing with Vitest

```typescript
// ComponentName.test.ts
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import ComponentName from './ComponentName.vue'

describe('ComponentName', () => {
  it('renders correctly with required props', () => {
    const wrapper = mount(ComponentName, {
      props: {
        id: 'test-component',
        nodeType: 'input'
      }
    })
    
    expect(wrapper.find('[data-testid="component-name"]').exists()).toBe(true)
  })
  
  it('emits events correctly', async () => {
    const wrapper = mount(ComponentName, {
      props: { id: 'test' }
    })
    
    await wrapper.find('button').trigger('click')
    
    expect(wrapper.emitted('click')).toBeTruthy()
  })
})
```

### 2. Accessibility Testing

```typescript
import { axe, toHaveNoViolations } from 'jest-axe'

expect.extend(toHaveNoViolations)

it('should not have accessibility violations', async () => {
  const wrapper = mount(ComponentName)
  const results = await axe(wrapper.element)
  expect(results).toHaveNoViolations()
})
```

## Performance Optimization

### 1. Virtual Scrolling for Large Lists

```vue
<template>
  <div class="virtual-list" ref="containerRef">
    <div 
      v-for="item in visibleItems" 
      :key="item.id"
      :style="getItemStyle(item)"
      class="virtual-item"
    >
      <slot :item="item" />
    </div>
  </div>
</template>

<script setup lang="ts">
const { visibleItems, getItemStyle } = useVirtualList({
  items: props.items,
  itemHeight: 60,
  containerHeight: 400
})
</script>
```

### 2. Lazy Loading Components

```typescript
// Lazy load heavy components
const HeavyComponent = defineAsyncComponent(() => 
  import('./HeavyComponent.vue')
)
```

## Troubleshooting

### Common Issues

1. **Component not reactive**: Ensure you're using `ref()` or `reactive()` for state
2. **Props not updating**: Check if you're mutating props directly
3. **Events not emitting**: Verify event names match the `defineEmits` interface
4. **Styles not applying**: Check scoped styles and CSS specificity

### Debugging Tips

```typescript
// Add debug logging in development
if (process.dev) {
  console.log('Component state:', toRaw(componentState))
}

// Use Vue DevTools for reactive debugging
// Install Vue DevTools browser extension
```

## Next Steps

1. **[Canvas Implementation Guide](canvas-implementation-guide.md)** - Implement the visual canvas
2. **[API Development Guide](api-development-guide.md)** - Connect to backend APIs
3. **[Testing Implementation Guide](testing-implementation-guide.md)** - Comprehensive testing

---

**Implementation Status**: Ready for Development  
**Complexity**: Intermediate  
**Estimated Development Time**: 1-2 weeks for core components
