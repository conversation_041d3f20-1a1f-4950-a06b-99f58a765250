# Deployment Guide - Flow Module

**Target Audience:** <PERSON><PERSON>ps Engineers, Platform Engineers  
**Prerequisites:** Firebase, Nuxt 3, CI/CD, Monitoring  
**Estimated Time:** 1-2 days initial setup, ongoing maintenance

## Overview

This guide provides comprehensive instructions for deploying the Flow module to production, including infrastructure setup, CI/CD configuration, monitoring, and scaling strategies.

## Prerequisites

### Required Infrastructure
- Firebase project with Firestore, Functions, and Hosting
- Domain with SSL certificate
- CI/CD pipeline (GitHub Actions recommended)
- Monitoring and alerting system
- Error tracking service (Sentry recommended)

### Environment Variables
```bash
# Production Environment Variables
FIREBASE_PROJECT_ID=partners-in-biz-85059
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_AUTH_DOMAIN=partners-in-biz-85059.firebaseapp.com
FIREBASE_DATABASE_URL=https://partners-in-biz-85059.firebaseio.com
FIREBASE_STORAGE_BUCKET=partners-in-biz-85059.appspot.com

# LangGraph Configuration
LANGRAPH_API_KEY=your_langraph_api_key
LANGRAPH_ENDPOINT=https://api.langraph.com

# Security
ENCRYPTION_KEY=your_32_character_encryption_key
JWT_SECRET=your_jwt_secret_key
WEBHOOK_SECRET=your_webhook_secret

# Monitoring
SENTRY_DSN=your_sentry_dsn
ANALYTICS_ID=your_analytics_id

# Performance
REDIS_URL=your_redis_connection_string
CDN_URL=your_cdn_url
```

## Infrastructure Setup

### 1. Firebase Configuration

```json
// firebase.json
{
  "hosting": {
    "public": ".output/public",
    "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],
    "rewrites": [
      {
        "source": "/api/**",
        "function": "nuxtssr"
      },
      {
        "source": "**",
        "destination": "/index.html"
      }
    ],
    "headers": [
      {
        "source": "/api/**",
        "headers": [
          {
            "key": "Cache-Control",
            "value": "no-cache, no-store, must-revalidate"
          }
        ]
      },
      {
        "source": "/_nuxt/**",
        "headers": [
          {
            "key": "Cache-Control",
            "value": "public, max-age=31536000, immutable"
          }
        ]
      }
    ]
  },
  "functions": {
    "source": ".output/server",
    "runtime": "nodejs18",
    "memory": "2GB",
    "timeout": "60s"
  },
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  }
}
```

### 2. Firestore Security Rules

```javascript
// firestore.rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Flow module collections
    match /flows/{flowId} {
      allow read, write: if request.auth != null 
        && request.auth.token.workspace_id == resource.data.workspace_id;
    }
    
    match /flow_executions/{executionId} {
      allow read, write: if request.auth != null 
        && request.auth.token.workspace_id == resource.data.workspace_id;
    }
    
    match /flow_templates/{templateId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null 
        && request.auth.token.workspace_id == resource.data.workspace_id;
    }
    
    match /flow_api_keys/{keyId} {
      allow read, write: if request.auth != null 
        && request.auth.token.workspace_id == resource.data.workspace_id
        && request.auth.token.user_id == resource.data.user_id;
    }
  }
}
```

### 3. Firestore Indexes

```json
// firestore.indexes.json
{
  "indexes": [
    {
      "collectionGroup": "flows",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "workspace_id", "order": "ASCENDING" },
        { "fieldPath": "user_id", "order": "ASCENDING" },
        { "fieldPath": "updated_at", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "flow_executions",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "workspace_id", "order": "ASCENDING" },
        { "fieldPath": "status", "order": "ASCENDING" },
        { "fieldPath": "created_at", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "flow_templates",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "category", "order": "ASCENDING" },
        { "fieldPath": "target_personas", "arrayContains": true },
        { "fieldPath": "usage_count", "order": "DESCENDING" }
      ]
    }
  ],
  "fieldOverrides": []
}
```

## CI/CD Pipeline Configuration

### 1. GitHub Actions Workflow

```yaml
# .github/workflows/deploy-flow-module.yml
name: Deploy Flow Module

on:
  push:
    branches: [main]
    paths: ['layers/flow/**', 'docs/modules/flow/**']
  pull_request:
    branches: [main]
    paths: ['layers/flow/**']

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Run type checking
        run: pnpm --filter=flow run type-check
      
      - name: Run unit tests
        run: pnpm --filter=flow run test:unit
      
      - name: Run integration tests
        run: pnpm --filter=flow run test:integration
        env:
          FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID_TEST }}
      
      - name: Run accessibility tests
        run: pnpm --filter=flow run test:a11y
      
      - name: Run security scan
        run: pnpm audit --audit-level moderate

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Build application
        run: pnpm --filter=flow run build
        env:
          NUXT_PUBLIC_FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
          NUXT_PUBLIC_FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}
          NUXT_PUBLIC_SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: flow-build
          path: .output/

  deploy:
    needs: build
    runs-on: ubuntu-latest
    environment: production
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: flow-build
          path: .output/
      
      - name: Deploy to Firebase
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT }}'
          projectId: ${{ secrets.FIREBASE_PROJECT_ID }}
          channelId: live
      
      - name: Deploy Firestore rules
        run: |
          npm install -g firebase-tools
          firebase deploy --only firestore:rules,firestore:indexes --project ${{ secrets.FIREBASE_PROJECT_ID }}
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
      
      - name: Notify deployment
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

### 2. Environment-Specific Configuration

```typescript
// nuxt.config.ts - Production optimizations
export default defineNuxtConfig({
  // Production-specific settings
  ssr: true,
  nitro: {
    preset: 'firebase',
    firebase: {
      gen: 2,
      httpsOptions: {
        region: 'us-central1',
        maxInstances: 100,
        memory: '2GiB',
        timeoutSeconds: 60
      }
    },
    minify: true,
    sourceMap: false
  },
  
  // Performance optimizations
  experimental: {
    payloadExtraction: false,
    inlineSSRStyles: false
  },
  
  // Security headers
  security: {
    headers: {
      contentSecurityPolicy: {
        'default-src': ["'self'"],
        'script-src': ["'self'", "'unsafe-inline'", 'https://apis.google.com'],
        'style-src': ["'self'", "'unsafe-inline'"],
        'img-src': ["'self'", 'data:', 'https:'],
        'connect-src': ["'self'", 'https://api.langraph.com', 'wss:']
      },
      crossOriginEmbedderPolicy: false
    }
  },
  
  // Monitoring
  sentry: {
    dsn: process.env.SENTRY_DSN,
    environment: process.env.NODE_ENV,
    tracesSampleRate: 0.1
  }
})
```

## Monitoring & Observability

### 1. Application Monitoring

```typescript
// plugins/monitoring.client.ts
export default defineNuxtPlugin(() => {
  // Performance monitoring
  if (process.client) {
    // Web Vitals tracking
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS(sendToAnalytics)
      getFID(sendToAnalytics)
      getFCP(sendToAnalytics)
      getLCP(sendToAnalytics)
      getTTFB(sendToAnalytics)
    })
    
    // Flow-specific metrics
    const { trackFlowMetrics } = useFlowAnalytics()
    trackFlowMetrics()
  }
})

function sendToAnalytics(metric: any) {
  // Send to your analytics service
  if (window.gtag) {
    window.gtag('event', metric.name, {
      value: Math.round(metric.value),
      metric_id: metric.id,
      metric_value: metric.value,
      metric_delta: metric.delta
    })
  }
}
```

### 2. Error Tracking

```typescript
// plugins/error-tracking.ts
export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.hook('vue:error', (error, context) => {
    // Send to Sentry
    if (process.client) {
      import('@sentry/browser').then(({ captureException }) => {
        captureException(error, {
          contexts: {
            vue: {
              componentName: context.componentName,
              propsData: context.propsData
            }
          }
        })
      })
    }
  })
  
  // API error tracking
  nuxtApp.hook('app:error', (error) => {
    console.error('Application error:', error)
    // Send to monitoring service
  })
})
```

### 3. Performance Monitoring

```typescript
// composables/usePerformanceMonitoring.ts
export const usePerformanceMonitoring = () => {
  const metrics = ref({
    canvasRenderTime: 0,
    nodeCount: 0,
    executionLatency: 0,
    memoryUsage: 0
  })
  
  const trackCanvasPerformance = () => {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name === 'canvas-render') {
          metrics.value.canvasRenderTime = entry.duration
        }
      }
    })
    
    observer.observe({ entryTypes: ['measure'] })
  }
  
  const trackMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      metrics.value.memoryUsage = memory.usedJSHeapSize
    }
  }
  
  return {
    metrics: readonly(metrics),
    trackCanvasPerformance,
    trackMemoryUsage
  }
}
```

## Scaling Configuration

### 1. Auto-scaling Settings

```yaml
# Cloud Function scaling
functions:
  nuxtssr:
    runtime: nodejs18
    memory: 2GB
    timeout: 60s
    minInstances: 2
    maxInstances: 100
    concurrency: 80
    
  flowExecution:
    runtime: nodejs18
    memory: 4GB
    timeout: 300s
    minInstances: 1
    maxInstances: 50
    concurrency: 10
```

### 2. Database Optimization

```typescript
// Database connection pooling
const firestoreSettings = {
  ignoreUndefinedProperties: true,
  cacheSizeBytes: 100 * 1024 * 1024, // 100MB cache
  experimentalForceLongPolling: false,
  merge: true
}

// Query optimization
const optimizedQueries = {
  // Use composite indexes
  getWorkspaceFlows: (workspaceId: string) => 
    query(
      collection(db, 'flows'),
      where('workspace_id', '==', workspaceId),
      orderBy('updated_at', 'desc'),
      limit(50)
    ),
  
  // Paginated queries
  getFlowsPaginated: (workspaceId: string, lastDoc?: DocumentSnapshot) => {
    let q = query(
      collection(db, 'flows'),
      where('workspace_id', '==', workspaceId),
      orderBy('updated_at', 'desc'),
      limit(20)
    )
    
    if (lastDoc) {
      q = query(q, startAfter(lastDoc))
    }
    
    return q
  }
}
```

## Security Hardening

### 1. API Security

```typescript
// Rate limiting middleware
export default defineEventHandler(async (event) => {
  const ip = getClientIP(event)
  const key = `rate_limit:${ip}`
  
  const { allowed, remaining } = await checkRateLimit(key, {
    max: 100,
    windowMs: 60000 // 1 minute
  })
  
  if (!allowed) {
    throw createError({
      statusCode: 429,
      statusMessage: 'Too Many Requests'
    })
  }
  
  setHeader(event, 'X-RateLimit-Remaining', remaining.toString())
})
```

### 2. Input Validation

```typescript
// Comprehensive input validation
import { z } from 'zod'

const FlowSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  nodes: z.array(NodeSchema).max(1000),
  edges: z.array(EdgeSchema).max(2000),
  workspace_id: z.string().uuid(),
  user_id: z.string().uuid()
})

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  
  try {
    const validatedData = FlowSchema.parse(body)
    // Process validated data
  } catch (error) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid input data',
      data: error.errors
    })
  }
})
```

## Backup & Recovery

### 1. Automated Backups

```bash
#!/bin/bash
# backup-firestore.sh

PROJECT_ID="partners-in-biz-85059"
BUCKET_NAME="flow-backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Export Firestore data
gcloud firestore export gs://${BUCKET_NAME}/firestore_${DATE} \
  --project=${PROJECT_ID} \
  --collection-ids=flows,flow_executions,flow_templates,flow_api_keys

# Cleanup old backups (keep last 30 days)
gsutil -m rm -r gs://${BUCKET_NAME}/firestore_$(date -d '30 days ago' +%Y%m%d)_*
```

### 2. Disaster Recovery Plan

```typescript
// Recovery procedures
const recoveryProcedures = {
  // Database recovery
  restoreFirestore: async (backupDate: string) => {
    // Restore from backup
    const command = `gcloud firestore import gs://flow-backups/firestore_${backupDate}`
    // Execute restoration
  },
  
  // Application recovery
  rollbackDeployment: async (version: string) => {
    // Rollback to previous version
    const command = `firebase hosting:channel:deploy ${version}`
    // Execute rollback
  },
  
  // Data integrity check
  validateDataIntegrity: async () => {
    // Check data consistency
    // Validate relationships
    // Report any issues
  }
}
```

## Health Checks & Monitoring

### 1. Health Check Endpoints

```typescript
// server/api/health.get.ts
export default defineEventHandler(async (event) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.APP_VERSION,
    checks: {
      database: await checkFirestore(),
      langraph: await checkLangGraph(),
      memory: checkMemoryUsage(),
      disk: checkDiskSpace()
    }
  }
  
  const isHealthy = Object.values(health.checks).every(check => check.status === 'ok')
  
  setResponseStatus(event, isHealthy ? 200 : 503)
  return health
})
```

### 2. Alerting Configuration

```yaml
# alerting-rules.yml
groups:
  - name: flow-module-alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: High error rate detected
          
      - alert: SlowResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: Slow response times detected
          
      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes / 1024 / 1024 > 1500
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: High memory usage detected
```

## Troubleshooting

### Common Deployment Issues

1. **Build Failures**
   - Check TypeScript compilation errors
   - Verify environment variables
   - Review dependency versions

2. **Runtime Errors**
   - Check Firebase configuration
   - Verify API endpoints
   - Review security rules

3. **Performance Issues**
   - Monitor memory usage
   - Check database query performance
   - Review caching strategies

### Debugging Tools

```bash
# Firebase debugging
firebase serve --only functions,hosting
firebase emulators:start

# Performance profiling
lighthouse --chrome-flags="--headless" https://your-domain.com

# Security scanning
npm audit
snyk test
```

## Next Steps

1. **[Monitoring Setup Guide](monitoring-setup-guide.md)** - Detailed monitoring configuration
2. **[Scaling Guide](scaling-guide.md)** - Advanced scaling strategies
3. **[Security Testing Guide](security-testing-guide.md)** - Security validation procedures

---

**Deployment Status**: Production Ready  
**Complexity**: Advanced  
**Estimated Setup Time**: 1-2 days initial, ongoing maintenance
