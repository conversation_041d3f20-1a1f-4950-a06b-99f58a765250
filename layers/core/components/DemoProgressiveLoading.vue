<template>
  <div class="progressive-loading-demo">
    <div class="demo-header">
      <h3 class="text-lg font-semibold">Progressive Layer Loading Demo</h3>
      <p class="text-sm text-muted-foreground">
        Demonstrates the progressive loading system with core structure loading in &lt;2s
      </p>
    </div>

    <!-- Loading Indicator -->
    <ProgressiveLoadingIndicator
      :title="loadingTitle"
      :layers="layerStates"
      :overall-progress="overallProgress"
      :core-progress="coreProgress"
      :background-progress="backgroundProgress"
      :core-complete="coreComplete"
      :background-complete="backgroundComplete"
      :average-load-time="averageLoadTime"
      :cache-hit-rate="cacheHitRate"
      :memory-usage="memoryUsage"
      :persistent-cache-items="persistentCacheItems"
      :active-workers="activeWorkers"
      :max-workers="maxWorkers"
      :show-phases="true"
      :show-details="showDetails"
      :show-stats="showStats"
      :show-actions="true"
      :can-cancel="isLoading"
      :can-retry="hasErrors"
      :can-optimize="true"
      @cancel="cancelLoading"
      @retry="retryFailed"
      @optimize="optimizePerformance"
    />

    <!-- Demo Controls -->
    <div class="demo-controls">
      <div class="control-section">
        <h4 class="font-medium mb-2">Demo Controls</h4>
        <div class="button-group">
          <BaseButton
            @click="startDemo"
            :disabled="isLoading"
            variant="solid"
            color="primary"
            size="sm"
          >
            <Icon name="lucide:play" class="h-4 w-4" />
            Start Loading Demo
          </BaseButton>
          
          <BaseButton
            @click="simulateSlowLayer"
            :disabled="!isLoading"
            variant="outline"
            size="sm"
          >
            <Icon name="lucide:turtle" class="h-4 w-4" />
            Simulate Slow Layer
          </BaseButton>
          
          <BaseButton
            @click="simulateError"
            :disabled="!isLoading"
            variant="outline"
            color="warning"
            size="sm"
          >
            <Icon name="lucide:alert-triangle" class="h-4 w-4" />
            Simulate Error
          </BaseButton>
        </div>
      </div>

      <div class="control-section">
        <h4 class="font-medium mb-2">View Options</h4>
        <div class="toggle-group">
          <BaseCheckbox v-model="showDetails" label="Show Layer Details" />
          <BaseCheckbox v-model="showStats" label="Show Performance Stats" />
        </div>
      </div>
    </div>

    <!-- Cache Status -->
    <div class="cache-status">
      <h4 class="font-medium mb-2">Cache Status</h4>
      <div class="cache-grid">
        <div class="cache-item">
          <span class="label">Memory Cache:</span>
          <span class="value">{{ memoryCacheSize }}MB</span>
        </div>
        <div class="cache-item">
          <span class="label">IndexedDB Cache:</span>
          <span class="value">{{ persistentCacheSize }}MB</span>
        </div>
        <div class="cache-item">
          <span class="label">Cache Hit Rate:</span>
          <span class="value">{{ cacheHitRate }}%</span>
        </div>
        <div class="cache-item">
          <span class="label">Priority Calculations:</span>
          <span class="value">{{ priorityCalculations }}</span>
        </div>
      </div>
    </div>

    <!-- Layer Priority Insights -->
    <div v-if="showPriorityInsights" class="priority-insights">
      <h4 class="font-medium mb-2">Layer Priority Insights</h4>
      <div class="priority-list">
        <div
          v-for="layer in sortedLayers"
          :key="layer.id"
          class="priority-item"
          :class="`priority-${layer.priority}`"
        >
          <div class="layer-info">
            <span class="layer-name">{{ layer.name }}</span>
            <Badge :variant="getPriorityVariant(layer.priority)" size="sm">
              {{ layer.priority }}
            </Badge>
          </div>
          <div class="layer-details">
            <span class="score">Score: {{ layer.score }}</span>
            <span class="reasons">{{ layer.reasons.join(', ') }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { 
  useProgressiveLoader, 
  useIndexedDBCache, 
  useLayerPriority,
  type LayerLoadingConfig 
} from '../composables'

// Demo state
const isLoading = ref(false)
const showDetails = ref(false)
const showStats = ref(true)
const showPriorityInsights = ref(false)

// Progressive loader setup
const progressiveLoader = useProgressiveLoader({
  maxConcurrentLoads: 3,
  coreLoadTimeout: 2000, // 2 seconds for core
  enableCaching: true
})

// Priority system setup
const prioritySystem = useLayerPriority()

// Cache setup
const indexedDBCache = useIndexedDBCache()

// Demo layer configurations
const demoLayers: LayerLoadingConfig[] = [
  // Core layers (must load within 2 seconds)
  {
    id: 'core-ui',
    type: 'core',
    priority: 'critical',
    loadingStrategy: 'immediate',
    dependencies: [],
    estimatedSize: 150 * 1024, // 150KB
    timeout: 1000,
    retryCount: 3,
    cacheStrategy: 'memory'
  },
  {
    id: 'core-data',
    type: 'core',
    priority: 'critical', 
    loadingStrategy: 'immediate',
    dependencies: [],
    estimatedSize: 300 * 1024, // 300KB
    timeout: 1500,
    retryCount: 3,
    cacheStrategy: 'disk'
  },
  {
    id: 'core-navigation',
    type: 'core',
    priority: 'high',
    loadingStrategy: 'immediate',
    dependencies: ['core-ui'],
    estimatedSize: 100 * 1024, // 100KB
    timeout: 800,
    retryCount: 2,
    cacheStrategy: 'memory'
  },
  // Background layers (load progressively)
  {
    id: 'writer-module',
    type: 'background',
    priority: 'high',
    loadingStrategy: 'deferred',
    dependencies: ['core-data'],
    estimatedSize: 2 * 1024 * 1024, // 2MB
    timeout: 5000,
    retryCount: 2,
    cacheStrategy: 'disk'
  },
  {
    id: 'crm-module',
    type: 'background',
    priority: 'medium',
    loadingStrategy: 'lazy',
    dependencies: ['core-data'],
    estimatedSize: 1.5 * 1024 * 1024, // 1.5MB
    visibilityThreshold: 0.1,
    timeout: 8000,
    retryCount: 2,
    cacheStrategy: 'disk'
  },
  {
    id: 'editor-module',
    type: 'background',
    priority: 'medium',
    loadingStrategy: 'on-demand',
    dependencies: ['core-ui', 'core-data'],
    estimatedSize: 3 * 1024 * 1024, // 3MB
    timeout: 10000,
    retryCount: 1,
    cacheStrategy: 'disk'
  },
  {
    id: 'analytics-module',
    type: 'optional',
    priority: 'low',
    loadingStrategy: 'lazy',
    dependencies: [],
    estimatedSize: 500 * 1024, // 500KB
    visibilityThreshold: 0.5,
    timeout: 15000,
    retryCount: 1,
    cacheStrategy: 'session'
  }
]

// Register layers with priority system
onMounted(() => {
  demoLayers.forEach(layer => {
    prioritySystem.registerLayer({
      id: layer.id,
      basePriority: layer.priority,
      dynamicFactors: {
        userInteraction: Math.random() * 0.8,
        viewportVisibility: Math.random() * 0.9,
        loadFrequency: Math.random() * 0.7,
        lastAccessTime: Date.now() - Math.random() * 86400000, // Random within 24h
        averageLoadTime: 1000 + Math.random() * 3000, // 1-4 seconds
        errorRate: Math.random() * 0.1 // 0-10% error rate
      },
      boosters: {
        isUserFocused: false,
        isInViewport: Math.random() > 0.5,
        isPrefetchCandidate: Math.random() > 0.7,
        hasUserPreference: Math.random() > 0.8
      },
      constraints: {
        timeBasedBoost: true,
        contextDependent: true
      }
    })
  })
})

// Computed properties for display
const layerStates = computed(() => {
  if (!progressiveLoader.getCurrentSession()) return []
  
  const session = progressiveLoader.getCurrentSession()
  if (!session) return []
  
  return Array.from(session.layers.values()).map(layer => ({
    id: layer.id,
    name: layer.id.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    type: demoLayers.find(l => l.id === layer.id)?.type || 'background',
    status: layer.status,
    progress: layer.progress,
    loadTime: layer.loadEndTime && layer.loadStartTime 
      ? layer.loadEndTime - layer.loadStartTime 
      : undefined,
    fromCache: layer.fromCache,
    size: layer.size
  }))
})

const overallProgress = computed(() => progressiveLoader.overallProgress.value)
const coreProgress = computed(() => {
  const coreLayers = layerStates.value.filter(l => l.type === 'core')
  if (coreLayers.length === 0) return 100
  return coreLayers.reduce((sum, layer) => sum + layer.progress, 0) / coreLayers.length
})

const backgroundProgress = computed(() => {
  const backgroundLayers = layerStates.value.filter(l => l.type !== 'core')
  if (backgroundLayers.length === 0) return 100
  return backgroundLayers.reduce((sum, layer) => sum + layer.progress, 0) / backgroundLayers.length
})

const coreComplete = computed(() => 
  progressiveLoader.coreLayersLoaded.value && coreProgress.value === 100
)

const backgroundComplete = computed(() => 
  backgroundProgress.value === 100 && layerStates.value.every(l => l.status === 'loaded')
)

const loadingTitle = computed(() => {
  if (!isLoading.value) return 'Progressive Loading Demo'
  if (coreComplete.value && backgroundComplete.value) return 'Loading Complete'
  if (coreComplete.value) return 'Core Loaded - Loading Modules'
  return 'Loading Core Structure'
})

const hasErrors = computed(() => 
  layerStates.value.some(l => l.status === 'error')
)

// Performance metrics
const averageLoadTime = computed(() => {
  const stats = progressiveLoader.getLoadingStats()
  return stats.averageLoadTime || 0
})

const cacheHitRate = computed(() => {
  const stats = progressiveLoader.getLoadingStats()
  const total = stats.totalLayers
  const fromCache = stats.fromCache
  return total > 0 ? Math.round((fromCache / total) * 100) : 0
})

const memoryUsage = computed(() => {
  const stats = progressiveLoader.getLoadingStats()
  return Math.round((stats.memoryStats?.usedJSHeapSize || 0) * 100) / 100
})

const persistentCacheItems = computed(() => 
  indexedDBCache.cacheStats.value.totalItems
)

const memoryCacheSize = computed(() => {
  const stats = progressiveLoader.getLoadingStats()
  return Math.round((stats.cacheStats?.size || 0) / 1024 / 1024 * 100) / 100
})

const persistentCacheSize = computed(() => 
  Math.round(indexedDBCache.cacheStats.value.totalSize / 1024 / 1024 * 100) / 100
)

const activeWorkers = computed(() => {
  const stats = progressiveLoader.getLoadingStats()
  return stats.queueStats?.active || 0
})

const maxWorkers = computed(() => {
  const stats = progressiveLoader.getLoadingStats()
  return stats.queueStats?.maxConcurrent || 0
})

const priorityCalculations = computed(() => 
  prioritySystem.priorityScores.value.size
)

const sortedLayers = computed(() => {
  return prioritySystem.getLayersByPriority().map(score => ({
    id: score.layerId,
    name: score.layerId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    priority: score.priority,
    score: Math.round(score.score),
    reasons: score.reasons.slice(0, 3) // Show first 3 reasons
  }))
})

// Demo functions
async function startDemo() {
  isLoading.value = true
  showPriorityInsights.value = true
  
  try {
    const session = await progressiveLoader.startLoadingSession(demoLayers)
    console.log('Loading session started:', session.id)
    
    // Simulate some user interactions during loading
    setTimeout(() => {
      prioritySystem.trackInteraction('writer-module', 'click')
      prioritySystem.boostLayerPriority('writer-module', 'isUserFocused')
    }, 1000)
    
    setTimeout(() => {
      prioritySystem.trackInteraction('crm-module', 'hover')
    }, 2000)
    
  } catch (error) {
    console.error('Demo loading failed:', error)
  } finally {
    setTimeout(() => {
      isLoading.value = false
    }, 10000) // Demo runs for 10 seconds
  }
}

function simulateSlowLayer() {
  // Find a loading layer and slow it down
  const loadingLayer = layerStates.value.find(l => l.status === 'loading')
  if (loadingLayer) {
    console.log(`Simulating slow performance for ${loadingLayer.id}`)
    prioritySystem.recordLoadPerformance(loadingLayer.id, 8000, true)
  }
}

function simulateError() {
  // Find a loading layer and simulate an error
  const loadingLayer = layerStates.value.find(l => l.status === 'loading')
  if (loadingLayer) {
    console.log(`Simulating error for ${loadingLayer.id}`)
    prioritySystem.recordLoadPerformance(loadingLayer.id, 5000, false)
  }
}

function cancelLoading() {
  progressiveLoader.cancelLoadingSession()
  isLoading.value = false
  showPriorityInsights.value = false
}

function retryFailed() {
  // In a real implementation, this would retry failed layers
  console.log('Retrying failed layers')
}

function optimizePerformance() {
  progressiveLoader.clearCache()
  prioritySystem.recalculateAllPriorities()
  console.log('Performance optimization triggered')
}

function getPriorityVariant(priority: string) {
  const variants = {
    critical: 'destructive',
    high: 'secondary',
    medium: 'outline',
    low: 'muted'
  }
  return variants[priority as keyof typeof variants] || 'outline'
}

// Cleanup
onUnmounted(() => {
  if (isLoading.value) {
    progressiveLoader.cancelLoadingSession()
  }
})
</script>

<style scoped>
.progressive-loading-demo {
  @apply space-y-6 p-6 bg-background rounded-lg border;
}

.demo-header {
  @apply space-y-2;
}

.demo-controls {
  @apply space-y-4 p-4 bg-muted/50 rounded-lg;
}

.control-section {
  @apply space-y-2;
}

.button-group {
  @apply flex flex-wrap gap-2;
}

.toggle-group {
  @apply flex flex-wrap gap-4;
}

.cache-status {
  @apply p-4 bg-card rounded-lg border;
}

.cache-grid {
  @apply grid grid-cols-2 md:grid-cols-4 gap-4;
}

.cache-item {
  @apply flex flex-col space-y-1;
}

.cache-item .label {
  @apply text-sm text-muted-foreground;
}

.cache-item .value {
  @apply font-mono font-medium text-foreground;
}

.priority-insights {
  @apply p-4 bg-card rounded-lg border;
}

.priority-list {
  @apply space-y-2;
}

.priority-item {
  @apply flex items-center justify-between p-3 rounded-md border;
}

.priority-item.priority-critical {
  @apply bg-destructive/5 border-destructive/20;
}

.priority-item.priority-high {
  @apply bg-secondary/5 border-secondary/20;
}

.priority-item.priority-medium {
  @apply bg-primary/5 border-primary/20;
}

.priority-item.priority-low {
  @apply bg-muted/50 border-muted;
}

.layer-info {
  @apply flex items-center gap-3;
}

.layer-name {
  @apply font-medium;
}

.layer-details {
  @apply flex flex-col items-end gap-1 text-sm text-muted-foreground;
}

.score {
  @apply font-mono;
}

.reasons {
  @apply text-xs;
}
</style>