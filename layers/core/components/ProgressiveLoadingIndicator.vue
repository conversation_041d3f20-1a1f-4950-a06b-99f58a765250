<template>
  <div class="progressive-loading-indicator">
    <!-- Main Progress Bar -->
    <div class="loading-header">
      <div class="loading-title">
        <Icon name="lucide:layers" class="h-5 w-5" />
        <span>{{ title }}</span>
      </div>
      <div class="loading-status">
        <span class="text-sm text-muted-foreground">
          {{ loadedCount }}/{{ totalCount }} layers
        </span>
        <Badge 
          :variant="statusVariant"
          size="sm"
        >
          {{ statusText }}
        </Badge>
      </div>
    </div>

    <!-- Overall Progress -->
    <div class="progress-container">
      <div class="progress-bar">
        <div 
          class="progress-fill"
          :style="{ width: `${overallProgress}%` }"
        />
      </div>
      <span class="progress-text">{{ Math.round(overallProgress) }}%</span>
    </div>

    <!-- Core vs Background Loading -->
    <div class="loading-phases" v-if="showPhases">
      <div class="phase">
        <div class="phase-label">
          <Icon name="lucide:zap" class="h-4 w-4" />
          <span>Core Layers</span>
          <Icon 
            v-if="coreComplete"
            name="lucide:check"
            class="h-4 w-4 text-success-500"
          />
        </div>
        <div class="mini-progress">
          <div 
            class="mini-progress-fill core"
            :style="{ width: `${coreProgress}%` }"
          />
        </div>
      </div>

      <div class="phase">
        <div class="phase-label">
          <Icon name="lucide:layers" class="h-4 w-4" />
          <span>Background</span>
          <Icon 
            v-if="backgroundComplete"
            name="lucide:check"
            class="h-4 w-4 text-success-500"
          />
        </div>
        <div class="mini-progress">
          <div 
            class="mini-progress-fill background"
            :style="{ width: `${backgroundProgress}%` }"
          />
        </div>
      </div>
    </div>

    <!-- Individual Layer Status -->
    <div v-if="showDetails" class="layer-details">
      <div class="layer-grid">
        <div 
          v-for="layer in visibleLayers"
          :key="layer.id"
          class="layer-item"
          :class="[
            `layer-${layer.status}`,
            `layer-${layer.type}`
          ]"
        >
          <div class="layer-icon">
            <Icon 
              v-if="layer.status === 'loaded'"
              name="lucide:check"
              class="h-4 w-4"
            />
            <Icon 
              v-else-if="layer.status === 'error'"
              name="lucide:x"
              class="h-4 w-4"
            />
            <Icon 
              v-else-if="layer.status === 'loading'"
              name="lucide:loader-2"
              class="h-4 w-4 animate-spin"
            />
            <Icon 
              v-else
              name="lucide:circle"
              class="h-4 w-4"
            />
          </div>
          
          <div class="layer-info">
            <div class="layer-name">{{ layer.name || layer.id }}</div>
            <div class="layer-progress">
              <div 
                class="layer-progress-bar"
                :style="{ width: `${layer.progress}%` }"
              />
            </div>
          </div>
          
          <div class="layer-meta">
            <span class="layer-type">{{ layer.type }}</span>
            <span v-if="layer.loadTime" class="layer-time">
              {{ formatTime(layer.loadTime) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Show More/Less -->
      <button 
        v-if="layers.length > maxVisibleLayers"
        @click="showAllLayers = !showAllLayers"
        class="show-more-btn"
      >
        <Icon 
          :name="showAllLayers ? 'lucide:chevron-up' : 'lucide:chevron-down'"
          class="h-4 w-4"
        />
        {{ showAllLayers ? 'Show Less' : `Show ${layers.length - maxVisibleLayers} More` }}
      </button>
    </div>

    <!-- Performance Stats -->
    <div v-if="showStats" class="performance-stats">
      <div class="stat">
        <Icon name="lucide:clock" class="h-4 w-4" />
        <span>Avg: {{ formatTime(averageLoadTime) }}</span>
      </div>
      <div class="stat">
        <Icon name="lucide:hard-drive" class="h-4 w-4" />
        <span>Cache: {{ cacheHitRate }}%</span>
      </div>
      <div class="stat">
        <Icon name="lucide:activity" class="h-4 w-4" />
        <span>Memory: {{ memoryUsage }}MB</span>
      </div>
      <div class="stat">
        <Icon name="lucide:database" class="h-4 w-4" />
        <span>Persistent: {{ persistentCacheItems }}</span>
      </div>
      <div class="stat">
        <Icon name="lucide:zap" class="h-4 w-4" />
        <span>Workers: {{ activeWorkers }}/{{ maxWorkers }}</span>
      </div>
    </div>

    <!-- Action Buttons -->
    <div v-if="showActions" class="loading-actions">
      <BaseButton
        v-if="canCancel"
        @click="$emit('cancel')"
        variant="outline"
        size="sm"
        class="cancel-btn"
      >
        <Icon name="lucide:x" class="h-4 w-4" />
        Cancel
      </BaseButton>
      
      <BaseButton
        v-if="canRetry"
        @click="$emit('retry')"
        variant="outline"
        size="sm"
        class="retry-btn"
      >
        <Icon name="lucide:refresh-cw" class="h-4 w-4" />
        Retry Failed
      </BaseButton>
      
      <BaseButton
        v-if="canOptimize"
        @click="$emit('optimize')"
        variant="outline"
        size="sm"
        class="optimize-btn"
      >
        <Icon name="lucide:zap" class="h-4 w-4" />
        Optimize
      </BaseButton>
    </div>
  </div>
</template>

<script setup lang="ts">
interface LayerState {
  id: string
  name?: string
  type: 'core' | 'background' | 'plugin' | 'optional'
  status: 'pending' | 'loading' | 'loaded' | 'error' | 'cancelled'
  progress: number
  loadTime?: number
  size?: number
  fromCache?: boolean
}

interface Props {
  title?: string
  layers: LayerState[]
  overallProgress: number
  coreProgress: number
  backgroundProgress: number
  coreComplete: boolean
  backgroundComplete: boolean
  averageLoadTime?: number
  cacheHitRate?: number
  memoryUsage?: number
  persistentCacheItems?: number
  activeWorkers?: number
  maxWorkers?: number
  showPhases?: boolean
  showDetails?: boolean
  showStats?: boolean
  showActions?: boolean
  canCancel?: boolean
  canRetry?: boolean
  canOptimize?: boolean
  maxVisibleLayers?: number
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Loading Project',
  showPhases: true,
  showDetails: false,
  showStats: false,
  showActions: false,
  canCancel: false,
  canRetry: false,
  canOptimize: false,
  maxVisibleLayers: 6,
  averageLoadTime: 0,
  cacheHitRate: 0,
  memoryUsage: 0,
  persistentCacheItems: 0,
  activeWorkers: 0,
  maxWorkers: 0
})

const emit = defineEmits<{
  cancel: []
  retry: []
  optimize: []
  toggleDetails: []
}>()

const showAllLayers = ref(false)

// Computed values
const loadedCount = computed(() => 
  props.layers.filter(l => l.status === 'loaded').length
)

const totalCount = computed(() => props.layers.length)

const statusVariant = computed(() => {
  if (props.coreComplete && props.backgroundComplete) return 'success'
  if (props.layers.some(l => l.status === 'error')) return 'destructive'
  if (props.coreComplete) return 'secondary'
  return 'outline'
})

const statusText = computed(() => {
  if (props.coreComplete && props.backgroundComplete) return 'Complete'
  if (props.layers.some(l => l.status === 'error')) return 'Errors'
  if (props.coreComplete) return 'Core Ready'
  return 'Loading'
})

const visibleLayers = computed(() => {
  if (showAllLayers.value || props.layers.length <= props.maxVisibleLayers) {
    return props.layers
  }
  return props.layers.slice(0, props.maxVisibleLayers)
})

// Utility functions
function formatTime(ms: number): string {
  if (ms < 1000) return `${Math.round(ms)}ms`
  return `${(ms / 1000).toFixed(1)}s`
}
</script>

<style scoped>
.progressive-loading-indicator {
  @apply space-y-4 p-4 bg-muted/30 rounded-lg border;
}

.loading-header {
  @apply flex items-center justify-between;
}

.loading-title {
  @apply flex items-center gap-2 font-medium;
}

.loading-status {
  @apply flex items-center gap-2;
}

.progress-container {
  @apply flex items-center gap-3;
}

.progress-bar {
  @apply flex-1 h-2 bg-muted rounded-full overflow-hidden;
}

.progress-fill {
  @apply h-full bg-primary rounded-full transition-all duration-300 ease-out;
}

.progress-text {
  @apply text-sm font-medium text-muted-foreground min-w-12 text-right;
}

.loading-phases {
  @apply space-y-2;
}

.phase {
  @apply space-y-1;
}

.phase-label {
  @apply flex items-center gap-2 text-sm;
}

.mini-progress {
  @apply h-1.5 bg-muted rounded-full overflow-hidden;
}

.mini-progress-fill {
  @apply h-full rounded-full transition-all duration-300;
}

.mini-progress-fill.core {
  @apply bg-primary;
}

.mini-progress-fill.background {
  @apply bg-secondary;
}

.layer-details {
  @apply space-y-3;
}

.layer-grid {
  @apply space-y-2;
}

.layer-item {
  @apply flex items-center gap-3 p-2 rounded-md border bg-background/50;
}

.layer-item.layer-core {
  @apply border-primary/20 bg-primary/5;
}

.layer-item.layer-background {
  @apply border-secondary/20 bg-secondary/5;
}

.layer-item.layer-loading {
  @apply border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950;
}

.layer-item.layer-loaded {
  @apply border-success-200 bg-success-50 dark:border-success-800 dark:bg-success-950;
}

.layer-item.layer-error {
  @apply border-destructive-200 bg-destructive-50 dark:border-destructive-800 dark:bg-destructive-950;
}

.layer-icon {
  @apply flex items-center justify-center w-6 h-6;
}

.layer-info {
  @apply flex-1 min-w-0;
}

.layer-name {
  @apply text-sm font-medium truncate;
}

.layer-progress {
  @apply h-1 bg-muted rounded-full overflow-hidden mt-1;
}

.layer-progress-bar {
  @apply h-full bg-current rounded-full transition-all duration-300;
}

.layer-meta {
  @apply flex flex-col items-end gap-1 text-xs text-muted-foreground;
}

.layer-type {
  @apply px-1.5 py-0.5 bg-muted rounded text-xs font-medium;
}

.show-more-btn {
  @apply flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors;
}

.performance-stats {
  @apply flex items-center gap-4 text-sm text-muted-foreground;
}

.stat {
  @apply flex items-center gap-1;
}

.loading-actions {
  @apply flex items-center gap-2 pt-2 border-t;
}

.cancel-btn {
  @apply text-destructive hover:text-destructive;
}

.retry-btn {
  @apply text-warning hover:text-warning;
}

.optimize-btn {
  @apply text-primary hover:text-primary;
}
</style>