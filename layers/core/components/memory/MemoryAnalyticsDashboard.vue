<template>
  <div class="memory-analytics-dashboard p-6 bg-white dark:bg-gray-900 rounded-lg shadow-lg">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Memory Analytics Dashboard
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Advanced memory management and optimization insights
        </p>
      </div>
      
      <div class="flex items-center space-x-3">
        <!-- System Health Badge -->
        <div class="flex items-center space-x-2">
          <div :class="[
            'w-3 h-3 rounded-full',
            systemHealthColor
          ]"></div>
          <span class="text-sm font-medium">{{ systemHealth }}% Health</span>
        </div>
        
        <!-- Refresh Button -->
        <button
          @click="refreshAnalytics"
          :disabled="isRefreshing"
          class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
        >
          <Icon
            name="lucide:refresh-cw"
            :class="['w-4 h-4', { 'animate-spin': isRefreshing }]"
          />
        </button>
      </div>
    </div>

    <!-- Key Metrics Row -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <!-- Memory Usage -->
      <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <div class="flex items-center justify-between">
          <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400">Memory Usage</h4>
          <Icon name="lucide:cpu" class="w-4 h-4 text-blue-500" />
        </div>
        <div class="mt-2">
          <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {{ formatMemory(memoryStats.usedJSHeapSize) }}
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            of {{ formatMemory(memoryBudget.maxMemoryMB) }} budget
          </div>
          <!-- Progress bar -->
          <div class="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              :class="['h-2 rounded-full transition-all duration-300', memoryUsageColor]"
              :style="{ width: `${Math.min(memoryUsagePercent, 100)}%` }"
            />
          </div>
        </div>
      </div>

      <!-- Active Layers -->
      <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <div class="flex items-center justify-between">
          <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Layers</h4>
          <Icon name="lucide:layers" class="w-4 h-4 text-green-500" />
        </div>
        <div class="mt-2">
          <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {{ layerCount }}
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            of {{ memoryBudget.maxLayers }} max
          </div>
        </div>
      </div>

      <!-- Efficiency Score -->
      <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <div class="flex items-center justify-between">
          <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400">Efficiency</h4>
          <Icon name="lucide:zap" class="w-4 h-4 text-yellow-500" />
        </div>
        <div class="mt-2">
          <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {{ Math.round(memoryEfficiency) }}%
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            Memory efficiency
          </div>
        </div>
      </div>

      <!-- Compression Ratio -->
      <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <div class="flex items-center justify-between">
          <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400">Compression</h4>
          <Icon name="lucide:archive" class="w-4 h-4 text-purple-500" />
        </div>
        <div class="mt-2">
          <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {{ Math.round(analytics.compressionRatio * 100) }}%
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            Average compression
          </div>
        </div>
      </div>
    </div>

    <!-- Memory Budget Selection -->
    <div class="mb-6">
      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Memory Budget</h4>
      <div class="grid grid-cols-4 gap-2">
        <button
          v-for="(budget, key) in MEMORY_BUDGETS"
          :key="key"
          @click="setMemoryBudget(key)"
          :class="[
            'p-3 rounded-lg border-2 transition-colors text-left',
            memoryBudget.projectType === key
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
              : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
          ]"
        >
          <div class="font-medium text-sm capitalize">{{ key }}</div>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            {{ formatMemory(budget.maxMemoryMB) }} max
          </div>
        </button>
      </div>
    </div>

    <!-- Usage Patterns Chart -->
    <div class="mb-6" v-if="memoryHistory.length > 0">
      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Memory Usage History</h4>
      <div class="h-32 bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <svg
          class="w-full h-full"
          viewBox="0 0 400 100"
          preserveAspectRatio="none"
        >
          <!-- Memory usage line -->
          <polyline
            :points="memoryHistoryPoints"
            fill="none"
            stroke="rgb(59, 130, 246)"
            stroke-width="2"
            class="drop-shadow-sm"
          />
          
          <!-- Warning threshold line -->
          <line
            :y1="warningThresholdY"
            :y2="warningThresholdY"
            x1="0"
            x2="400"
            stroke="rgb(245, 158, 11)"
            stroke-width="1"
            stroke-dasharray="5,5"
            opacity="0.7"
          />
          
          <!-- Critical threshold line -->
          <line
            :y1="criticalThresholdY"
            :y2="criticalThresholdY"
            x1="0"
            x2="400"
            stroke="rgb(239, 68, 68)"
            stroke-width="1"
            stroke-dasharray="5,5"
            opacity="0.7"
          />
        </svg>
      </div>
      <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
        <span>{{ formatTime(oldestTimestamp) }}</span>
        <span>Now</span>
      </div>
    </div>

    <!-- Recommendations -->
    <div class="mb-6" v-if="analytics.recommendations.length > 0">
      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
        Optimization Recommendations
      </h4>
      <div class="space-y-3">
        <div
          v-for="(rec, index) in analytics.recommendations.slice(0, 3)"
          :key="index"
          :class="[
            'p-4 rounded-lg border-l-4',
            rec.priority === 'high' ? 'border-red-500 bg-red-50 dark:bg-red-900/20' :
            rec.priority === 'medium' ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20' :
            'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
          ]"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-2">
                <Icon
                  :name="getRecommendationIcon(rec.type)"
                  class="w-4 h-4 text-gray-600 dark:text-gray-400"
                />
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {{ rec.description }}
                </span>
              </div>
              <div class="mt-1 text-xs text-gray-600 dark:text-gray-400">
                Potential savings: {{ formatMemory(rec.potentialSavings) }} •
                Difficulty: {{ rec.implementationComplexity }}
              </div>
            </div>
            <button
              @click="executeRecommendation(rec)"
              :disabled="executingRecommendation === index"
              class="ml-4 px-3 py-1 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 rounded transition-colors"
            >
              <Icon
                v-if="executingRecommendation === index"
                name="lucide:loader-2"
                class="w-3 h-3 animate-spin"
              />
              <span v-else>Apply</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Memory Leaks Warning -->
    <div v-if="analytics.memoryLeaks.length > 0" class="mb-6">
      <h4 class="text-sm font-medium text-red-700 dark:text-red-300 mb-3">
        ⚠️ Potential Memory Leaks Detected
      </h4>
      <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
        <div class="space-y-2">
          <div
            v-for="leak in analytics.memoryLeaks.slice(0, 3)"
            :key="leak.layerId"
            class="text-sm text-red-800 dark:text-red-200"
          >
            Layer {{ leak.layerId }}: {{ formatMemory(leak.memoryGrowth) }} growth
            (Score: {{ leak.leakScore }})
          </div>
        </div>
        <button
          @click="handleMemoryLeaks"
          class="mt-3 px-3 py-1 text-xs font-medium text-white bg-red-600 hover:bg-red-700 rounded transition-colors"
        >
          Fix Memory Leaks
        </button>
      </div>
    </div>

    <!-- Layer Usage Patterns -->
    <div v-if="topUsagePatterns.length > 0">
      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
        Top Memory Consumers
      </h4>
      <div class="space-y-2">
        <div
          v-for="pattern in topUsagePatterns"
          :key="pattern.layerId"
          class="flex items-center justify-between py-2 px-3 bg-gray-50 dark:bg-gray-800 rounded"
        >
          <div class="flex items-center space-x-3">
            <div :class="[
              'w-2 h-2 rounded-full',
              pattern.memoryImportance === 'critical' ? 'bg-red-500' :
              pattern.memoryImportance === 'high' ? 'bg-orange-500' :
              pattern.memoryImportance === 'medium' ? 'bg-yellow-500' :
              'bg-gray-500'
            ]"></div>
            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
              {{ pattern.layerId }}
            </span>
            <span class="text-xs text-gray-500 dark:text-gray-400">
              {{ pattern.accessFrequency }} accesses
            </span>
          </div>
          <div class="text-xs text-gray-600 dark:text-gray-400">
            Next access: {{ formatRelativeTime(pattern.predictedNextAccess) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useAdvancedMemoryManager, MEMORY_BUDGETS } from '../composables/useAdvancedMemoryManager'
import type { MemoryRecommendation } from '../composables/useAdvancedMemoryManager'

interface Props {
  projectType?: keyof typeof MEMORY_BUDGETS
  autoRefresh?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  projectType: 'medium',
  autoRefresh: true
})

// Initialize advanced memory manager
const memoryManager = useAdvancedMemoryManager()

// Local state
const isRefreshing = ref(false)
const executingRecommendation = ref<number | null>(null)

// Destructure memory manager
const {
  memoryStats,
  memoryBudget,
  analytics,
  memoryEfficiency,
  systemHealth,
  memoryHistory,
  setMemoryBudget,
  performIntelligentCleanup,
  generateRecommendations,
  layerRegistry
} = memoryManager

// Set initial budget
setMemoryBudget(props.projectType)

// Computed properties
const layerCount = computed(() => layerRegistry.value.size)

const memoryUsagePercent = computed(() => 
  (memoryStats.value.usedJSHeapSize / memoryBudget.value.maxMemoryMB) * 100
)

const systemHealthColor = computed(() => {
  const health = systemHealth.value
  if (health >= 80) return 'bg-green-500'
  if (health >= 60) return 'bg-yellow-500'
  if (health >= 40) return 'bg-orange-500'
  return 'bg-red-500'
})

const memoryUsageColor = computed(() => {
  const percent = memoryUsagePercent.value
  if (percent >= 90) return 'bg-red-500'
  if (percent >= 75) return 'bg-orange-500'
  if (percent >= 50) return 'bg-yellow-500'
  return 'bg-blue-500'
})

const topUsagePatterns = computed(() => {
  return Array.from(memoryManager.usagePatterns.value.values())
    .sort((a, b) => b.accessFrequency - a.accessFrequency)
    .slice(0, 5)
})

// Memory history chart data
const memoryHistoryPoints = computed(() => {
  const history = memoryHistory.value
  if (history.length < 2) return ''
  
  const maxMemory = Math.max(...history.map(h => h.usage))
  const minMemory = Math.min(...history.map(h => h.usage))
  const range = maxMemory - minMemory || 1
  
  return history.map((point, index) => {
    const x = (index / (history.length - 1)) * 400
    const y = 100 - ((point.usage - minMemory) / range) * 80 // Leave 20% margin
    return `${x},${y}`
  }).join(' ')
})

const oldestTimestamp = computed(() => {
  const history = memoryHistory.value
  return history.length > 0 ? history[0].timestamp : Date.now()
})

const warningThresholdY = computed(() => {
  const warning = memoryBudget.value.warningThresholdMB
  const max = memoryBudget.value.maxMemoryMB
  return 100 - (warning / max) * 80
})

const criticalThresholdY = computed(() => {
  const critical = memoryBudget.value.criticalThresholdMB
  const max = memoryBudget.value.maxMemoryMB
  return 100 - (critical / max) * 80
})

// Methods
function formatMemory(mb: number): string {
  if (mb < 1) {
    return `${Math.round(mb * 1024)} KB`
  } else if (mb < 1024) {
    return `${Math.round(mb)} MB`
  } else {
    return `${(mb / 1024).toFixed(1)} GB`
  }
}

function formatTime(timestamp: number): string {
  return new Date(timestamp).toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

function formatRelativeTime(timestamp: number): string {
  const now = Date.now()
  const diff = timestamp - now
  
  if (diff < 0) return 'past'
  if (diff < 60000) return `${Math.round(diff / 1000)}s`
  if (diff < 3600000) return `${Math.round(diff / 60000)}m`
  return `${Math.round(diff / 3600000)}h`
}

function getRecommendationIcon(type: string): string {
  switch (type) {
    case 'cleanup': return 'lucide:trash-2'
    case 'compression': return 'lucide:archive'
    case 'virtualization': return 'lucide:layers'
    case 'budget': return 'lucide:trending-up'
    case 'preload': return 'lucide:download'
    default: return 'lucide:lightbulb'
  }
}

async function refreshAnalytics() {
  isRefreshing.value = true
  try {
    // Trigger analytics refresh
    generateRecommendations()
    await new Promise(resolve => setTimeout(resolve, 500)) // Visual feedback
  } finally {
    isRefreshing.value = false
  }
}

async function executeRecommendation(rec: MemoryRecommendation) {
  const index = analytics.value.recommendations.indexOf(rec)
  executingRecommendation.value = index
  
  try {
    await rec.action()
    // Refresh recommendations after execution
    await refreshAnalytics()
  } catch (error) {
    console.error('Failed to execute recommendation:', error)
  } finally {
    executingRecommendation.value = null
  }
}

async function handleMemoryLeaks() {
  // Force cleanup of all leaked layers
  for (const leak of analytics.value.memoryLeaks) {
    memoryManager.unregisterLayer(leak.layerId)
  }
  
  await refreshAnalytics()
}
</script>

<style scoped>
.memory-analytics-dashboard {
  font-family: system-ui, -apple-system, sans-serif;
}
</style>