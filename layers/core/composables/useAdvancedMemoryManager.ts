/**
 * Advanced Memory Management System
 * Enhanced memory management with intelligent cleanup, predictive analytics, and optimization recommendations
 */

import { ref, computed, watch, onUnmounted } from 'vue'
import { useMemoryManager, type MemoryStats, type LayerMemoryInfo } from './useMemoryManager'
import type { MemoryThresholds, MemoryManagerOptions } from './useMemoryManager'

export interface MemoryUsagePattern {
  layerId: string
  accessFrequency: number
  lastAccessTime: number
  accessTimes: number[]
  averageSessionTime: number
  memoryImportance: 'critical' | 'high' | 'medium' | 'low'
  predictedNextAccess: number
  decompressionCost: number
}

export interface MemoryBudget {
  projectType: 'small' | 'medium' | 'large' | 'enterprise'
  maxMemoryMB: number
  warningThresholdMB: number
  criticalThresholdMB: number
  maxLayers: number
  compressionLevel: number
  preloadStrategy: 'conservative' | 'balanced' | 'aggressive'
}

export interface MemoryAnalytics {
  totalSessions: number
  averageSessionMemory: number
  peakMemoryUsage: number
  cleanupFrequency: number
  compressionRatio: number
  gcTriggerCount: number
  performanceImpact: number
  memoryLeaks: MemoryLeak[]
  recommendations: MemoryRecommendation[]
}

export interface MemoryLeak {
  layerId: string
  createdAt: number
  lastCleanupAttempt: number
  memoryGrowth: number
  leakScore: number
}

export interface MemoryRecommendation {
  type: 'compression' | 'cleanup' | 'preload' | 'virtualization' | 'budget'
  priority: 'high' | 'medium' | 'low'
  description: string
  potentialSavings: number
  implementationComplexity: 'easy' | 'medium' | 'hard'
  action: () => Promise<void>
}

export interface SmartCleanupOptions {
  enablePredictiveCleanup?: boolean
  enableUsagePatternAnalysis?: boolean
  enableMemoryLeakDetection?: boolean
  enableSmartPrefetching?: boolean
  learningWindowSize?: number
  cleanupAggressiveness?: number
}

// Pre-defined memory budgets for different project types
const MEMORY_BUDGETS: Record<string, MemoryBudget> = {
  small: {
    projectType: 'small',
    maxMemoryMB: 256,
    warningThresholdMB: 128,
    criticalThresholdMB: 192,
    maxLayers: 50,
    compressionLevel: 70,
    preloadStrategy: 'conservative'
  },
  medium: {
    projectType: 'medium',
    maxMemoryMB: 512,
    warningThresholdMB: 256,
    criticalThresholdMB: 384,
    maxLayers: 100,
    compressionLevel: 60,
    preloadStrategy: 'balanced'
  },
  large: {
    projectType: 'large',
    maxMemoryMB: 1024,
    warningThresholdMB: 512,
    criticalThresholdMB: 768,
    maxLayers: 200,
    compressionLevel: 50,
    preloadStrategy: 'balanced'
  },
  enterprise: {
    projectType: 'enterprise',
    maxMemoryMB: 2048,
    warningThresholdMB: 1024,
    criticalThresholdMB: 1536,
    maxLayers: 500,
    compressionLevel: 40,
    preloadStrategy: 'aggressive'
  }
}

/**
 * Enhanced Memory Management with AI-driven optimization
 */
export function useAdvancedMemoryManager(
  baseOptions: MemoryManagerOptions = {},
  smartOptions: SmartCleanupOptions = {}
) {
  const {
    enablePredictiveCleanup = true,
    enableUsagePatternAnalysis = true,
    enableMemoryLeakDetection = true,
    enableSmartPrefetching = true,
    learningWindowSize = 100,
    cleanupAggressiveness = 0.7
  } = smartOptions

  // Base memory manager
  const baseMemoryManager = useMemoryManager(baseOptions)

  // Advanced state
  const usagePatterns = ref<Map<string, MemoryUsagePattern>>(new Map())
  const memoryBudget = ref<MemoryBudget>(MEMORY_BUDGETS.medium)
  const analytics = ref<MemoryAnalytics>({
    totalSessions: 0,
    averageSessionMemory: 0,
    peakMemoryUsage: 0,
    cleanupFrequency: 0,
    compressionRatio: 0,
    gcTriggerCount: 0,
    performanceImpact: 0,
    memoryLeaks: [],
    recommendations: []
  })
  
  const sessionStartTime = ref(Date.now())
  const memoryHistory = ref<{ timestamp: number; usage: number }[]>([])
  const cleanupHistory = ref<{ timestamp: number; layersRemoved: number; memoryFreed: number }[]>([])

  // Computed analytics
  const memoryEfficiency = computed(() => {
    const current = baseMemoryManager.memoryStats.value.usedJSHeapSize
    const budget = memoryBudget.value.maxMemoryMB
    return Math.max(0, 100 - (current / budget) * 100)
  })

  const systemHealth = computed(() => {
    const stats = baseMemoryManager.memoryStats.value
    const budget = memoryBudget.value
    
    let score = 100
    
    // Memory usage penalty
    if (stats.usedJSHeapSize > budget.warningThresholdMB) {
      score -= 30
    }
    if (stats.usedJSHeapSize > budget.criticalThresholdMB) {
      score -= 50
    }
    
    // Layer count penalty
    const layerCount = baseMemoryManager.layerRegistry.value.size
    if (layerCount > budget.maxLayers * 0.8) {
      score -= 20
    }
    
    // Memory leak penalty
    score -= analytics.value.memoryLeaks.length * 10
    
    return Math.max(0, score)
  })

  const nextRecommendedAction = computed((): MemoryRecommendation | null => {
    const recs = analytics.value.recommendations
    return recs.find(r => r.priority === 'high') || recs[0] || null
  })

  /**
   * Set memory budget based on project type
   */
  function setMemoryBudget(projectType: keyof typeof MEMORY_BUDGETS | MemoryBudget) {
    if (typeof projectType === 'string') {
      memoryBudget.value = { ...MEMORY_BUDGETS[projectType] }
    } else {
      memoryBudget.value = { ...projectType }
    }
    
    // Update base memory manager thresholds
    const budget = memoryBudget.value
    baseMemoryManager.stopMonitoring()
    
    // Reinitialize with new thresholds
    const newManager = useMemoryManager({
      ...baseOptions,
      thresholds: {
        warning: budget.warningThresholdMB,
        critical: budget.criticalThresholdMB,
        maximum: budget.maxMemoryMB
      }
    })
    
    // Copy state from old manager
    newManager.layerRegistry.value = baseMemoryManager.layerRegistry.value
  }

  /**
   * Record layer access for pattern analysis
   */
  function recordLayerAccess(layerId: string, decompressionTime?: number) {
    if (!enableUsagePatternAnalysis) return

    const now = Date.now()
    let pattern = usagePatterns.value.get(layerId)
    
    if (!pattern) {
      pattern = {
        layerId,
        accessFrequency: 1,
        lastAccessTime: now,
        accessTimes: [now],
        averageSessionTime: 0,
        memoryImportance: 'medium',
        predictedNextAccess: now + 300000, // Default 5 minutes
        decompressionCost: decompressionTime || 0
      }
    } else {
      pattern.accessFrequency++
      pattern.lastAccessTime = now
      pattern.accessTimes.push(now)
      pattern.decompressionCost = Math.max(pattern.decompressionCost, decompressionTime || 0)
      
      // Keep only recent access times
      const cutoff = now - 24 * 60 * 60 * 1000 // 24 hours
      pattern.accessTimes = pattern.accessTimes.filter(time => time > cutoff)
      
      // Calculate average session time
      if (pattern.accessTimes.length > 1) {
        const intervals = []
        for (let i = 1; i < pattern.accessTimes.length; i++) {
          intervals.push(pattern.accessTimes[i] - pattern.accessTimes[i - 1])
        }
        pattern.averageSessionTime = intervals.reduce((a, b) => a + b, 0) / intervals.length
      }
      
      // Predict next access using exponential moving average
      if (pattern.accessTimes.length >= 3) {
        const recentIntervals = pattern.accessTimes.slice(-3).map((time, i, arr) => 
          i > 0 ? time - arr[i - 1] : 0
        ).filter(interval => interval > 0)
        
        if (recentIntervals.length > 0) {
          const avgInterval = recentIntervals.reduce((a, b) => a + b, 0) / recentIntervals.length
          pattern.predictedNextAccess = now + avgInterval
        }
      }
      
      // Determine memory importance
      const frequency = pattern.accessFrequency
      const recency = now - pattern.lastAccessTime
      const sessionTime = pattern.averageSessionTime
      
      if (frequency > 20 && recency < 300000) { // Frequent and recent
        pattern.memoryImportance = 'critical'
      } else if (frequency > 10 && recency < 600000) {
        pattern.memoryImportance = 'high'
      } else if (frequency > 5 && recency < 1800000) {
        pattern.memoryImportance = 'medium'
      } else {
        pattern.memoryImportance = 'low'
      }
    }
    
    usagePatterns.value.set(layerId, pattern)
  }

  /**
   * Intelligent cleanup based on usage patterns and predictions
   */
  async function performIntelligentCleanup(): Promise<{
    layersRemoved: number
    memoryFreed: number
    recommendations: MemoryRecommendation[]
  }> {
    const now = Date.now()
    const layersToRemove: string[] = []
    const recommendations: MemoryRecommendation[] = []
    let memoryFreed = 0

    // Get current memory state
    const currentMemory = baseMemoryManager.memoryStats.value.usedJSHeapSize
    const targetMemory = memoryBudget.value.warningThresholdMB * cleanupAggressiveness

    if (currentMemory <= targetMemory) {
      return { layersRemoved: 0, memoryFreed: 0, recommendations: [] }
    }

    // Score layers for removal
    const layerScores: { layerId: string; score: number; memorySize: number }[] = []
    
    baseMemoryManager.layerRegistry.value.forEach((layer, layerId) => {
      const pattern = usagePatterns.value.get(layerId)
      let score = 0
      
      // Base score from age
      const age = now - layer.lastAccessed
      score += Math.min(age / (60 * 60 * 1000), 10) // Max 10 points for age (1 hour = 10 points)
      
      // Adjust score based on usage patterns
      if (pattern) {
        // Importance penalty
        switch (pattern.memoryImportance) {
          case 'critical': score -= 50; break
          case 'high': score -= 20; break
          case 'medium': score -= 5; break
          case 'low': break
        }
        
        // Predicted access penalty
        const timeToNextAccess = pattern.predictedNextAccess - now
        if (timeToNextAccess < 300000) { // Next access within 5 minutes
          score -= 15
        }
        
        // Decompression cost penalty (expensive to decompress = keep in memory)
        score -= Math.min(pattern.decompressionCost / 100, 5)
      }
      
      // Layer-specific penalties
      if (!layer.isVisible) score += 5 // Hidden layers are good candidates
      if (layer.priority === 'low') score += 10
      if (layer.compressedData) score += 3 // Already compressed
      
      layerScores.push({ layerId, score, memorySize: layer.size })
    })

    // Sort by score (highest first = best candidates for removal)
    layerScores.sort((a, b) => b.score - a.score)

    // Remove layers until we reach target memory
    for (const { layerId, memorySize } of layerScores) {
      if (currentMemory - memoryFreed <= targetMemory) break
      
      const layer = baseMemoryManager.layerRegistry.value.get(layerId)
      if (!layer) continue
      
      // Skip critical layers
      const pattern = usagePatterns.value.get(layerId)
      if (pattern?.memoryImportance === 'critical') continue
      
      layersToRemove.push(layerId)
      memoryFreed += memorySize / 1024 / 1024 // Convert to MB
      
      // Stop if we've removed enough
      if (layersToRemove.length >= 20) break // Limit batch size
    }

    // Execute removals
    for (const layerId of layersToRemove) {
      baseMemoryManager.unregisterLayer(layerId)
      usagePatterns.value.delete(layerId)
    }

    // Record cleanup
    cleanupHistory.value.push({
      timestamp: now,
      layersRemoved: layersToRemove.length,
      memoryFreed
    })

    // Keep cleanup history limited
    if (cleanupHistory.value.length > 50) {
      cleanupHistory.value = cleanupHistory.value.slice(-50)
    }

    // Generate recommendations
    if (memoryFreed < 50 && currentMemory > memoryBudget.value.criticalThresholdMB) {
      recommendations.push({
        type: 'budget',
        priority: 'high',
        description: 'Consider upgrading to a larger memory budget for your project size',
        potentialSavings: 0,
        implementationComplexity: 'easy',
        action: async () => {
          const nextBudget = getNextLargerBudget(memoryBudget.value.projectType)
          if (nextBudget) {
            setMemoryBudget(nextBudget)
          }
        }
      })
    }

    if (layersToRemove.length < 5 && baseMemoryManager.layerRegistry.value.size > 50) {
      recommendations.push({
        type: 'compression',
        priority: 'medium',
        description: 'Increase compression level for better memory efficiency',
        potentialSavings: baseMemoryManager.totalLayersSize.value * 0.3,
        implementationComplexity: 'easy',
        action: async () => {
          // Implement higher compression
          for (const [layerId, layer] of baseMemoryManager.layerRegistry.value) {
            if (!layer.isVisible && layer.compressionLevel < 70) {
              await baseMemoryManager.updateLayerVisibility(layerId, false)
            }
          }
        }
      })
    }

    return { layersRemoved: layersToRemove.length, memoryFreed, recommendations }
  }

  /**
   * Detect and track memory leaks
   */
  function detectMemoryLeaks(): MemoryLeak[] {
    if (!enableMemoryLeakDetection) return []

    const now = Date.now()
    const leaks: MemoryLeak[] = []
    const memoryGrowthThreshold = 50 // MB

    // Check for layers that should have been cleaned up but weren't
    baseMemoryManager.layerRegistry.value.forEach((layer, layerId) => {
      const age = now - layer.lastAccessed
      const pattern = usagePatterns.value.get(layerId)
      
      // Layer is old, low priority, but still in memory
      if (
        age > 10 * 60 * 1000 && // 10 minutes old
        layer.priority === 'low' &&
        !layer.isVisible &&
        (!pattern || pattern.memoryImportance === 'low')
      ) {
        const existingLeak = analytics.value.memoryLeaks.find(leak => leak.layerId === layerId)
        
        if (existingLeak) {
          existingLeak.memoryGrowth += layer.size / 1024 / 1024
          existingLeak.leakScore++
        } else {
          leaks.push({
            layerId,
            createdAt: layer.lastAccessed,
            lastCleanupAttempt: now,
            memoryGrowth: layer.size / 1024 / 1024,
            leakScore: 1
          })
        }
      }
    })

    return leaks
  }

  /**
   * Smart prefetching based on usage patterns
   */
  async function performSmartPrefetching(): Promise<string[]> {
    if (!enableSmartPrefetching) return []

    const now = Date.now()
    const prefetchCandidates: string[] = []

    // Find layers likely to be accessed soon
    usagePatterns.value.forEach((pattern, layerId) => {
      const timeToNextAccess = pattern.predictedNextAccess - now
      
      // Prefetch if predicted access is within next 2 minutes
      if (
        timeToNextAccess > 0 && 
        timeToNextAccess < 120000 &&
        pattern.memoryImportance !== 'low' &&
        !baseMemoryManager.layerRegistry.value.has(layerId)
      ) {
        prefetchCandidates.push(layerId)
      }
    })

    // Limit prefetching based on current memory usage
    const currentMemory = baseMemoryManager.memoryStats.value.usedJSHeapSize
    const availableMemory = memoryBudget.value.warningThresholdMB - currentMemory
    
    if (availableMemory < 50) { // Less than 50MB available
      return []
    }

    // Return candidates (actual prefetching would be implemented by the calling code)
    return prefetchCandidates.slice(0, Math.min(5, Math.floor(availableMemory / 10)))
  }

  /**
   * Generate optimization recommendations
   */
  function generateRecommendations(): MemoryRecommendation[] {
    const recommendations: MemoryRecommendation[] = []
    const stats = baseMemoryManager.memoryStats.value
    const layerCount = baseMemoryManager.layerRegistry.value.size

    // High memory usage recommendations
    if (stats.usedJSHeapSize > memoryBudget.value.warningThresholdMB) {
      recommendations.push({
        type: 'cleanup',
        priority: 'high',
        description: `Memory usage is ${Math.round(stats.usedJSHeapSize)}MB. Cleanup recommended.`,
        potentialSavings: baseMemoryManager.totalLayersSize.value * 0.3,
        implementationComplexity: 'easy',
        action: performIntelligentCleanup
      })
    }

    // Too many layers recommendation
    if (layerCount > memoryBudget.value.maxLayers * 0.8) {
      recommendations.push({
        type: 'virtualization',
        priority: 'medium',
        description: `${layerCount} layers detected. Enable layer virtualization for better performance.`,
        potentialSavings: layerCount * 0.1, // Rough estimate
        implementationComplexity: 'medium',
        action: async () => {
          // Implementation would depend on the specific layer system
          console.log('Enabling layer virtualization...')
        }
      })
    }

    // Compression recommendations
    const uncompressedLayers = Array.from(baseMemoryManager.layerRegistry.value.values())
      .filter(layer => !layer.isVisible && !layer.compressedData)
    
    if (uncompressedLayers.length > 10) {
      recommendations.push({
        type: 'compression',
        priority: 'medium',
        description: `${uncompressedLayers.length} hidden layers could be compressed.`,
        potentialSavings: uncompressedLayers.reduce((sum, layer) => sum + layer.size, 0) / 1024 / 1024 * 0.6,
        implementationComplexity: 'easy',
        action: async () => {
          for (const layer of uncompressedLayers.slice(0, 20)) {
            await baseMemoryManager.updateLayerVisibility(layer.id, false)
          }
        }
      })
    }

    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })
  }

  // Helper function
  function getNextLargerBudget(currentType: string): keyof typeof MEMORY_BUDGETS | null {
    const order: (keyof typeof MEMORY_BUDGETS)[] = ['small', 'medium', 'large', 'enterprise']
    const currentIndex = order.indexOf(currentType as keyof typeof MEMORY_BUDGETS)
    return currentIndex >= 0 && currentIndex < order.length - 1 ? order[currentIndex + 1] : null
  }

  // Enhanced layer registration that records usage patterns
  const enhancedRegisterLayer = async (
    id: string,
    type: LayerMemoryInfo['type'],
    data: any,
    options: Parameters<typeof baseMemoryManager.registerLayer>[3] = {}
  ) => {
    await baseMemoryManager.registerLayer(id, type, data, options)
    recordLayerAccess(id)
  }

  // Enhanced layer access that records patterns
  const enhancedAccessLayer = async (id: string) => {
    const startTime = performance.now()
    const result = await baseMemoryManager.accessLayer(id)
    const endTime = performance.now()
    
    recordLayerAccess(id, endTime - startTime)
    return result
  }

  // Periodic analytics update
  const analyticsTimer = setInterval(() => {
    const now = Date.now()
    
    // Update memory history
    memoryHistory.value.push({
      timestamp: now,
      usage: baseMemoryManager.memoryStats.value.usedJSHeapSize
    })
    
    // Keep history limited to last 24 hours
    const cutoff = now - 24 * 60 * 60 * 1000
    memoryHistory.value = memoryHistory.value.filter(entry => entry.timestamp > cutoff)
    
    // Update analytics
    analytics.value.memoryLeaks = detectMemoryLeaks()
    analytics.value.recommendations = generateRecommendations()
    
    // Update peak memory
    analytics.value.peakMemoryUsage = Math.max(
      analytics.value.peakMemoryUsage,
      baseMemoryManager.memoryStats.value.usedJSHeapSize
    )
    
    // Trigger predictive cleanup if enabled
    if (enablePredictiveCleanup && baseMemoryManager.needsCleanup.value) {
      performIntelligentCleanup().catch(console.error)
    }
  }, 30000) // Every 30 seconds

  // Cleanup on unmount
  onUnmounted(() => {
    clearInterval(analyticsTimer)
  })

  return {
    // Base memory manager
    ...baseMemoryManager,
    
    // Enhanced methods
    registerLayer: enhancedRegisterLayer,
    accessLayer: enhancedAccessLayer,
    
    // Advanced state
    memoryBudget: computed(() => memoryBudget.value),
    usagePatterns: computed(() => usagePatterns.value),
    analytics: computed(() => analytics.value),
    memoryEfficiency,
    systemHealth,
    nextRecommendedAction,
    
    // Advanced methods
    setMemoryBudget,
    performIntelligentCleanup,
    detectMemoryLeaks,
    performSmartPrefetching,
    generateRecommendations,
    recordLayerAccess,
    
    // Analytics
    memoryHistory: computed(() => memoryHistory.value),
    cleanupHistory: computed(() => cleanupHistory.value)
  }
}

// Export utilities
export { MEMORY_BUDGETS }
export type { 
  MemoryUsagePattern, 
  MemoryBudget, 
  MemoryAnalytics, 
  MemoryRecommendation,
  MemoryLeak,
  SmartCleanupOptions
}