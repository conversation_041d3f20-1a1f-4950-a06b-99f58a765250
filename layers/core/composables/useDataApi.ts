import { ref, Ref } from 'vue'
import { v4 as uuidv4 } from 'uuid'
import { useToaster } from './toaster'

/**
 * Shared API module for standardized data operations
 */
export const useDataApi = () => {
  const { show } = useToaster()

  const isLoading: Ref<boolean> = ref(false)
  const error: Ref<string | null> = ref(null)

  /**
   * Create a new entity using the server API
   * @param collectionName The collection name
   * @param data The data to create
   * @param options Additional options
   * @returns The created entity with ID
   */
  const create = async <T>(
    collectionName: string,
    data: Partial<T>,
    options: {
      showSuccessToast?: boolean
      showErrorToast?: boolean
      customSuccessMessage?: string
      customErrorMessage?: string
      workspaceId?: string
      profileId?: string
      embed?: string[]
    } = {}
  ): Promise<T & { id: string }> => {
    const {
      showSuccessToast = true,
      showErrorToast = true,
      customSuccessMessage,
      customErrorMessage,
      workspaceId,
      profileId,
      embed
    } = options

    isLoading.value = true
    error.value = null

    try {
      const workspaceIds = data.workspace_ids as string[] || []
      const profileIds = data.profile_ids as string[] || []

      // Add current workspace and profile if provided
      if (workspaceId && !workspaceIds.includes(workspaceId)) {
        workspaceIds.push(workspaceId)
      }

      if (profileId && !profileIds.includes(profileId)) {
        profileIds.push(profileId)
      }

      const newId = uuidv4()

      const newData = {
        ...data,
        id: newId
      } as T & { id: string }

      // Call the server API endpoint
      const response = await $fetch('/api/data/write', {
        method: 'POST',
        body: {
          collection: collectionName,
          data: newData,
          embed
        }
      })

      if (showSuccessToast) {
        show({
          title: customSuccessMessage || 'Created successfully',
          type: 'success'
        })
      }

      return {
        ...newData,
        id: newId
      } as T & { id: string }
    } catch (err: any) {
      console.error(`Error creating ${collectionName}:`, err)
      error.value = err.message || 'Failed to create'

      if (showErrorToast) {
        show({
          title: customErrorMessage || `Failed to create: ${error.value}`,
          type: 'danger'
        })
      }

      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Update an existing entity using the server API
   * @param collectionName The collection name
   * @param id The entity ID
   * @param data The data to update
   * @param options Additional options
   * @returns The updated entity
   */
  const update = async <T>(
    collectionName: string,
    id: string,
    data: Partial<T>,
    options: {
      showSuccessToast?: boolean
      showErrorToast?: boolean
      customSuccessMessage?: string
      customErrorMessage?: string
      workspaceId?: string
      profileId?: string
      embed?: string[]
    } = {}
  ): Promise<T & { id: string }> => {
    const {
      showSuccessToast = true,
      showErrorToast = true,
      customSuccessMessage,
      customErrorMessage,
      workspaceId,
      profileId,
      embed
    } = options

    isLoading.value = true
    error.value = null

    try {
      // First get the current entity to ensure it exists and to get current data
      const currentEntity = await getById<T>(collectionName, id, {
        showErrorToast: false,
        includeDeleted: false
      })

      if (!currentEntity) {
        throw new Error(`${collectionName} not found with ID: ${id}`)
      }

      // Update workspace_ids and profile_ids if provided
      const existingData = currentEntity as T & {
        id: string
        workspace_ids?: string[]
        profile_ids?: string[]
      }

      let workspaceIds = existingData.workspace_ids || []
      let profileIds = existingData.profile_ids || []

      if (workspaceId && !workspaceIds.includes(workspaceId)) {
        workspaceIds.push(workspaceId)
      }

      if (profileId && !profileIds.includes(profileId)) {
        profileIds.push(profileId)
      }

      const updateData = {
        ...data,
        workspace_ids: workspaceIds,
        profile_ids: profileIds,
        updated_at: new Date().toISOString()
      }

      // Call the server API endpoint
      const response = await $fetch('/api/data/update', {
        method: 'POST',
        body: {
          collection: collectionName,
          id,
          data: updateData,
          embed
        }
      })

      if (showSuccessToast) {
        show({
          title: customSuccessMessage || 'Updated successfully',
          type: 'success'
        })
      }

      // Return the updated entity
      return {
        ...existingData,
        ...updateData,
        id
      } as T & { id: string }
    } catch (err: any) {
      console.error(`Error updating ${collectionName}:`, err)
      error.value = err.message || 'Failed to update'

      if (showErrorToast) {
        show({
          title: customErrorMessage || `Failed to update: ${error.value}`,
          type: 'danger'
        })
      }

      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Delete an entity using the server API (soft delete by setting deleted_at)
   * @param collectionName The collection name
   * @param id The entity ID
   * @param options Additional options
   * @returns The deleted entity ID
   */
  const remove = async (
    collectionName: string,
    id: string,
    options: {
      showSuccessToast?: boolean
      showErrorToast?: boolean
      customSuccessMessage?: string
      customErrorMessage?: string
      hardDelete?: boolean
    } = {}
  ): Promise<{ id: string }> => {
    const {
      showSuccessToast = true,
      showErrorToast = true,
      customSuccessMessage,
      customErrorMessage,
      hardDelete = false
    } = options

    isLoading.value = true
    error.value = null

    try {
      // First check if the entity exists
      const currentEntity = await getById(collectionName, id, {
        showErrorToast: false,
        includeDeleted: true
      })

      if (!currentEntity) {
        throw new Error(`${collectionName} not found with ID: ${id}`)
      }

      // Call the server API endpoint
      const response = await $fetch('/api/data/delete', {
        method: 'POST',
        body: {
          collection: collectionName,
          id,
          hardDelete
        }
      })

      if (showSuccessToast) {
        show({
          title: customSuccessMessage || 'Deleted successfully',
          type: 'success'
        })
      }

      return { id }
    } catch (err: any) {
      console.error(`Error deleting ${collectionName}:`, err)
      error.value = err.message || 'Failed to delete'

      if (showErrorToast) {
        show({
          title: customErrorMessage || `Failed to delete: ${error.value}`,
          type: 'danger'
        })
      }

      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Get an entity by ID using the server API
   * @param collectionName The collection name
   * @param id The entity ID
   * @param options Additional options
   * @returns The entity or null if not found
   */
  const getById = async <T>(
    collectionName: string,
    id: string,
    options: {
      showErrorToast?: boolean
      customErrorMessage?: string
      includeDeleted?: boolean
    } = {}
  ): Promise<(T & { id: string }) | null> => {
    const {
      showErrorToast = true,
      customErrorMessage,
      includeDeleted = false
    } = options

    isLoading.value = true
    error.value = null

    try {
      // Call the server API endpoint
      const response = await $fetch('/api/data/read', {
        method: 'POST',
        body: {
          collection: collectionName,
          id,
          includeDeleted
        }
      })

      // If no data is returned, return null
      if (!response || !response.data) {
        return null
      }

      // Return the entity
      return response.data as T & { id: string }
    } catch (err: any) {
      console.error(`Error getting ${collectionName} by ID:`, err)
      error.value = err.message || 'Failed to fetch data'

      if (showErrorToast) {
        show({
          title: customErrorMessage || `Failed to fetch data: ${error.value}`,
          type: 'danger'
        })
      }

      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Get all entities from a collection using the server API
   * @param collectionName The collection name
   * @param options Additional options
   * @returns Array of entities
   */
  const getAll = async <T>(
    collectionName: string,
    options: {
      workspaceId?: string
      profileId?: string
      showErrorToast?: boolean
      customErrorMessage?: string
      includeDeleted?: boolean
      additionalFilters?: [string, string, any][]
      orderBy?: string
      orderDirection?: 'asc' | 'desc'
      limit?: number
      startAfter?: string
    } = {}
  ): Promise<(T & { id: string })[]> => {
    const {
      workspaceId,
      profileId,
      showErrorToast = true,
      customErrorMessage,
      includeDeleted = false,
      additionalFilters = [],
      orderBy = 'created_at',
      orderDirection = 'desc',
      limit = 100,
      startAfter = null
    } = options

    isLoading.value = true
    error.value = null

    try {
      // Build filters object
      const filters: Record<string, any> = {}

      // Add workspace filter if provided
      if (workspaceId) {
        filters['workspace_ids'] = { $contains: workspaceId }
      }

      // Add profile filter if provided
      if (profileId) {
        filters['profile_ids'] = { $contains: profileId }
      }

      // Filter out deleted items unless includeDeleted is true
      if (!includeDeleted) {
        filters['deleted_at'] = null
      }

      // Add any additional filters
      for (const filter of additionalFilters) {
        const [field, operator, value] = filter

        // Convert Firestore operators to API operators
        const apiOperator = operator === '==' ? '$eq' :
                           operator === '!=' ? '$ne' :
                           operator === '>' ? '$gt' :
                           operator === '>=' ? '$gte' :
                           operator === '<' ? '$lt' :
                           operator === '<=' ? '$lte' :
                           operator === 'array-contains' ? '$contains' :
                           operator === 'array-contains-any' ? '$containsAny' :
                           operator === 'in' ? '$in' :
                           operator === 'not-in' ? '$nin' : '$eq'

        filters[field] = { [apiOperator]: value }
      }

      // Call the server API endpoint
      const response = await $fetch('/api/data/read', {
        method: 'POST',
        body: {
          collection: collectionName,
          filters,
          limit,
          orderBy,
          orderDirection,
          startAfter
        }
      })

      // If no data is returned, return empty array
      if (!response || !response.data) {
        return []
      }

      // Return the entities
      return response.data as (T & { id: string })[]
    } catch (err: any) {
      console.error(`Error getting all ${collectionName}:`, err)
      error.value = err.message || 'Failed to fetch data'

      if (showErrorToast) {
        show({
          title: customErrorMessage || `Failed to fetch data: ${error.value}`,
          type: 'danger'
        })
      }

      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Get entities by a specific field value using the server API
   * @param collectionName The collection name
   * @param field The field to query
   * @param value The value to match
   * @param options Additional options
   * @returns Array of matching entities
   */
  const getByField = async <T>(
    collectionName: string,
    field: string,
    value: any,
    options: {
      operator?: string
      workspaceId?: string
      profileId?: string
      showErrorToast?: boolean
      customErrorMessage?: string
      includeDeleted?: boolean
      orderBy?: string
      orderDirection?: 'asc' | 'desc'
      limit?: number
      startAfter?: string
    } = {}
  ): Promise<(T & { id: string })[]> => {
    const {
      operator = '==',
      workspaceId,
      profileId,
      showErrorToast = true,
      customErrorMessage,
      includeDeleted = false,
      orderBy = 'created_at',
      orderDirection = 'desc',
      limit = 100,
      startAfter = null
    } = options

    isLoading.value = true
    error.value = null

    try {
      // Build filters object
      const filters: Record<string, any> = {}

      // Convert Firestore operator to API operator
      const apiOperator = operator === '==' ? '$eq' :
                         operator === '!=' ? '$ne' :
                         operator === '>' ? '$gt' :
                         operator === '>=' ? '$gte' :
                         operator === '<' ? '$lt' :
                         operator === '<=' ? '$lte' :
                         operator === 'array-contains' ? '$contains' :
                         operator === 'array-contains-any' ? '$containsAny' :
                         operator === 'in' ? '$in' :
                         operator === 'not-in' ? '$nin' : '$eq'

      // Add the field filter
      filters[field] = { [apiOperator]: value }

      // Add workspace filter if provided
      if (workspaceId) {
        filters['workspace_ids'] = { $contains: workspaceId }
      }

      // Add profile filter if provided
      if (profileId) {
        filters['profile_ids'] = { $contains: profileId }
      }

      // Filter out deleted items unless includeDeleted is true
      if (!includeDeleted) {
        filters['deleted_at'] = null
      }

      // Call the server API endpoint
      const response = await $fetch('/api/data/read', {
        method: 'POST',
        body: {
          collection: collectionName,
          filters,
          limit,
          orderBy,
          orderDirection,
          startAfter
        }
      })

      // If no data is returned, return empty array
      if (!response || !response.data) {
        return []
      }

      // Return the entities
      return response.data as (T & { id: string })[]
    } catch (err: any) {
      console.error(`Error getting ${collectionName} by field:`, err)
      error.value = err.message || 'Failed to fetch data'

      if (showErrorToast) {
        show({
          title: customErrorMessage || `Failed to fetch data: ${error.value}`,
          type: 'danger'
        })
      }

      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Create an entity-specific API instance for a specific entity type
   * @param collectionName The collection to use for this entity
   * @returns Entity-specific API methods
   */
  const useEntityApi = <T>(collectionName: string) => {
    return {
      create: (data: Partial<T>, options = {}) => create<T>(collectionName, data, options),
      update: (id: string, data: Partial<T>, options = {}) => update<T>(collectionName, id, data, options),
      remove: (id: string, options = {}) => remove(collectionName, id, options),
      getById: (id: string, options = {}) => getById<T>(collectionName, id, options),
      getAll: (options = {}) => getAll<T>(collectionName, options),
      getByField: (field: string, value: any, options = {}) => getByField<T>(collectionName, field, value, options),
      isLoading,
      error
    }
  }

  return {
    create,
    update,
    remove,
    getById,
    getAll,
    getByField,
    useEntityApi,
    isLoading,
    error
  }
}

/**
 * Pre-configured API hooks for common entities
 */

export function useAgenciesApi() {
  return useDataApi().useEntityApi('agencies')
}

export function useAgentApi() {
  return useDataApi().useEntityApi('agents')
}

export function useTeamApi() {
  return useDataApi().useEntityApi('teams')
}

export function useAccountsApi() {
  return useDataApi().useEntityApi('accounts')
}

export function useContactsApi() {
  return useDataApi().useEntityApi('contacts')
}

export function useProjectsApi() {
  return useDataApi().useEntityApi('projects')
}

export function useIntegrationsApi() {
  return useDataApi().useEntityApi('integrations')
}

export function useThreadsApi() {
  return useDataApi().useEntityApi('threads')
}

export function useCompaniesApi() {
  return useDataApi().useEntityApi('companies')
}

export function useProductsApi() {
  return useDataApi().useEntityApi('products')
}

export function useOrdersApi() {
  return useDataApi().useEntityApi('orders')
}

export function useEditorProjectsApi() {
  return useDataApi().useEntityApi('editor_projects')
}

export function useBooksApi() {
  return useDataApi().useEntityApi('books')
}

export function useResearchProjectsApi() {
  return useDataApi().useEntityApi('research_projects')
}

export function useResearchFilesApi() {
  return useDataApi().useEntityApi('research_files')
}

export function useResearchExampleBooksApi() {
  return useDataApi().useEntityApi('research_example_books')
}

export default useDataApi