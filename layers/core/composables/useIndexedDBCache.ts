/**
 * IndexedDB Layer Caching System
 * Provides persistent storage for layer data to improve loading performance
 */

import { ref, computed, onUnmounted } from 'vue'

export interface CachedLayerData {
  id: string
  type: 'core' | 'background' | 'plugin' | 'optional'
  data: any
  timestamp: number
  version: string
  size: number
  compressionLevel: number
  metadata?: Record<string, any>
}

export interface IndexedDBCacheOptions {
  dbName?: string
  dbVersion?: number
  storeName?: string
  maxCacheSize?: number // in MB
  maxAge?: number // in ms
  compressionEnabled?: boolean
}

const DEFAULT_OPTIONS: Required<IndexedDBCacheOptions> = {
  dbName: 'LayerCache',
  dbVersion: 1,
  storeName: 'layers',
  maxCacheSize: 100, // 100MB
  maxAge: 24 * 60 * 60 * 1000, // 24 hours
  compressionEnabled: true
}

/**
 * IndexedDB Cache Manager
 */
class IndexedDBCacheManager {
  private db: IDBDatabase | null = null
  private options: Required<IndexedDBCacheOptions>
  private isInitialized = false
  private initPromise: Promise<void> | null = null

  constructor(options: IndexedDBCacheOptions = {}) {
    this.options = { ...DEFAULT_OPTIONS, ...options }
  }

  /**
   * Initialize the IndexedDB connection
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return
    if (this.initPromise) return this.initPromise

    this.initPromise = new Promise((resolve, reject) => {
      if (typeof window === 'undefined' || !window.indexedDB) {
        reject(new Error('IndexedDB not available'))
        return
      }

      const request = indexedDB.open(this.options.dbName, this.options.dbVersion)

      request.onerror = () => {
        reject(new Error('Failed to open IndexedDB'))
      }

      request.onsuccess = () => {
        this.db = request.result
        this.isInitialized = true
        resolve()
      }

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        
        // Create object store if it doesn't exist
        if (!db.objectStoreNames.contains(this.options.storeName)) {
          const store = db.createObjectStore(this.options.storeName, { keyPath: 'id' })
          
          // Create indexes
          store.createIndex('timestamp', 'timestamp', { unique: false })
          store.createIndex('type', 'type', { unique: false })
          store.createIndex('size', 'size', { unique: false })
        }
      }
    })

    return this.initPromise
  }

  /**
   * Store layer data in cache
   */
  async set(layerData: Omit<CachedLayerData, 'timestamp'>): Promise<void> {
    await this.initialize()
    if (!this.db) throw new Error('IndexedDB not initialized')

    // Check cache size limits before storing
    await this.cleanupIfNeeded()

    const cachedData: CachedLayerData = {
      ...layerData,
      timestamp: Date.now()
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.options.storeName], 'readwrite')
      const store = transaction.objectStore(this.options.storeName)
      
      const request = store.put(cachedData)
      
      request.onsuccess = () => resolve()
      request.onerror = () => reject(new Error('Failed to store layer data'))
    })
  }

  /**
   * Retrieve layer data from cache
   */
  async get(layerId: string): Promise<CachedLayerData | null> {
    await this.initialize()
    if (!this.db) return null

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.options.storeName], 'readonly')
      const store = transaction.objectStore(this.options.storeName)
      
      const request = store.get(layerId)
      
      request.onsuccess = () => {
        const result = request.result
        
        // Check if data is expired
        if (result && (Date.now() - result.timestamp) > this.options.maxAge) {
          this.delete(layerId) // Clean up expired data
          resolve(null)
          return
        }
        
        resolve(result || null)
      }
      
      request.onerror = () => reject(new Error('Failed to retrieve layer data'))
    })
  }

  /**
   * Check if layer exists in cache
   */
  async has(layerId: string): Promise<boolean> {
    const data = await this.get(layerId)
    return data !== null
  }

  /**
   * Delete layer from cache
   */
  async delete(layerId: string): Promise<void> {
    await this.initialize()
    if (!this.db) return

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.options.storeName], 'readwrite')
      const store = transaction.objectStore(this.options.storeName)
      
      const request = store.delete(layerId)
      
      request.onsuccess = () => resolve()
      request.onerror = () => reject(new Error('Failed to delete layer data'))
    })
  }

  /**
   * Get all cached layer IDs
   */
  async getAllKeys(): Promise<string[]> {
    await this.initialize()
    if (!this.db) return []

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.options.storeName], 'readonly')
      const store = transaction.objectStore(this.options.storeName)
      
      const request = store.getAllKeys()
      
      request.onsuccess = () => resolve(request.result as string[])
      request.onerror = () => reject(new Error('Failed to get cache keys'))
    })
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<{
    totalItems: number
    totalSize: number
    oldestItem: number
    newestItem: number
  }> {
    await this.initialize()
    if (!this.db) return { totalItems: 0, totalSize: 0, oldestItem: 0, newestItem: 0 }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.options.storeName], 'readonly')
      const store = transaction.objectStore(this.options.storeName)
      
      const request = store.getAll()
      
      request.onsuccess = () => {
        const items = request.result as CachedLayerData[]
        
        const stats = {
          totalItems: items.length,
          totalSize: items.reduce((sum, item) => sum + item.size, 0),
          oldestItem: items.length > 0 ? Math.min(...items.map(item => item.timestamp)) : 0,
          newestItem: items.length > 0 ? Math.max(...items.map(item => item.timestamp)) : 0
        }
        
        resolve(stats)
      }
      
      request.onerror = () => reject(new Error('Failed to get cache stats'))
    })
  }

  /**
   * Clean up cache if it exceeds size limits
   */
  private async cleanupIfNeeded(): Promise<void> {
    const stats = await this.getStats()
    const sizeLimitBytes = this.options.maxCacheSize * 1024 * 1024

    if (stats.totalSize <= sizeLimitBytes) return

    // Get all items sorted by timestamp (oldest first)
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.options.storeName], 'readwrite')
      const store = transaction.objectStore(this.options.storeName)
      const index = store.index('timestamp')
      
      const request = index.getAll()
      
      request.onsuccess = () => {
        const items = request.result as CachedLayerData[]
        let currentSize = stats.totalSize
        
        // Remove oldest items until under size limit
        for (const item of items) {
          if (currentSize <= sizeLimitBytes) break
          
          store.delete(item.id)
          currentSize -= item.size
        }
        
        resolve()
      }
      
      request.onerror = () => reject(new Error('Failed to cleanup cache'))
    })
  }

  /**
   * Clear all cached data
   */
  async clear(): Promise<void> {
    await this.initialize()
    if (!this.db) return

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.options.storeName], 'readwrite')
      const store = transaction.objectStore(this.options.storeName)
      
      const request = store.clear()
      
      request.onsuccess = () => resolve()
      request.onerror = () => reject(new Error('Failed to clear cache'))
    })
  }

  /**
   * Close the database connection
   */
  close(): void {
    if (this.db) {
      this.db.close()
      this.db = null
      this.isInitialized = false
      this.initPromise = null
    }
  }
}

/**
 * Composable for IndexedDB layer caching
 */
export function useIndexedDBCache(options?: IndexedDBCacheOptions) {
  const cacheManager = new IndexedDBCacheManager(options)
  
  // Reactive state
  const isInitialized = ref(false)
  const cacheStats = ref({
    totalItems: 0,
    totalSize: 0,
    oldestItem: 0,
    newestItem: 0
  })

  // Initialize on first use
  let initPromise: Promise<void> | null = null
  
  const ensureInitialized = async () => {
    if (isInitialized.value) return
    if (!initPromise) {
      initPromise = cacheManager.initialize().then(() => {
        isInitialized.value = true
      })
    }
    return initPromise
  }

  /**
   * Store layer in cache
   */
  const cacheLayer = async (
    id: string,
    type: CachedLayerData['type'],
    data: any,
    version: string = '1.0.0',
    metadata?: Record<string, any>
  ): Promise<boolean> => {
    try {
      await ensureInitialized()
      
      const size = estimateDataSize(data)
      
      await cacheManager.set({
        id,
        type,
        data,
        version,
        size,
        compressionLevel: 0, // Could implement compression here
        metadata
      })
      
      await updateStats()
      return true
    } catch (error) {
      console.error('Failed to cache layer:', error)
      return false
    }
  }

  /**
   * Retrieve layer from cache
   */
  const getCachedLayer = async (id: string): Promise<any | null> => {
    try {
      await ensureInitialized()
      const cached = await cacheManager.get(id)
      return cached?.data || null
    } catch (error) {
      console.error('Failed to get cached layer:', error)
      return null
    }
  }

  /**
   * Check if layer is cached
   */
  const hasLayer = async (id: string): Promise<boolean> => {
    try {
      await ensureInitialized()
      return await cacheManager.has(id)
    } catch (error) {
      console.error('Failed to check cached layer:', error)
      return false
    }
  }

  /**
   * Remove layer from cache
   */
  const removeLayer = async (id: string): Promise<boolean> => {
    try {
      await ensureInitialized()
      await cacheManager.delete(id)
      await updateStats()
      return true
    } catch (error) {
      console.error('Failed to remove cached layer:', error)
      return false
    }
  }

  /**
   * Clear all cached layers
   */
  const clearCache = async (): Promise<boolean> => {
    try {
      await ensureInitialized()
      await cacheManager.clear()
      await updateStats()
      return true
    } catch (error) {
      console.error('Failed to clear cache:', error)
      return false
    }
  }

  /**
   * Update cache statistics
   */
  const updateStats = async () => {
    try {
      await ensureInitialized()
      cacheStats.value = await cacheManager.getStats()
    } catch (error) {
      console.error('Failed to update cache stats:', error)
    }
  }

  /**
   * Get cache utilization percentage
   */
  const cacheUtilization = computed(() => {
    const maxSizeBytes = (options?.maxCacheSize || DEFAULT_OPTIONS.maxCacheSize) * 1024 * 1024
    return (cacheStats.value.totalSize / maxSizeBytes) * 100
  })

  /**
   * Check if cache is near capacity
   */
  const isNearCapacity = computed(() => cacheUtilization.value > 80)

  // Helper function to estimate data size
  function estimateDataSize(data: any): number {
    try {
      return new Blob([JSON.stringify(data)]).size
    } catch {
      return 1024 // Default 1KB
    }
  }

  // Initialize stats on mount
  updateStats()

  // Cleanup on unmount
  onUnmounted(() => {
    cacheManager.close()
  })

  return {
    // State
    isInitialized: computed(() => isInitialized.value),
    cacheStats: computed(() => cacheStats.value),
    cacheUtilization,
    isNearCapacity,

    // Methods
    cacheLayer,
    getCachedLayer,
    hasLayer,
    removeLayer,
    clearCache,
    updateStats,

    // Manager instance for advanced usage
    cacheManager
  }
}

// Export types
export type { IndexedDBCacheOptions, CachedLayerData }