/**
 * Advanced Layer Priority System
 * Manages dynamic layer prioritization based on user interaction and viewport visibility
 */

import { ref, computed, reactive, watch, onUnmounted } from 'vue'
import type { LayerLoadingConfig } from './useProgressiveLoader'

export interface LayerPriorityConfig {
  id: string
  basePriority: 'critical' | 'high' | 'medium' | 'low'
  dynamicFactors: {
    userInteraction: number // 0-1, how much user interacts with this layer
    viewportVisibility: number // 0-1, how often layer is visible
    loadFrequency: number // 0-1, how often layer is loaded
    lastAccessTime: number // timestamp
    averageLoadTime: number // ms
    errorRate: number // 0-1
  }
  boosters: {
    isUserFocused: boolean
    isInViewport: boolean
    isPrefetchCandidate: boolean
    hasUserPreference: boolean
  }
  constraints: {
    maxPriority?: LayerLoadingConfig['priority']
    minPriority?: LayerLoadingConfig['priority']
    timeBasedBoost?: boolean
    contextDependent?: boolean
  }
}

export interface PriorityAnalytics {
  totalInteractions: number
  viewportTime: number // total time in viewport (ms)
  loadCount: number
  errorCount: number
  averageResponseTime: number
  lastUsed: number
  predictedNextUse: number
}

interface PriorityScore {
  layerId: string
  score: number
  priority: LayerLoadingConfig['priority']
  reasons: string[]
  analytics: PriorityAnalytics
}

/**
 * Layer Interaction Tracker
 * Monitors user interactions with layers to inform priority decisions
 */
class LayerInteractionTracker {
  private interactions = new Map<string, PriorityAnalytics>()
  private viewportObserver: IntersectionObserver | null = null
  private visibilityTimers = new Map<string, number>()

  constructor() {
    this.initializeViewportObserver()
  }

  private initializeViewportObserver() {
    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
      this.viewportObserver = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            const layerId = entry.target.getAttribute('data-layer-id')
            if (!layerId) return

            if (entry.isIntersecting) {
              this.startViewportTimer(layerId)
            } else {
              this.endViewportTimer(layerId)
            }
          })
        },
        { threshold: 0.1 } // Consider visible when 10% is in viewport
      )
    }
  }

  private startViewportTimer(layerId: string) {
    this.visibilityTimers.set(layerId, Date.now())
  }

  private endViewportTimer(layerId: string) {
    const startTime = this.visibilityTimers.get(layerId)
    if (startTime) {
      const duration = Date.now() - startTime
      this.recordViewportTime(layerId, duration)
      this.visibilityTimers.delete(layerId)
    }
  }

  /**
   * Track user interaction with a layer
   */
  recordInteraction(layerId: string, interactionType: 'click' | 'hover' | 'focus' | 'scroll') {
    const analytics = this.getOrCreateAnalytics(layerId)
    analytics.totalInteractions++
    analytics.lastUsed = Date.now()
    
    // Weight different interaction types
    const weights = { click: 1.0, focus: 0.8, hover: 0.5, scroll: 0.3 }
    const weight = weights[interactionType] || 0.1
    
    // Update interaction score (exponential moving average)
    const alpha = 0.1
    analytics.averageResponseTime = (1 - alpha) * analytics.averageResponseTime + alpha * weight
    
    this.interactions.set(layerId, analytics)
  }

  /**
   * Record time spent in viewport
   */
  recordViewportTime(layerId: string, duration: number) {
    const analytics = this.getOrCreateAnalytics(layerId)
    analytics.viewportTime += duration
    this.interactions.set(layerId, analytics)
  }

  /**
   * Record layer load event
   */
  recordLoad(layerId: string, loadTime: number, success: boolean) {
    const analytics = this.getOrCreateAnalytics(layerId)
    analytics.loadCount++
    
    if (!success) {
      analytics.errorCount++
    }
    
    // Update average response time
    const alpha = 0.2
    analytics.averageResponseTime = 
      (1 - alpha) * analytics.averageResponseTime + alpha * loadTime
    
    this.interactions.set(layerId, analytics)
  }

  /**
   * Start tracking element for viewport visibility
   */
  trackElement(element: Element, layerId: string) {
    if (this.viewportObserver) {
      element.setAttribute('data-layer-id', layerId)
      this.viewportObserver.observe(element)
    }
  }

  /**
   * Stop tracking element
   */
  untrackElement(element: Element) {
    if (this.viewportObserver) {
      this.viewportObserver.unobserve(element)
    }
  }

  /**
   * Get analytics for a layer
   */
  getAnalytics(layerId: string): PriorityAnalytics | null {
    return this.interactions.get(layerId) || null
  }

  /**
   * Get all analytics
   */
  getAllAnalytics(): Map<string, PriorityAnalytics> {
    return this.interactions
  }

  private getOrCreateAnalytics(layerId: string): PriorityAnalytics {
    if (!this.interactions.has(layerId)) {
      this.interactions.set(layerId, {
        totalInteractions: 0,
        viewportTime: 0,
        loadCount: 0,
        errorCount: 0,
        averageResponseTime: 0,
        lastUsed: 0,
        predictedNextUse: 0
      })
    }
    return this.interactions.get(layerId)!
  }

  /**
   * Predict when layer will be used next
   */
  predictNextUse(layerId: string): number {
    const analytics = this.getAnalytics(layerId)
    if (!analytics || analytics.loadCount < 3) {
      return Date.now() + 3600000 // Default: 1 hour
    }

    // Simple prediction based on usage patterns
    const timeSinceLastUse = Date.now() - analytics.lastUsed
    const averageInterval = analytics.viewportTime / Math.max(analytics.totalInteractions, 1)
    
    return Date.now() + Math.max(averageInterval, timeSinceLastUse * 0.5)
  }

  destroy() {
    if (this.viewportObserver) {
      this.viewportObserver.disconnect()
      this.viewportObserver = null
    }
    this.interactions.clear()
    this.visibilityTimers.clear()
  }
}

/**
 * Priority Calculation Engine
 */
class PriorityCalculationEngine {
  /**
   * Calculate dynamic priority score for a layer
   */
  calculatePriorityScore(
    config: LayerPriorityConfig,
    analytics: PriorityAnalytics
  ): PriorityScore {
    const reasons: string[] = []
    let score = this.getBasePriorityScore(config.basePriority)
    
    // Apply dynamic factors
    score += this.applyDynamicFactors(config.dynamicFactors, reasons)
    
    // Apply boosters
    score += this.applyBoosters(config.boosters, reasons)
    
    // Apply time-based factors
    score += this.applyTimeFactors(analytics, reasons)
    
    // Apply constraints
    const finalPriority = this.applyConstraints(score, config.constraints)
    
    return {
      layerId: config.id,
      score,
      priority: finalPriority,
      reasons,
      analytics
    }
  }

  private getBasePriorityScore(priority: LayerLoadingConfig['priority']): number {
    const scores = { critical: 1000, high: 750, medium: 500, low: 250 }
    return scores[priority]
  }

  private applyDynamicFactors(
    factors: LayerPriorityConfig['dynamicFactors'],
    reasons: string[]
  ): number {
    let boost = 0

    // User interaction boost
    if (factors.userInteraction > 0.7) {
      boost += 200
      reasons.push('High user interaction')
    } else if (factors.userInteraction > 0.3) {
      boost += 100
      reasons.push('Moderate user interaction')
    }

    // Viewport visibility boost
    if (factors.viewportVisibility > 0.8) {
      boost += 150
      reasons.push('Frequently visible')
    } else if (factors.viewportVisibility > 0.4) {
      boost += 75
      reasons.push('Sometimes visible')
    }

    // Load frequency boost
    if (factors.loadFrequency > 0.6) {
      boost += 100
      reasons.push('Frequently loaded')
    }

    // Recent access boost
    const timeSinceAccess = Date.now() - factors.lastAccessTime
    const hoursSinceAccess = timeSinceAccess / (1000 * 60 * 60)
    
    if (hoursSinceAccess < 1) {
      boost += 150
      reasons.push('Recently accessed')
    } else if (hoursSinceAccess < 24) {
      boost += 50
      reasons.push('Accessed today')
    }

    // Performance penalty
    if (factors.averageLoadTime > 5000) {
      boost -= 100
      reasons.push('Slow loading performance')
    } else if (factors.averageLoadTime < 1000) {
      boost += 50
      reasons.push('Fast loading performance')
    }

    // Error rate penalty
    if (factors.errorRate > 0.1) {
      boost -= 200
      reasons.push('High error rate')
    }

    return boost
  }

  private applyBoosters(
    boosters: LayerPriorityConfig['boosters'],
    reasons: string[]
  ): number {
    let boost = 0

    if (boosters.isUserFocused) {
      boost += 300
      reasons.push('User currently focused')
    }

    if (boosters.isInViewport) {
      boost += 200
      reasons.push('Currently in viewport')
    }

    if (boosters.isPrefetchCandidate) {
      boost += 100
      reasons.push('Prefetch candidate')
    }

    if (boosters.hasUserPreference) {
      boost += 150
      reasons.push('User preference set')
    }

    return boost
  }

  private applyTimeFactors(
    analytics: PriorityAnalytics,
    reasons: string[]
  ): number {
    let boost = 0

    // Predict if layer will be needed soon
    const predictedUse = analytics.predictedNextUse
    const timeUntilPredicted = predictedUse - Date.now()
    
    if (timeUntilPredicted < 300000) { // 5 minutes
      boost += 100
      reasons.push('Predicted use soon')
    } else if (timeUntilPredicted < 3600000) { // 1 hour
      boost += 50
      reasons.push('Predicted use within hour')
    }

    return boost
  }

  private applyConstraints(
    score: number,
    constraints: LayerPriorityConfig['constraints']
  ): LayerLoadingConfig['priority'] {
    // Convert score to priority
    let priority: LayerLoadingConfig['priority']
    
    if (score >= 1000) {
      priority = 'critical'
    } else if (score >= 700) {
      priority = 'high'
    } else if (score >= 400) {
      priority = 'medium'
    } else {
      priority = 'low'
    }

    // Apply constraints
    if (constraints.maxPriority) {
      const maxIndex = ['low', 'medium', 'high', 'critical'].indexOf(constraints.maxPriority)
      const currentIndex = ['low', 'medium', 'high', 'critical'].indexOf(priority)
      if (currentIndex > maxIndex) {
        priority = constraints.maxPriority
      }
    }

    if (constraints.minPriority) {
      const minIndex = ['low', 'medium', 'high', 'critical'].indexOf(constraints.minPriority)
      const currentIndex = ['low', 'medium', 'high', 'critical'].indexOf(priority)
      if (currentIndex < minIndex) {
        priority = constraints.minPriority
      }
    }

    return priority
  }
}

/**
 * Composable for advanced layer priority management
 */
export function useLayerPriority() {
  const interactionTracker = new LayerInteractionTracker()
  const priorityEngine = new PriorityCalculationEngine()
  
  // State management
  const layerConfigs = new Map<string, LayerPriorityConfig>()
  const priorityScores = ref(new Map<string, PriorityScore>())
  
  // Analytics
  const analyticsData = reactive({
    totalLayers: 0,
    highPriorityLayers: 0,
    averageScore: 0,
    lastCalculation: 0
  })

  /**
   * Register a layer for priority management
   */
  function registerLayer(config: LayerPriorityConfig) {
    layerConfigs.set(config.id, config)
    analyticsData.totalLayers = layerConfigs.size
  }

  /**
   * Track user interaction with layer
   */
  function trackInteraction(
    layerId: string,
    interactionType: 'click' | 'hover' | 'focus' | 'scroll'
  ) {
    interactionTracker.recordInteraction(layerId, interactionType)
    
    // Trigger priority recalculation for this layer
    recalculateLayerPriority(layerId)
  }

  /**
   * Track element for viewport visibility
   */
  function trackElement(element: Element, layerId: string) {
    interactionTracker.trackElement(element, layerId)
  }

  /**
   * Record layer load performance
   */
  function recordLoadPerformance(layerId: string, loadTime: number, success: boolean) {
    interactionTracker.recordLoad(layerId, loadTime, success)
    recalculateLayerPriority(layerId)
  }

  /**
   * Recalculate priority for a specific layer
   */
  function recalculateLayerPriority(layerId: string) {
    const config = layerConfigs.get(layerId)
    if (!config) return

    const analytics = interactionTracker.getAnalytics(layerId)
    if (!analytics) return

    // Update predicted next use
    analytics.predictedNextUse = interactionTracker.predictNextUse(layerId)

    const score = priorityEngine.calculatePriorityScore(config, analytics)
    priorityScores.value.set(layerId, score)
    
    updateAnalytics()
  }

  /**
   * Recalculate priorities for all layers
   */
  function recalculateAllPriorities() {
    layerConfigs.forEach((config, layerId) => {
      recalculateLayerPriority(layerId)
    })
  }

  /**
   * Get priority for a layer
   */
  function getLayerPriority(layerId: string): LayerLoadingConfig['priority'] | null {
    const score = priorityScores.value.get(layerId)
    return score?.priority || null
  }

  /**
   * Get sorted list of layers by priority
   */
  function getLayersByPriority(): PriorityScore[] {
    return Array.from(priorityScores.value.values())
      .sort((a, b) => b.score - a.score)
  }

  /**
   * Get layers that should be prefetched
   */
  function getPrefetchCandidates(maxCount: number = 5): string[] {
    const sortedLayers = getLayersByPriority()
    
    return sortedLayers
      .filter(layer => {
        const config = layerConfigs.get(layer.layerId)
        return config?.boosters.isPrefetchCandidate || 
               layer.analytics.predictedNextUse < Date.now() + 600000 // 10 minutes
      })
      .slice(0, maxCount)
      .map(layer => layer.layerId)
  }

  /**
   * Boost layer priority temporarily
   */
  function boostLayerPriority(layerId: string, boostType: keyof LayerPriorityConfig['boosters']) {
    const config = layerConfigs.get(layerId)
    if (config) {
      config.boosters[boostType] = true
      recalculateLayerPriority(layerId)
      
      // Auto-remove boost after delay
      setTimeout(() => {
        if (config.boosters[boostType]) {
          config.boosters[boostType] = false
          recalculateLayerPriority(layerId)
        }
      }, 30000) // 30 seconds
    }
  }

  /**
   * Update analytics summary
   */
  function updateAnalytics() {
    const scores = Array.from(priorityScores.value.values())
    
    analyticsData.highPriorityLayers = scores.filter(
      s => s.priority === 'critical' || s.priority === 'high'
    ).length
    
    analyticsData.averageScore = scores.length > 0 
      ? scores.reduce((sum, s) => sum + s.score, 0) / scores.length 
      : 0
    
    analyticsData.lastCalculation = Date.now()
  }

  /**
   * Get priority explanation for debugging
   */
  function explainPriority(layerId: string): {
    score: number
    priority: LayerLoadingConfig['priority']
    reasons: string[]
    analytics: PriorityAnalytics
  } | null {
    const score = priorityScores.value.get(layerId)
    return score || null
  }

  // Auto-recalculate priorities periodically
  const recalcInterval = setInterval(() => {
    recalculateAllPriorities()
  }, 60000) // Every minute

  // Cleanup
  onUnmounted(() => {
    clearInterval(recalcInterval)
    interactionTracker.destroy()
    layerConfigs.clear()
    priorityScores.value.clear()
  })

  return {
    // State
    priorityScores: computed(() => priorityScores.value),
    analyticsData: computed(() => analyticsData),
    
    // Registration
    registerLayer,
    
    // Tracking
    trackInteraction,
    trackElement,
    recordLoadPerformance,
    
    // Priority calculation
    recalculateLayerPriority,
    recalculateAllPriorities,
    getLayerPriority,
    getLayersByPriority,
    getPrefetchCandidates,
    boostLayerPriority,
    
    // Debugging
    explainPriority,
    
    // Analytics
    getInteractionAnalytics: (layerId: string) => interactionTracker.getAnalytics(layerId),
    getAllAnalytics: () => interactionTracker.getAllAnalytics()
  }
}

// Export types
export type { LayerPriorityConfig, PriorityAnalytics, PriorityScore }