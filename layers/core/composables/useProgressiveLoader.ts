/**
 * Progressive Layer Loading System
 * Manages layer loading priorities and progressive loading for improved performance
 */

import { ref, computed, reactive, nextTick, onUnmounted } from 'vue'
import { useMemoryManager } from './useMemoryManager'
import { useCanvasWorkerPool } from '../../../writer/composables/useCanvasWorkerPool'
import { useIndexedDBCache } from './useIndexedDBCache'

export interface LayerLoadingConfig {
  id: string
  type: 'core' | 'background' | 'plugin' | 'optional'
  priority: 'critical' | 'high' | 'medium' | 'low'
  dependencies?: string[]
  estimatedSize?: number
  loadingStrategy: 'immediate' | 'deferred' | 'lazy' | 'on-demand'
  visibilityThreshold?: number // For lazy loading
  timeout?: number
  retryCount?: number
  cacheStrategy?: 'memory' | 'disk' | 'session' | 'none'
}

export interface LayerLoadingState {
  id: string
  status: 'pending' | 'loading' | 'loaded' | 'error' | 'cancelled'
  progress: number
  error?: Error
  loadStartTime?: number
  loadEndTime?: number
  size?: number
  fromCache?: boolean
  retryAttempts: number
}

export interface LoadingSession {
  id: string
  startTime: number
  coreLoadTime?: number
  totalLoadTime?: number
  layers: Map<string, LayerLoadingState>
  cancelled: boolean
}

export interface ProgressiveLoaderOptions {
  maxConcurrentLoads?: number
  coreLoadTimeout?: number
  backgroundLoadTimeout?: number
  retryDelay?: number
  enableCaching?: boolean
  cacheSize?: number
  visibilityBufferPx?: number
  preloadDistance?: number
}

// Default configuration
const DEFAULT_OPTIONS: Required<ProgressiveLoaderOptions> = {
  maxConcurrentLoads: 3,
  coreLoadTimeout: 2000, // 2 seconds for core layers
  backgroundLoadTimeout: 30000, // 30 seconds for background layers
  retryDelay: 1000,
  enableCaching: true,
  cacheSize: 50, // Number of cached layers
  visibilityBufferPx: 100,
  preloadDistance: 2000
}

// Layer loading queue management
class LayerLoadingQueue {
  private queues = {
    critical: [] as LayerLoadingConfig[],
    high: [] as LayerLoadingConfig[],
    medium: [] as LayerLoadingConfig[],
    low: [] as LayerLoadingConfig[]
  }
  
  private activeLoads = new Set<string>()
  private maxConcurrent: number
  
  constructor(maxConcurrent: number) {
    this.maxConcurrent = maxConcurrent
  }
  
  enqueue(config: LayerLoadingConfig) {
    // Check dependencies first
    const dependencies = config.dependencies || []
    const unmetDependencies = dependencies.filter(dep => !this.isLayerLoaded(dep))
    
    if (unmetDependencies.length > 0) {
      // Defer until dependencies are met
      setTimeout(() => this.enqueue(config), 100)
      return
    }
    
    this.queues[config.priority].push(config)
  }
  
  dequeue(): LayerLoadingConfig | null {
    if (this.activeLoads.size >= this.maxConcurrent) {
      return null
    }
    
    // Process in priority order
    for (const priority of ['critical', 'high', 'medium', 'low'] as const) {
      const queue = this.queues[priority]
      if (queue.length > 0) {
        const config = queue.shift()!
        this.activeLoads.add(config.id)
        return config
      }
    }
    
    return null
  }
  
  markComplete(layerId: string) {
    this.activeLoads.delete(layerId)
  }
  
  clear() {
    Object.values(this.queues).forEach(queue => queue.length = 0)
    this.activeLoads.clear()
  }
  
  private isLayerLoaded(layerId: string): boolean {
    // Check if layer is already loaded
    // This would integrate with the actual layer system
    return false
  }
  
  getStats() {
    return {
      queued: Object.values(this.queues).reduce((sum, queue) => sum + queue.length, 0),
      active: this.activeLoads.size,
      maxConcurrent: this.maxConcurrent
    }
  }
}

// Cache management for loaded layers
class LayerCache {
  private cache = new Map<string, any>()
  private accessTimes = new Map<string, number>()
  private maxSize: number
  
  constructor(maxSize: number) {
    this.maxSize = maxSize
  }
  
  set(key: string, value: any) {
    // Remove oldest items if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.getOldestKey()
      if (oldestKey) {
        this.cache.delete(oldestKey)
        this.accessTimes.delete(oldestKey)
      }
    }
    
    this.cache.set(key, value)
    this.accessTimes.set(key, Date.now())
  }
  
  get(key: string): any {
    const value = this.cache.get(key)
    if (value !== undefined) {
      this.accessTimes.set(key, Date.now())
    }
    return value
  }
  
  has(key: string): boolean {
    return this.cache.has(key)
  }
  
  delete(key: string) {
    this.cache.delete(key)
    this.accessTimes.delete(key)
  }
  
  clear() {
    this.cache.clear()
    this.accessTimes.clear()
  }
  
  private getOldestKey(): string | null {
    let oldestKey: string | null = null
    let oldestTime = Infinity
    
    for (const [key, time] of this.accessTimes) {
      if (time < oldestTime) {
        oldestTime = time
        oldestKey = key
      }
    }
    
    return oldestKey
  }
  
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: 0 // Would need to track hits/misses for actual rate
    }
  }
}

// Intersection Observer for lazy loading
class LazyLoadingObserver {
  private observer: IntersectionObserver | null = null
  private callbacks = new Map<Element, () => void>()
  
  constructor(rootMargin: string = '100px') {
    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const callback = this.callbacks.get(entry.target)
              if (callback) {
                callback()
                this.unobserve(entry.target)
              }
            }
          })
        },
        { rootMargin }
      )
    }
  }
  
  observe(element: Element, callback: () => void) {
    if (!this.observer) {
      // Fallback: execute immediately if no observer support
      callback()
      return
    }
    
    this.callbacks.set(element, callback)
    this.observer.observe(element)
  }
  
  unobserve(element: Element) {
    if (this.observer) {
      this.observer.unobserve(element)
    }
    this.callbacks.delete(element)
  }
  
  disconnect() {
    if (this.observer) {
      this.observer.disconnect()
    }
    this.callbacks.clear()
  }
}

/**
 * Progressive Layer Loading Composable
 */
export function useProgressiveLoader(options: ProgressiveLoaderOptions = {}) {
  const config = { ...DEFAULT_OPTIONS, ...options }
  const memoryManager = useMemoryManager()
  const { workerPool } = useCanvasWorkerPool()
  const indexedDBCache = useIndexedDBCache({
    maxCacheSize: 200, // 200MB for layer caching
    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
  })
  
  // State management
  const currentSession = ref<LoadingSession | null>(null)
  const loadingQueue = new LayerLoadingQueue(config.maxConcurrentLoads)
  const layerCache = new LayerCache(config.cacheSize)
  const lazyObserver = new LazyLoadingObserver(`${config.visibilityBufferPx}px`)
  
  // Reactive state
  const isLoading = ref(false)
  const coreLayersLoaded = ref(false)
  const loadingProgress = ref(0)
  const loadingStats = reactive({
    totalLayers: 0,
    loadedLayers: 0,
    failedLayers: 0,
    fromCache: 0,
    averageLoadTime: 0
  })
  
  let processingTimer: NodeJS.Timeout | null = null
  
  // Computed values
  const canStartBackgroundLoading = computed(() => coreLayersLoaded.value)
  const overallProgress = computed(() => {
    if (!currentSession.value) return 0
    const total = currentSession.value.layers.size
    if (total === 0) return 0
    
    let progress = 0
    currentSession.value.layers.forEach(layer => {
      progress += layer.progress
    })
    
    return progress / total
  })
  
  /**
   * Start loading session with layer configurations
   */
  async function startLoadingSession(
    layers: LayerLoadingConfig[],
    sessionId?: string
  ): Promise<LoadingSession> {
    // Cancel existing session if active
    if (currentSession.value && !currentSession.value.cancelled) {
      await cancelLoadingSession()
    }
    
    const session: LoadingSession = {
      id: sessionId || `session_${Date.now()}`,
      startTime: Date.now(),
      layers: new Map(),
      cancelled: false
    }
    
    // Initialize layer states
    layers.forEach(config => {
      session.layers.set(config.id, {
        id: config.id,
        status: 'pending',
        progress: 0,
        retryAttempts: 0
      })
    })
    
    currentSession.value = session
    isLoading.value = true
    loadingProgress.value = 0
    loadingStats.totalLayers = layers.length
    loadingStats.loadedLayers = 0
    loadingStats.failedLayers = 0
    loadingStats.fromCache = 0
    
    // Separate core and background layers
    const coreLayers = layers.filter(l => l.type === 'core')
    const backgroundLayers = layers.filter(l => l.type !== 'core')
    
    // Start loading core layers first
    await loadCoreLayers(coreLayers)
    
    // Mark core loading complete
    coreLayersLoaded.value = true
    session.coreLoadTime = Date.now() - session.startTime
    
    // Start background loading
    startBackgroundLoading(backgroundLayers)
    
    return session
  }
  
  /**
   * Load core layers with high priority
   */
  async function loadCoreLayers(coreLayers: LayerLoadingConfig[]): Promise<void> {
    const promises = coreLayers.map(config => loadLayer(config, true))
    
    try {
      await Promise.race([
        Promise.all(promises),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Core loading timeout')), config.coreLoadTimeout)
        )
      ])
    } catch (error) {
      console.error('Core layers loading failed:', error)
      // Continue with partial core loading
    }
  }
  
  /**
   * Start background layer loading
   */
  function startBackgroundLoading(backgroundLayers: LayerLoadingConfig[]) {
    // Add to queue based on loading strategy
    backgroundLayers.forEach(config => {
      switch (config.loadingStrategy) {
        case 'immediate':
          loadingQueue.enqueue(config)
          break
        case 'deferred':
          setTimeout(() => loadingQueue.enqueue(config), 100)
          break
        case 'lazy':
          // Will be loaded when visible
          break
        case 'on-demand':
          // Will be loaded when requested
          break
      }
    })
    
    // Start processing queue
    startQueueProcessing()
  }
  
  /**
   * Load a single layer
   */
  async function loadLayer(config: LayerLoadingConfig, isCore = false): Promise<any> {
    const session = currentSession.value
    if (!session || session.cancelled) {
      throw new Error('Loading session cancelled')
    }
    
    const layerState = session.layers.get(config.id)
    if (!layerState) {
      throw new Error(`Layer ${config.id} not found in session`)
    }
    
    layerState.status = 'loading'
    layerState.loadStartTime = Date.now()
    layerState.retryAttempts++
    
    try {
      // Check IndexedDB cache first (persistent)
      if (config.cacheStrategy !== 'none') {
        const persistentCached = await indexedDBCache.getCachedLayer(config.id)
        if (persistentCached !== null) {
          layerState.status = 'loaded'
          layerState.progress = 100
          layerState.fromCache = true
          layerState.loadEndTime = Date.now()
          loadingStats.fromCache++
          updateProgress()
          
          // Also cache in memory for faster subsequent access
          layerCache.set(config.id, persistentCached)
          
          return persistentCached
        }
        
        // Check memory cache (faster but temporary)
        if (layerCache.has(config.id)) {
          const cachedData = layerCache.get(config.id)
          layerState.status = 'loaded'
          layerState.progress = 100
          layerState.fromCache = true
          layerState.loadEndTime = Date.now()
          loadingStats.fromCache++
          updateProgress()
          return cachedData
        }
      }
      
      // Load layer data
      const data = await loadLayerData(config, (progress) => {
        layerState.progress = progress
        updateProgress()
      })
      
      // Cache the result in both memory and IndexedDB
      if (config.cacheStrategy !== 'none') {
        layerCache.set(config.id, data)
        
        // Cache in IndexedDB for persistence (don't await to avoid blocking)
        indexedDBCache.cacheLayer(
          config.id,
          config.type,
          data,
          '1.0.0',
          {
            loadTime: Date.now() - layerState.loadStartTime!,
            priority: config.priority,
            estimatedSize: config.estimatedSize
          }
        ).catch(error => {
          console.warn(`Failed to cache layer ${config.id} in IndexedDB:`, error)
        })
      }
      
      // Register with memory manager
      await memoryManager.registerLayer(
        config.id,
        'data',
        data,
        {
          priority: isCore ? 'high' : 'medium',
          isVisible: config.loadingStrategy === 'immediate',
          enableCompression: true
        }
      )
      
      layerState.status = 'loaded'
      layerState.progress = 100
      layerState.loadEndTime = Date.now()
      layerState.size = config.estimatedSize || estimateDataSize(data)
      
      loadingStats.loadedLayers++
      updateProgress()
      
      return data
      
    } catch (error) {
      layerState.status = 'error'
      layerState.error = error instanceof Error ? error : new Error(String(error))
      
      // Retry logic
      if (layerState.retryAttempts < (config.retryCount || 3)) {
        await new Promise(resolve => setTimeout(resolve, config.retryDelay || 1000))
        return loadLayer(config, isCore)
      }
      
      loadingStats.failedLayers++
      updateProgress()
      
      throw error
    } finally {
      loadingQueue.markComplete(config.id)
    }
  }
  
  /**
   * Load layer data (placeholder - would integrate with actual layer loading)
   */
  async function loadLayerData(
    config: LayerLoadingConfig,
    onProgress: (progress: number) => void
  ): Promise<any> {
    // Simulate progressive loading
    const steps = 10
    for (let i = 0; i <= steps; i++) {
      await new Promise(resolve => setTimeout(resolve, 50))
      onProgress((i / steps) * 100)
    }
    
    // Return mock data - in real implementation, this would load actual layer data
    return {
      id: config.id,
      type: config.type,
      timestamp: Date.now(),
      size: config.estimatedSize || 1024
    }
  }
  
  /**
   * Start queue processing
   */
  function startQueueProcessing() {
    if (processingTimer) {
      clearInterval(processingTimer)
    }
    
    processingTimer = setInterval(() => {
      const nextLayer = loadingQueue.dequeue()
      if (nextLayer) {
        loadLayer(nextLayer).catch(error => {
          console.error(`Failed to load layer ${nextLayer.id}:`, error)
        })
      }
    }, 100)
  }
  
  /**
   * Update overall progress
   */
  function updateProgress() {
    loadingProgress.value = overallProgress.value
    
    // Update average load time
    if (currentSession.value) {
      let totalTime = 0
      let completedLayers = 0
      
      currentSession.value.layers.forEach(layer => {
        if (layer.loadEndTime && layer.loadStartTime) {
          totalTime += layer.loadEndTime - layer.loadStartTime
          completedLayers++
        }
      })
      
      if (completedLayers > 0) {
        loadingStats.averageLoadTime = totalTime / completedLayers
      }
    }
  }
  
  /**
   * Load layer on demand
   */
  async function loadLayerOnDemand(layerId: string): Promise<any> {
    if (!currentSession.value) {
      throw new Error('No active loading session')
    }
    
    const layerState = currentSession.value.layers.get(layerId)
    if (!layerState) {
      throw new Error(`Layer ${layerId} not found`)
    }
    
    if (layerState.status === 'loaded') {
      // Return from memory manager
      return await memoryManager.accessLayer(layerId)
    }
    
    // Find the layer config and load it
    // This would need to be implemented based on your layer system
    throw new Error('On-demand loading not fully implemented')
  }
  
  /**
   * Setup lazy loading for element
   */
  function setupLazyLoading(element: Element, layerId: string) {
    lazyObserver.observe(element, () => {
      loadLayerOnDemand(layerId).catch(error => {
        console.error(`Lazy loading failed for layer ${layerId}:`, error)
      })
    })
  }
  
  /**
   * Prioritize layer loading
   */
  function prioritizeLayer(layerId: string, newPriority: LayerLoadingConfig['priority']) {
    // Implementation would need to update the queue and potentially interrupt current loading
    console.log(`Prioritizing layer ${layerId} to ${newPriority}`)
  }
  
  /**
   * Cancel loading session
   */
  async function cancelLoadingSession(): Promise<void> {
    if (currentSession.value) {
      currentSession.value.cancelled = true
      loadingQueue.clear()
      
      if (processingTimer) {
        clearInterval(processingTimer)
        processingTimer = null
      }
      
      isLoading.value = false
      currentSession.value = null
    }
  }
  
  /**
   * Get loading statistics
   */
  function getLoadingStats() {
    return {
      ...loadingStats,
      queueStats: loadingQueue.getStats(),
      cacheStats: layerCache.getStats(),
      memoryStats: memoryManager.getMemoryInfo(),
      session: currentSession.value ? {
        id: currentSession.value.id,
        duration: Date.now() - currentSession.value.startTime,
        coreLoadTime: currentSession.value.coreLoadTime,
        layersCount: currentSession.value.layers.size
      } : null
    }
  }
  
  // Helper function to estimate data size
  function estimateDataSize(data: any): number {
    try {
      return new Blob([JSON.stringify(data)]).size
    } catch {
      return 1024 // Default 1KB
    }
  }
  
  // Cleanup on unmount
  onUnmounted(() => {
    cancelLoadingSession()
    lazyObserver.disconnect()
    layerCache.clear()
  })
  
  return {
    // State
    isLoading: computed(() => isLoading.value),
    coreLayersLoaded: computed(() => coreLayersLoaded.value),
    loadingProgress: computed(() => loadingProgress.value),
    overallProgress,
    canStartBackgroundLoading,
    loadingStats: computed(() => loadingStats),
    
    // Methods
    startLoadingSession,
    loadLayerOnDemand,
    setupLazyLoading,
    prioritizeLayer,
    cancelLoadingSession,
    getLoadingStats,
    
    // Cache management
    clearCache: () => {
      layerCache.clear()
      indexedDBCache.clearCache()
    },
    clearMemoryCache: () => layerCache.clear(),
    clearPersistentCache: () => indexedDBCache.clearCache(),
    
    // Session management
    getCurrentSession: () => currentSession.value
  }
}

// Export types and utilities
export { LayerLoadingQueue, LayerCache, LazyLoadingObserver }