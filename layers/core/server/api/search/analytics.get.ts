import { z } from 'zod'
import { collection, query, where, orderBy, limit, getDocs, startAfter } from 'firebase/firestore'
import { useFirestore } from '~/layers/core/composables/useFirestore'

// Analytics query schema
const AnalyticsQuerySchema = z.object({
  workspaceId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  limit: z.number().min(1).max(1000).optional().default(100),
  cursor: z.string().optional()
})

// Analytics response interface
interface SearchAnalyticsResponse {
  totalSearches: number
  averageResponseTime: number
  topQueries: Array<{
    query: string
    count: number
    averageResults: number
  }>
  noResultQueries: Array<{
    query: string
    count: number
  }>
  entityTypeDistribution: Record<string, number>
  searchTrends: Array<{
    date: string
    searchCount: number
    averageResponseTime: number
  }>
}

/**
 * Search analytics endpoint for administrators
 * GET /api/search/analytics
 */
export default defineEventHandler(async (event) => {
  try {
    // Validate request method
    if (getMethod(event) !== 'GET') {
      throw createError({
        statusCode: 405,
        statusMessage: 'Method Not Allowed'
      })
    }

    // Parse and validate query parameters
    const query = getQuery(event)
    const validatedQuery = AnalyticsQuerySchema.parse(query)

    const { workspaceId, startDate, endDate, limit: queryLimit, cursor } = validatedQuery

    const { db } = useFirestore()
    const analyticsRef = collection(db, 'search_analytics')

    // Build query constraints
    const constraints = []

    // Filter by workspace if provided
    if (workspaceId) {
      constraints.push(where('workspaceId', '==', workspaceId))
    }

    // Filter by date range if provided
    if (startDate) {
      constraints.push(where('timestamp', '>=', new Date(startDate)))
    }

    if (endDate) {
      constraints.push(where('timestamp', '<=', new Date(endDate)))
    }

    // Order by timestamp (most recent first)
    constraints.push(orderBy('timestamp', 'desc'))

    // Add pagination
    if (cursor) {
      constraints.push(startAfter(cursor))
    }

    constraints.push(limit(queryLimit))

    // Execute query
    const q = query(analyticsRef, ...constraints)
    const querySnapshot = await getDocs(q)

    // Process analytics data
    const searches = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))

    // Calculate analytics metrics
    const totalSearches = searches.length
    const averageResponseTime = searches.reduce((sum, search) => sum + (search.responseTime || 0), 0) / totalSearches || 0

    // Top queries analysis
    const queryMap = new Map<string, { count: number; totalResults: number }>()
    searches.forEach(search => {
      const existing = queryMap.get(search.query) || { count: 0, totalResults: 0 }
      queryMap.set(search.query, {
        count: existing.count + 1,
        totalResults: existing.totalResults + (search.resultCount || 0)
      })
    })

    const topQueries = Array.from(queryMap.entries())
      .map(([query, data]) => ({
        query,
        count: data.count,
        averageResults: data.totalResults / data.count
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)

    // No result queries
    const noResultQueries = Array.from(queryMap.entries())
      .filter(([, data]) => data.totalResults === 0)
      .map(([query, data]) => ({
        query,
        count: data.count
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)

    // Entity type distribution
    const entityTypeDistribution: Record<string, number> = {}
    searches.forEach(search => {
      if (search.entityTypes && Array.isArray(search.entityTypes)) {
        search.entityTypes.forEach((type: string) => {
          entityTypeDistribution[type] = (entityTypeDistribution[type] || 0) + 1
        })
      }
    })

    // Search trends (grouped by day)
    const trendMap = new Map<string, { count: number; totalResponseTime: number }>()
    searches.forEach(search => {
      const date = new Date(search.timestamp.toDate()).toISOString().split('T')[0]
      const existing = trendMap.get(date) || { count: 0, totalResponseTime: 0 }
      trendMap.set(date, {
        count: existing.count + 1,
        totalResponseTime: existing.totalResponseTime + (search.responseTime || 0)
      })
    })

    const searchTrends = Array.from(trendMap.entries())
      .map(([date, data]) => ({
        date,
        searchCount: data.count,
        averageResponseTime: data.totalResponseTime / data.count
      }))
      .sort((a, b) => a.date.localeCompare(b.date))

    // Prepare response
    const response: SearchAnalyticsResponse = {
      totalSearches,
      averageResponseTime,
      topQueries,
      noResultQueries,
      entityTypeDistribution,
      searchTrends
    }

    // Set security headers
    setHeader(event, 'X-Content-Type-Options', 'nosniff')
    setHeader(event, 'X-Frame-Options', 'DENY')
    setHeader(event, 'X-XSS-Protection', '1; mode=block')

    return response

  } catch (error) {
    console.error('Search analytics error:', error)

    if (error instanceof z.ZodError) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid query parameters',
        data: error.errors
      })
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
      data: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})