import { z } from 'zod'
import { collection, query, where, orderBy, limit, getDocs, documentId, addDoc, serverTimestamp } from 'firebase/firestore'
import { useFirestore } from '~/layers/core/composables/useFirestore'
import type { SearchEntityType, SearchResult, SearchResponse } from '~/layers/crm/types/crm'

// Search analytics interface for tracking
interface SearchAnalytics {
  query: string
  entityTypes: SearchEntityType[]
  resultCount: number
  responseTime: number
  userId?: string
  workspaceId?: string
  timestamp: any
  hasResults: boolean
  clientIP: string
}

// Request schema validation with security enhancements
const SearchRequestSchema = z.object({
  query: z.string()
    .min(1, 'Query is required')
    .max(100, 'Query too long')
    .regex(/^[a-zA-Z0-9\s\-_@.]+$/, 'Query contains invalid characters'), // Only allow safe characters
  filters: z.object({
    entityTypes: z.array(z.enum(['account', 'contact', 'company', 'product', 'order']))
      .max(5, 'Too many entity types')
      .optional(),
    workspaceId: z.string()
      .regex(/^[a-zA-Z0-9\-_]+$/, 'Invalid workspace ID format')
      .max(50, 'Workspace ID too long')
      .optional(),
    profileId: z.string()
      .regex(/^[a-zA-Z0-9\-_]+$/, 'Invalid profile ID format')
      .max(50, 'Profile ID too long')
      .optional(),
    status: z.string()
      .regex(/^[a-zA-Z0-9\-_]+$/, 'Invalid status format')
      .max(20, 'Status too long')
      .optional(),
    tags: z.array(z.string().max(30, 'Tag too long'))
      .max(10, 'Too many tags')
      .optional()
  }).optional(),
  limit: z.number().min(1).max(100).optional().default(50),
  includeDeleted: z.boolean().optional().default(false)
})

type SearchRequest = z.infer<typeof SearchRequestSchema>

// Entity type to collection name mapping (proper mapping instead of simple pluralization)
const ENTITY_COLLECTION_MAP: Record<SearchEntityType, string> = {
  account: 'accounts',
  contact: 'contacts', 
  company: 'companies', // Proper plural
  product: 'products',
  order: 'orders'
}

// Search fields configuration for each entity type
const SEARCH_FIELDS_MAP: Record<SearchEntityType, string[]> = {
  account: ['name', 'description', 'type'],
  contact: ['name', 'email', 'job_title', 'company_name'],
  company: ['name', 'description', 'industry'],
  product: ['name', 'sku', 'description', 'categories'],
  order: ['order_number', 'description']
}

/**
 * Sanitize text input to prevent XSS
 */
function sanitizeText(text: string): string {
  if (typeof text !== 'string') return ''
  
  return text
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
    .trim()
}

/**
 * Format entity data to search result with security sanitization
 */
function formatSearchResult(entity: any, type: SearchEntityType): SearchResult {
  const baseResult = {
    id: entity.id,
    type,
    entity
  }

  switch (type) {
    case 'account':
      return {
        ...baseResult,
        title: sanitizeText(entity.name || 'Untitled Account'),
        subtitle: sanitizeText(entity.type || ''),
        description: sanitizeText(entity.description || ''),
        avatar: entity.avatar,
        metadata: {
          status: entity.status,
          contactCount: entity.contact_ids?.length || 0
        }
      }
    
    case 'contact':
      return {
        ...baseResult,
        title: sanitizeText(entity.name || 'Untitled Contact'),
        subtitle: sanitizeText(entity.email || ''),
        description: sanitizeText(`${entity.job_title || ''} ${entity.company_name ? `at ${entity.company_name}` : ''}`.trim()),
        avatar: entity.avatar,
        metadata: {
          status: entity.status,
          phone: entity.phone,
          jobTitle: entity.job_title
        }
      }
    
    case 'company':
      return {
        ...baseResult,
        title: sanitizeText(entity.name || 'Untitled Company'),
        subtitle: sanitizeText(entity.industry || ''),
        description: sanitizeText(entity.description || ''),
        avatar: entity.logo,
        metadata: {
          status: entity.status,
          employeeCount: entity.employees_count,
          revenue: entity.annual_revenue
        }
      }
    
    case 'product':
      return {
        ...baseResult,
        title: sanitizeText(entity.name || 'Untitled Product'),
        subtitle: sanitizeText(entity.sku || ''),
        description: sanitizeText(entity.description || ''),
        avatar: entity.images?.[0],
        metadata: {
          status: entity.status,
          price: entity.price,
          currency: entity.currency || 'USD',
          stockStatus: entity.stock_status
        }
      }
    
    case 'order':
      return {
        ...baseResult,
        title: sanitizeText(entity.order_number || 'Untitled Order'),
        subtitle: sanitizeText(`Total: ${entity.currency || '$'}${entity.total || '0'}`),
        description: sanitizeText(entity.description || ''),
        metadata: {
          status: entity.status,
          paymentStatus: entity.payment_status,
          deliveryStatus: entity.delivery_status,
          total: entity.total,
          currency: entity.currency
        }
      }
    
    default:
      return {
        ...baseResult,
        title: sanitizeText(entity.name || 'Unknown'),
        subtitle: '',
        description: sanitizeText(entity.description || ''),
        metadata: {}
      }
  }
}

/**
 * Search entities in a specific collection with optimized database-level filtering
 */
async function searchEntityCollection(
  entityType: SearchEntityType,
  searchQuery: string,
  filters: SearchRequest['filters'] = {},
  searchLimit: number = 20
): Promise<SearchResult[]> {
  try {
    const { db } = useFirestore()
    const collectionName = ENTITY_COLLECTION_MAP[entityType]
    const collectionRef = collection(db, collectionName)
    
    // Build query constraints with performance considerations
    const constraints = []
    
    // Most selective filters first for better query performance
    
    // Active filter (exclude soft deleted unless explicitly requested) - often most selective
    if (!filters.includeDeleted) {
      constraints.push(where('active', '==', true))
    }
    
    // Status filter - potentially selective
    if (filters.status) {
      constraints.push(where('status', '==', filters.status))
    }
    
    // Workspace filter - ensures data isolation
    if (filters.workspaceId) {
      constraints.push(where('workspace_ids', 'array-contains', filters.workspaceId))
    }
    
    // Profile filter - user-specific filtering
    if (filters.profileId) {
      constraints.push(where('profile_ids', 'array-contains', filters.profileId))
    }
    
    // Add ordering and limit with performance optimization
    // Use updated_at for newer records first if no specific search term
    if (!searchQuery.trim()) {
      constraints.push(orderBy('updated_at', 'desc'))
    } else {
      constraints.push(orderBy('name'))
    }
    
    // Limit early to reduce data transfer
    constraints.push(limit(Math.min(searchLimit, 50))) // Cap at 50 for performance
    
    // Execute query with timeout protection
    const q = query(collectionRef, ...constraints)
    const querySnapshot = await Promise.race([
      getDocs(q),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Query timeout')), 5000)
      )
    ]) as any
    
    // Optimize entity mapping
    const entities = querySnapshot.docs.map((doc: any) => {
      const data = doc.data()
      return {
        id: doc.id,
        // Only extract fields we actually need for search
        name: data.name,
        description: data.description,
        email: data.email,
        job_title: data.job_title,
        company_name: data.company_name,
        industry: data.industry,
        sku: data.sku,
        order_number: data.order_number,
        avatar: data.avatar,
        logo: data.logo,
        images: data.images,
        status: data.status,
        type: data.type,
        phone: data.phone,
        contact_ids: data.contact_ids,
        employees_count: data.employees_count,
        annual_revenue: data.annual_revenue,
        price: data.price,
        currency: data.currency,
        stock_status: data.stock_status,
        total: data.total,
        payment_status: data.payment_status,
        delivery_status: data.delivery_status
      }
    })
    
    // Apply optimized text search filtering with relevance scoring
    if (!searchQuery.trim()) {
      return entities.map((entity: any) => formatSearchResult(entity, entityType))
    }
    
    const searchText = searchQuery.toLowerCase().trim()
    const searchFields = SEARCH_FIELDS_MAP[entityType]
    
    // Filter and score entities
    const scoredEntities = entities
      .filter((entity: any) => {
        // Check all searchable fields
        return searchFields.some(field => {
          const fieldValue = entity[field]
          if (typeof fieldValue === 'string') {
            return fieldValue.toLowerCase().includes(searchText)
          } else if (Array.isArray(fieldValue)) {
            return fieldValue.some((item: any) => 
              String(item).toLowerCase().includes(searchText)
            )
          }
          return false
        })
      })
      .map((entity: any) => ({
        entity,
        score: calculateRelevanceScore(entity, searchQuery, entityType)
      }))
      .sort((a, b) => b.score - a.score) // Sort by relevance score descending
    
    return scoredEntities.map(({ entity }) => formatSearchResult(entity, entityType))
  } catch (error) {
    console.error(`Error searching ${entityType} collection:`, error)
    
    // Provide more specific error information
    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        throw new Error(`Search timeout for ${entityType} - try refining your query`)
      } else if (error.message.includes('permission')) {
        throw new Error(`Access denied for ${entityType} collection`)
      }
    }
    
    throw new Error(`Failed to search ${entityType} entities`)
  }
}

// Enhanced caching and rate limiting
const searchCache = new Map<string, { data: SearchResponse; timestamp: number }>()
const requestCounts = new Map<string, { count: number; resetTime: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 30 // 30 requests per minute
const CACHE_TTL = 5 * 60 * 1000 // 5 minutes cache TTL

// Search suggestions cache for popular/frequent queries
const searchSuggestions = new Map<string, string[]>()

/**
 * Track search analytics asynchronously
 */
async function trackSearchAnalytics(analytics: SearchAnalytics): Promise<void> {
  try {
    const { db } = useFirestore()
    const analyticsRef = collection(db, 'search_analytics')
    
    // Fire and forget - don't wait for this to complete
    addDoc(analyticsRef, {
      ...analytics,
      timestamp: serverTimestamp()
    }).catch(error => {
      console.error('Failed to track search analytics:', error)
    })
  } catch (error) {
    console.error('Search analytics tracking error:', error)
  }
}

/**
 * Add search query to user's search history asynchronously
 */
async function addToSearchHistory(
  query: string,
  filters: any,
  resultCount: number,
  responseTime: number,
  userId?: string,
  workspaceId?: string
): Promise<void> {
  try {
    // Only add to history if we have a user ID and a meaningful query
    if (!userId || !query.trim() || query.length < 2) {
      return
    }
    
    const { db } = useFirestore()
    const historyRef = collection(db, 'search_history')
    
    // Fire and forget - don't wait for this to complete
    addDoc(historyRef, {
      user_id: userId,
      workspace_id: workspaceId || null,
      query: query.trim(),
      filters: filters || {},
      result_count: resultCount,
      search_time: responseTime,
      timestamp: serverTimestamp(),
      active: true
    }).catch(error => {
      console.error('Failed to add search to history:', error)
    })
  } catch (error) {
    console.error('Search history tracking error:', error)
  }
}

/**
 * Generate cache key for search request
 */
function generateCacheKey(searchQuery: string, filters: any, searchLimit: number): string {
  return `search:${searchQuery}:${JSON.stringify(filters)}:${searchLimit}`
}

/**
 * Get cached search result if available and not expired
 */
function getCachedResult(cacheKey: string): SearchResponse | null {
  const cached = searchCache.get(cacheKey)
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data
  }
  if (cached) {
    searchCache.delete(cacheKey) // Remove expired cache
  }
  return null
}

/**
 * Cache search result
 */
function setCachedResult(cacheKey: string, data: SearchResponse): void {
  searchCache.set(cacheKey, {
    data,
    timestamp: Date.now()
  })
  
  // Clean up old cache entries periodically
  if (searchCache.size > 1000) {
    const oldestEntries = Array.from(searchCache.entries())
      .sort(([,a], [,b]) => a.timestamp - b.timestamp)
      .slice(0, 200)
    
    oldestEntries.forEach(([key]) => searchCache.delete(key))
  }
}

/**
 * Calculate relevance score for search results
 */
function calculateRelevanceScore(entity: any, searchQuery: string, entityType: SearchEntityType): number {
  const searchText = searchQuery.toLowerCase().trim()
  let score = 0
  
  // Base score by entity type priority
  const typePriority = { account: 100, contact: 90, company: 80, product: 70, order: 60 }
  score += typePriority[entityType] || 50
  
  // Name matching (highest weight)
  if (entity.name) {
    const name = entity.name.toLowerCase()
    if (name === searchText) score += 1000 // Exact match
    else if (name.startsWith(searchText)) score += 500 // Starts with
    else if (name.includes(searchText)) score += 200 // Contains
  }
  
  // Secondary field matching
  const searchFields = SEARCH_FIELDS_MAP[entityType]
  searchFields.forEach(field => {
    if (field !== 'name' && entity[field]) {
      const value = String(entity[field]).toLowerCase()
      if (value === searchText) score += 300
      else if (value.startsWith(searchText)) score += 150
      else if (value.includes(searchText)) score += 50
    }
  })
  
  // Boost for active/important entities
  if (entity.status === 'active') score += 10
  if (entity.type === 'premium' || entity.type === 'vip') score += 20
  
  return score
}

/**
 * Generate search suggestions based on query
 */
function generateSearchSuggestions(query: string): string[] {
  const suggestions: string[] = []
  const lowerQuery = query.toLowerCase()
  
  // Get from cache first
  const cached = searchSuggestions.get(lowerQuery)
  if (cached) return cached
  
  // Common search patterns and suggestions
  const commonSuggestions = [
    'active accounts', 'new contacts', 'pending orders', 
    'high value customers', 'recent products', 'overdue invoices'
  ]
  
  // Filter suggestions that start with or contain the query
  const filtered = commonSuggestions.filter(suggestion => 
    suggestion.toLowerCase().includes(lowerQuery)
  )
  
  suggestions.push(...filtered)
  
  // Cache suggestions
  searchSuggestions.set(lowerQuery, suggestions)
  
  return suggestions.slice(0, 5) // Limit to 5 suggestions
}

/**
 * Check rate limiting for IP address
 */
function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = requestCounts.get(clientIP)
  
  if (!clientData || now > clientData.resetTime) {
    // Reset or initialize count
    requestCounts.set(clientIP, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW
    })
    return true
  }
  
  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }
  
  clientData.count++
  return true
}

/**
 * Global search endpoint handler with security and performance optimizations
 */
export default defineEventHandler(async (event) => {
  const startTime = Date.now()
  let searchAnalytics: Partial<SearchAnalytics> = {}
  
  try {
    // Validate request method
    if (getMethod(event) !== 'POST') {
      throw createError({
        statusCode: 405,
        statusMessage: 'Method Not Allowed'
      })
    }
    
    // Rate limiting
    const clientIP = getClientIP(event) || 'unknown'
    if (!checkRateLimit(clientIP)) {
      throw createError({
        statusCode: 429,
        statusMessage: 'Too Many Requests',
        data: 'Rate limit exceeded. Please try again later.'
      })
    }
    
    // Parse and validate request body
    const body = await readBody(event)
    const validatedRequest = SearchRequestSchema.parse(body)
    
    const { query: searchQuery, filters = {}, limit: searchLimit } = validatedRequest
    
    // Initialize analytics tracking
    searchAnalytics = {
      query: searchQuery,
      entityTypes: filters.entityTypes || ['account', 'contact', 'company', 'product', 'order'],
      clientIP,
      workspaceId: filters.workspaceId
    }
    
    // Check cache first
    const cacheKey = generateCacheKey(searchQuery, filters, searchLimit)
    const cachedResult = getCachedResult(cacheKey)
    
    if (cachedResult) {
      // Track cache hit
      const responseTime = Date.now() - startTime
      trackSearchAnalytics({
        ...searchAnalytics,
        resultCount: cachedResult.results.length,
        responseTime,
        hasResults: cachedResult.results.length > 0
      } as SearchAnalytics)
      
      // Add to search history for cached results too
      addToSearchHistory(
        searchQuery,
        filters,
        cachedResult.results.length,
        responseTime,
        filters?.profileId,
        filters?.workspaceId
      )
      
      return cachedResult
    }
    
    // Determine entity types to search
    const entityTypes = filters.entityTypes || ['account', 'contact', 'company', 'product', 'order']
    
    // Calculate limit per entity type (distribute evenly)
    const limitPerType = Math.ceil(searchLimit / entityTypes.length)
    
    // Search across all entity types in parallel
    const searchPromises = entityTypes.map(entityType =>
      searchEntityCollection(entityType, searchQuery, filters, limitPerType)
    )
    
    const entityResults = await Promise.allSettled(searchPromises)
    
    // Flatten and combine successful results
    const allResults: SearchResult[] = []
    const errors: string[] = []
    
    entityResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        allResults.push(...result.value)
      } else {
        errors.push(`Failed to search ${entityTypes[index]}: ${result.reason.message}`)
      }
    })
    
    // Results are already sorted by relevance from searchEntityCollection
    // Apply final limit
    const limitedResults = allResults.slice(0, searchLimit)
    
    // Generate search suggestions for queries with few results
    const suggestions = allResults.length < 3 ? generateSearchSuggestions(searchQuery) : []
    
    // Prepare response with enhanced metadata
    const response: SearchResponse = {
      results: limitedResults,
      total: allResults.length,
      hasMore: allResults.length > searchLimit,
      nextCursor: allResults.length > searchLimit ? String(searchLimit) : undefined,
      suggestions, // Add search suggestions
      searchTime: Date.now() - startTime // Add response time
    }
    
    // Cache the result for future requests
    setCachedResult(cacheKey, response)
    
    // Track search analytics and search history
    const responseTime = Date.now() - startTime
    trackSearchAnalytics({
      ...searchAnalytics,
      resultCount: response.results.length,
      responseTime,
      hasResults: response.results.length > 0
    } as SearchAnalytics)
    
    // Add to search history (async, non-blocking)
    addToSearchHistory(
      searchQuery,
      filters,
      response.results.length,
      responseTime,
      filters?.profileId,
      filters?.workspaceId
    )
    
    // Set security headers
    setHeader(event, 'X-Content-Type-Options', 'nosniff')
    setHeader(event, 'X-Frame-Options', 'DENY')
    setHeader(event, 'X-XSS-Protection', '1; mode=block')
    
    // Include errors in response if any (for debugging)
    if (errors.length > 0) {
      console.warn('Search errors:', errors)
    }
    
    return response
    
  } catch (error) {
    console.error('Global search error:', error)
    
    if (error instanceof z.ZodError) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request',
        data: error.errors
      })
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
      data: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})