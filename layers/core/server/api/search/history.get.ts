import { z } from 'zod'
import { collection, query, where, orderBy, limit, getDocs, startAfter, doc } from 'firebase/firestore'
import { useFirestore } from '~/layers/core/composables/useFirestore'
import type { SearchHistoryEntry, SearchHistoryResponse } from '~/layers/crm/types/crm'

// Request schema validation
const SearchHistoryRequestSchema = z.object({
  userId: z.string()
    .min(1, 'User ID is required')
    .max(50, 'User ID too long'),
  workspaceId: z.string()
    .max(50, 'Workspace ID too long')
    .optional(),
  limit: z.number()
    .min(1, 'Limit must be at least 1')
    .max(50, 'Limit cannot exceed 50')
    .optional()
    .default(10),
  offset: z.number()
    .min(0, 'Offset cannot be negative')
    .optional()
    .default(0),
  cursor: z.string()
    .optional() // For cursor-based pagination
})

type SearchHistoryRequest = z.infer<typeof SearchHistoryRequestSchema>

/**
 * Format Firestore document to SearchHistoryEntry
 */
function formatHistoryEntry(doc: any): SearchHistoryEntry {
  const data = doc.data()
  
  return {
    id: doc.id,
    user_id: data.user_id,
    workspace_id: data.workspace_id,
    query: data.query || '',
    filters: data.filters || {},
    result_count: data.result_count || 0,
    search_time: data.search_time || 0,
    timestamp: data.timestamp?.toDate ? data.timestamp.toDate() : new Date(data.timestamp),
    active: data.active !== false // Default to true if not specified
  }
}

/**
 * Rate limiting for search history requests
 */
const requestCounts = new Map<string, { count: number; resetTime: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 60 // 60 requests per minute

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = requestCounts.get(clientIP)
  
  if (!clientData || now > clientData.resetTime) {
    requestCounts.set(clientIP, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW
    })
    return true
  }
  
  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }
  
  clientData.count++
  return true
}

/**
 * Get search history endpoint
 */
export default defineEventHandler(async (event) => {
  const startTime = Date.now()
  
  try {
    // Validate request method
    if (getMethod(event) !== 'GET') {
      throw createError({
        statusCode: 405,
        statusMessage: 'Method Not Allowed'
      })
    }
    
    // Rate limiting
    const clientIP = getClientIP(event) || 'unknown'
    if (!checkRateLimit(clientIP)) {
      throw createError({
        statusCode: 429,
        statusMessage: 'Too Many Requests',
        data: 'Rate limit exceeded. Please try again later.'
      })
    }
    
    // Parse and validate query parameters
    const query = getQuery(event)
    const validatedRequest = SearchHistoryRequestSchema.parse(query)
    
    const { userId, workspaceId, limit: requestLimit, offset, cursor } = validatedRequest
    
    // Initialize Firestore
    const { db } = useFirestore()
    const collectionRef = collection(db, 'search_history')
    
    // Build query constraints
    const constraints = []
    
    // Most selective filters first
    constraints.push(where('user_id', '==', userId))
    constraints.push(where('active', '==', true))
    
    if (workspaceId) {
      constraints.push(where('workspace_id', '==', workspaceId))
    }
    
    // Ordering
    constraints.push(orderBy('timestamp', 'desc'))
    
    // Cursor-based pagination (preferred over offset)
    if (cursor) {
      try {
        const cursorDoc = doc(db, 'search_history', cursor)
        const cursorSnapshot = await getDocs(query(collectionRef, where('__name__', '==', cursor)))
        if (!cursorSnapshot.empty) {
          constraints.push(startAfter(cursorSnapshot.docs[0]))
        }
      } catch (err) {
        console.warn('Invalid cursor provided, falling back to offset pagination:', err)
      }
    }
    
    // Limit
    constraints.push(limit(requestLimit))
    
    // Execute query with timeout protection
    const searchQuery = query(collectionRef, ...constraints)
    const querySnapshot = await Promise.race([
      getDocs(searchQuery),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Query timeout')), 5000)
      )
    ]) as any
    
    // Format results
    const entries = querySnapshot.docs.map(formatHistoryEntry)
    
    // Get total count for pagination info (only if needed and within reasonable limits)
    let total = 0
    let hasMore = false
    
    if (offset === 0 && !cursor) {
      // For first page, get a count estimate
      try {
        const countQuery = query(
          collectionRef,
          where('user_id', '==', userId),
          where('active', '==', true),
          ...(workspaceId ? [where('workspace_id', '==', workspaceId)] : [])
        )
        
        // Use a larger limit to get a better estimate without full count
        const countSnapshot = await getDocs(query(countQuery, limit(100)))
        total = countSnapshot.size
        
        // If we got the full limit, there might be more
        if (countSnapshot.size === 100) {
          hasMore = true
          total = -1 // Indicate unknown total
        } else {
          hasMore = entries.length === requestLimit
        }
      } catch (err) {
        console.warn('Failed to get count estimate:', err)
        hasMore = entries.length === requestLimit
        total = -1
      }
    } else {
      // For subsequent pages, just check if there might be more
      hasMore = entries.length === requestLimit
    }
    
    // Prepare response
    const response: SearchHistoryResponse = {
      entries,
      total: total >= 0 ? total : entries.length,
      hasMore
    }
    
    // Set security headers
    setHeader(event, 'X-Content-Type-Options', 'nosniff')
    setHeader(event, 'X-Frame-Options', 'DENY')
    setHeader(event, 'X-XSS-Protection', '1; mode=block')
    
    // Add response time header for monitoring
    setHeader(event, 'X-Response-Time', `${Date.now() - startTime}ms`)
    
    return response
    
  } catch (error) {
    console.error('Search history error:', error)
    
    if (error instanceof z.ZodError) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request parameters',
        data: error.errors
      })
    }
    
    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        throw createError({
          statusCode: 504,
          statusMessage: 'Request timeout',
          data: 'The request took too long to complete'
        })
      } else if (error.message.includes('permission')) {
        throw createError({
          statusCode: 403,
          statusMessage: 'Access denied',
          data: 'You do not have permission to access this resource'
        })
      }
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
      data: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})