import { z } from 'zod'
import { collection, addDoc, serverTimestamp, query, where, orderBy, limit, getDocs, writeBatch } from 'firebase/firestore'
import { useFirestore } from '~/layers/core/composables/useFirestore'
import type { SearchHistoryEntry } from '~/layers/crm/types/crm'

// Request schema validation
const CreateHistoryEntrySchema = z.object({
  user_id: z.string()
    .min(1, 'User ID is required')
    .max(50, 'User ID too long'),
  workspace_id: z.string()
    .max(50, 'Workspace ID too long')
    .optional(),
  query: z.string()
    .min(1, 'Query is required')
    .max(200, 'Query too long')
    .regex(/^[a-zA-Z0-9\s\-_@.!?]+$/, 'Query contains invalid characters'),
  filters: z.object({
    entityTypes: z.array(z.enum(['account', 'contact', 'company', 'product', 'order']))
      .max(5, 'Too many entity types')
      .optional(),
    workspaceId: z.string()
      .max(50, 'Workspace ID too long')
      .optional(),
    profileId: z.string()
      .max(50, 'Profile ID too long')
      .optional(),
    status: z.string()
      .max(20, 'Status too long')
      .optional(),
    tags: z.array(z.string().max(30, 'Tag too long'))
      .max(10, 'Too many tags')
      .optional()
  }).optional().default({}),
  result_count: z.number()
    .min(0, 'Result count cannot be negative')
    .max(10000, 'Result count too large')
    .optional()
    .default(0),
  search_time: z.number()
    .min(0, 'Search time cannot be negative')
    .max(60000, 'Search time too large') // Max 60 seconds
    .optional()
    .default(0)
})

type CreateHistoryEntryRequest = z.infer<typeof CreateHistoryEntrySchema>

/**
 * Rate limiting for creating history entries
 */
const requestCounts = new Map<string, { count: number; resetTime: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 30 // 30 requests per minute

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = requestCounts.get(clientIP)
  
  if (!clientData || now > clientData.resetTime) {
    requestCounts.set(clientIP, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW
    })
    return true
  }
  
  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }
  
  clientData.count++
  return true
}

/**
 * Check for duplicate recent searches to prevent spam
 */
async function isDuplicateSearch(
  userId: string, 
  query: string, 
  filters: any,
  workspaceId?: string
): Promise<boolean> {
  try {
    const { db } = useFirestore()
    const collectionRef = collection(db, 'search_history')
    
    // Check for duplicate searches in the last 5 minutes
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
    
    const constraints = [
      where('user_id', '==', userId),
      where('query', '==', query),
      where('active', '==', true),
      where('timestamp', '>=', fiveMinutesAgo),
      orderBy('timestamp', 'desc'),
      limit(1)
    ]
    
    if (workspaceId) {
      constraints.splice(-3, 0, where('workspace_id', '==', workspaceId))
    }
    
    const duplicateQuery = query(collectionRef, ...constraints)
    const snapshot = await getDocs(duplicateQuery)
    
    if (!snapshot.empty) {
      const recentEntry = snapshot.docs[0].data()
      // Check if filters are the same
      const filtersMatch = JSON.stringify(recentEntry.filters) === JSON.stringify(filters)
      return filtersMatch
    }
    
    return false
  } catch (err) {
    console.warn('Failed to check for duplicate search:', err)
    return false // Allow the search if we can't check
  }
}

/**
 * Clean up old history entries for a user (keep last 50)
 */
async function cleanupOldHistory(userId: string, workspaceId?: string): Promise<void> {
  try {
    const { db } = useFirestore()
    const collectionRef = collection(db, 'search_history')
    
    // Get all entries for user, ordered by timestamp desc
    const constraints = [
      where('user_id', '==', userId),
      where('active', '==', true),
      orderBy('timestamp', 'desc')
    ]
    
    if (workspaceId) {
      constraints.splice(-2, 0, where('workspace_id', '==', workspaceId))
    }
    
    const allEntriesQuery = query(collectionRef, ...constraints)
    const snapshot = await getDocs(allEntriesQuery)
    
    // If more than 50 entries, mark older ones as inactive
    if (snapshot.size > 50) {
      const batch = writeBatch(db)
      snapshot.docs.slice(50).forEach(doc => {
        batch.update(doc.ref, { active: false })
      })
      await batch.commit()
    }
  } catch (err) {
    console.error('Failed to cleanup old history:', err)
    // Don't throw error - cleanup is maintenance operation
  }
}

/**
 * Create search history entry endpoint
 */
export default defineEventHandler(async (event) => {
  const startTime = Date.now()
  
  try {
    // Validate request method
    if (getMethod(event) !== 'POST') {
      throw createError({
        statusCode: 405,
        statusMessage: 'Method Not Allowed'
      })
    }
    
    // Rate limiting
    const clientIP = getClientIP(event) || 'unknown'
    if (!checkRateLimit(clientIP)) {
      throw createError({
        statusCode: 429,
        statusMessage: 'Too Many Requests',
        data: 'Rate limit exceeded. Please try again later.'
      })
    }
    
    // Parse and validate request body
    const body = await readBody(event)
    const validatedRequest = CreateHistoryEntrySchema.parse(body)
    
    const { user_id, workspace_id, query: searchQuery, filters, result_count, search_time } = validatedRequest
    
    // Check for duplicate searches
    const isDuplicate = await isDuplicateSearch(user_id, searchQuery, filters, workspace_id)
    if (isDuplicate) {
      // Return success but don't create duplicate entry
      return {
        id: 'duplicate',
        message: 'Duplicate search detected, not added to history'
      }
    }
    
    // Initialize Firestore
    const { db } = useFirestore()
    const collectionRef = collection(db, 'search_history')
    
    // Prepare history entry data
    const historyEntry = {
      user_id,
      workspace_id: workspace_id || null,
      query: searchQuery.trim(),
      filters: filters || {},
      result_count,
      search_time,
      timestamp: serverTimestamp(),
      active: true,
      created_at: serverTimestamp()
    }
    
    // Create the document with timeout protection
    const docRef = await Promise.race([
      addDoc(collectionRef, historyEntry),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Create timeout')), 5000)
      )
    ]) as any
    
    // Clean up old entries asynchronously (don't wait for completion)
    cleanupOldHistory(user_id, workspace_id).catch(err => {
      console.error('Cleanup failed:', err)
    })
    
    // Set security headers
    setHeader(event, 'X-Content-Type-Options', 'nosniff')
    setHeader(event, 'X-Frame-Options', 'DENY')
    setHeader(event, 'X-XSS-Protection', '1; mode=block')
    
    // Add response time header for monitoring
    setHeader(event, 'X-Response-Time', `${Date.now() - startTime}ms`)
    
    return {
      id: docRef.id,
      message: 'Search history entry created successfully'
    }
    
  } catch (error) {
    console.error('Create search history error:', error)
    
    if (error instanceof z.ZodError) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request data',
        data: error.errors
      })
    }
    
    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        throw createError({
          statusCode: 504,
          statusMessage: 'Request timeout',
          data: 'The request took too long to complete'
        })
      } else if (error.message.includes('permission')) {
        throw createError({
          statusCode: 403,
          statusMessage: 'Access denied',
          data: 'You do not have permission to create this resource'
        })
      }
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
      data: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})