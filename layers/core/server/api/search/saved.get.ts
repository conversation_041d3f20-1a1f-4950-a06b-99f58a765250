import { z } from 'zod'
import { collection, query, where, orderBy, limit, getDocs, startAfter, doc } from 'firebase/firestore'
import { useFirestore } from '~/layers/core/composables/useFirestore'
import type { SavedSearch, SavedSearchResponse } from '~/layers/crm/types/crm'

// Request schema validation
const SavedSearchRequestSchema = z.object({
  userId: z.string()
    .min(1, 'User ID is required')
    .max(50, 'User ID too long'),
  workspaceId: z.string()
    .max(50, 'Workspace ID too long')
    .optional(),
  limit: z.number()
    .min(1, 'Limit must be at least 1')
    .max(100, 'Limit cannot exceed 100')
    .optional()
    .default(50),
  offset: z.number()
    .min(0, 'Offset cannot be negative')
    .optional()
    .default(0),
  includeInactive: z.boolean()
    .optional()
    .default(false),
  favoritesOnly: z.boolean()
    .optional()
    .default(false),
  cursor: z.string()
    .optional() // For cursor-based pagination
})

type SavedSearchRequest = z.infer<typeof SavedSearchRequestSchema>

/**
 * Format Firestore document to SavedSearch
 */
function formatSavedSearch(doc: any): SavedSearch {
  const data = doc.data()
  
  return {
    id: doc.id,
    user_id: data.user_id,
    workspace_id: data.workspace_id,
    name: data.name || '',
    description: data.description,
    query: data.query || '',
    filters: data.filters || {},
    is_favorite: data.is_favorite || false,
    notification_enabled: data.notification_enabled || false,
    created_at: data.created_at?.toDate ? data.created_at.toDate() : new Date(data.created_at),
    updated_at: data.updated_at?.toDate ? data.updated_at.toDate() : new Date(data.updated_at),
    last_executed: data.last_executed?.toDate ? data.last_executed.toDate() : (data.last_executed ? new Date(data.last_executed) : undefined),
    execution_count: data.execution_count || 0,
    active: data.active !== false, // Default to true if not specified
    tags: data.tags || []
  }
}

/**
 * Rate limiting for saved search requests
 */
const requestCounts = new Map<string, { count: number; resetTime: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 100 // 100 requests per minute

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = requestCounts.get(clientIP)
  
  if (!clientData || now > clientData.resetTime) {
    requestCounts.set(clientIP, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW
    })
    return true
  }
  
  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }
  
  clientData.count++
  return true
}

/**
 * Get saved searches endpoint
 */
export default defineEventHandler(async (event) => {
  const startTime = Date.now()
  
  try {
    // Validate request method
    if (getMethod(event) !== 'GET') {
      throw createError({
        statusCode: 405,
        statusMessage: 'Method Not Allowed'
      })
    }
    
    // Rate limiting
    const clientIP = getClientIP(event) || 'unknown'
    if (!checkRateLimit(clientIP)) {
      throw createError({
        statusCode: 429,
        statusMessage: 'Too Many Requests',
        data: 'Rate limit exceeded. Please try again later.'
      })
    }
    
    // Parse and validate query parameters
    const queryParams = getQuery(event)
    const validatedRequest = SavedSearchRequestSchema.parse(queryParams)
    
    const { 
      userId, 
      workspaceId, 
      limit: requestLimit, 
      offset, 
      includeInactive, 
      favoritesOnly,
      cursor 
    } = validatedRequest
    
    // Initialize Firestore
    const { db } = useFirestore()
    const collectionRef = collection(db, 'saved_searches')
    
    // Build query constraints
    const constraints = []
    
    // Most selective filters first
    constraints.push(where('user_id', '==', userId))
    
    if (!includeInactive) {
      constraints.push(where('active', '==', true))
    }
    
    if (workspaceId) {
      constraints.push(where('workspace_id', '==', workspaceId))
    }
    
    if (favoritesOnly) {
      constraints.push(where('is_favorite', '==', true))
    }
    
    // Ordering - favorites first, then by updated_at desc
    if (favoritesOnly) {
      constraints.push(orderBy('updated_at', 'desc'))
    } else {
      // Use compound ordering: favorites first, then by updated_at
      constraints.push(orderBy('is_favorite', 'desc'))
      constraints.push(orderBy('updated_at', 'desc'))
    }
    
    // Cursor-based pagination (preferred over offset)
    if (cursor) {
      try {
        const cursorDoc = doc(db, 'saved_searches', cursor)
        const cursorSnapshot = await getDocs(query(collectionRef, where('__name__', '==', cursor)))
        if (!cursorSnapshot.empty) {
          constraints.push(startAfter(cursorSnapshot.docs[0]))
        }
      } catch (err) {
        console.warn('Invalid cursor provided, falling back to offset pagination:', err)
      }
    }
    
    // Limit
    constraints.push(limit(requestLimit))
    
    // Execute query with timeout protection
    const searchQuery = query(collectionRef, ...constraints)
    const querySnapshot = await Promise.race([
      getDocs(searchQuery),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Query timeout')), 5000)
      )
    ]) as any
    
    // Format results
    const searches = querySnapshot.docs.map(formatSavedSearch)
    
    // Get total count for pagination info (only if needed and within reasonable limits)
    let total = 0
    let hasMore = false
    
    if (offset === 0 && !cursor) {
      // For first page, get a count estimate
      try {
        const countConstraints = [
          where('user_id', '==', userId),
          ...(includeInactive ? [] : [where('active', '==', true)]),
          ...(workspaceId ? [where('workspace_id', '==', workspaceId)] : []),
          ...(favoritesOnly ? [where('is_favorite', '==', true)] : [])
        ]
        
        const countQuery = query(collectionRef, ...countConstraints)
        
        // Use a larger limit to get a better estimate without full count
        const countSnapshot = await getDocs(query(countQuery, limit(200)))
        total = countSnapshot.size
        
        // If we got the full limit, there might be more
        if (countSnapshot.size === 200) {
          hasMore = true
          total = -1 // Indicate unknown total
        } else {
          hasMore = searches.length === requestLimit
        }
      } catch (err) {
        console.warn('Failed to get count estimate:', err)
        hasMore = searches.length === requestLimit
        total = -1
      }
    } else {
      // For subsequent pages, just check if there might be more
      hasMore = searches.length === requestLimit
    }
    
    // Prepare response
    const response: SavedSearchResponse = {
      searches,
      total: total >= 0 ? total : searches.length,
      hasMore
    }
    
    // Set security headers
    setHeader(event, 'X-Content-Type-Options', 'nosniff')
    setHeader(event, 'X-Frame-Options', 'DENY')
    setHeader(event, 'X-XSS-Protection', '1; mode=block')
    
    // Add response time header for monitoring
    setHeader(event, 'X-Response-Time', `${Date.now() - startTime}ms`)
    
    return response
    
  } catch (error) {
    console.error('Get saved searches error:', error)
    
    if (error instanceof z.ZodError) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request parameters',
        data: error.errors
      })
    }
    
    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        throw createError({
          statusCode: 504,
          statusMessage: 'Request timeout',
          data: 'The request took too long to complete'
        })
      } else if (error.message.includes('permission')) {
        throw createError({
          statusCode: 403,
          statusMessage: 'Access denied',
          data: 'You do not have permission to access this resource'
        })
      }
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
      data: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})