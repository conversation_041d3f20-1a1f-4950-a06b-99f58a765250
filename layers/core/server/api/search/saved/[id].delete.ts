import { z } from 'zod'
import { doc, updateDoc, deleteDoc, serverTimestamp, getDoc } from 'firebase/firestore'
import { useFirestore } from '~/layers/core/composables/useFirestore'

// Request schema validation
const DeleteSavedSearchSchema = z.object({
  user_id: z.string()
    .min(1, 'User ID is required')
    .max(50, 'User ID too long'),
  workspace_id: z.string()
    .max(50, 'Workspace ID too long')
    .optional(),
  hard_delete: z.boolean()
    .optional()
    .default(false) // Default to soft delete
})

type DeleteSavedSearchRequest = z.infer<typeof DeleteSavedSearchSchema>

/**
 * Rate limiting for deleting saved searches
 */
const requestCounts = new Map<string, { count: number; resetTime: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 20 // 20 requests per minute

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = requestCounts.get(clientIP)
  
  if (!clientData || now > clientData.resetTime) {
    requestCounts.set(clientIP, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW
    })
    return true
  }
  
  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }
  
  clientData.count++
  return true
}

/**
 * Check if user owns the saved search and get search data
 */
async function verifyOwnershipAndGetData(
  searchId: string, 
  userId: string, 
  workspaceId?: string
): Promise<{ isOwner: boolean; searchData?: any }> {
  try {
    const { db } = useFirestore()
    const docRef = doc(db, 'saved_searches', searchId)
    const docSnapshot = await getDoc(docRef)
    
    if (!docSnapshot.exists()) {
      return { isOwner: false }
    }
    
    const data = docSnapshot.data()
    
    // Check user ownership
    if (data.user_id !== userId) {
      return { isOwner: false }
    }
    
    // Check workspace context if provided
    if (workspaceId && data.workspace_id !== workspaceId) {
      return { isOwner: false }
    }
    
    // Check if search is already deleted (soft deleted)
    if (data.active === false) {
      return { isOwner: false }
    }
    
    return { isOwner: true, searchData: data }
  } catch (err) {
    console.error('Failed to verify ownership:', err)
    return { isOwner: false }
  }
}

/**
 * Delete saved search endpoint (soft delete by default)
 */
export default defineEventHandler(async (event) => {
  const startTime = Date.now()
  
  try {
    // Validate request method
    if (getMethod(event) !== 'DELETE') {
      throw createError({
        statusCode: 405,
        statusMessage: 'Method Not Allowed'
      })
    }
    
    // Rate limiting
    const clientIP = getClientIP(event) || 'unknown'
    if (!checkRateLimit(clientIP)) {
      throw createError({
        statusCode: 429,
        statusMessage: 'Too Many Requests',
        data: 'Rate limit exceeded. Please try again later.'
      })
    }
    
    // Get search ID from route params
    const searchId = getRouterParam(event, 'id')
    if (!searchId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid search ID',
        data: 'Search ID is required'
      })
    }
    
    // Validate search ID format
    if (!/^[a-zA-Z0-9_-]+$/.test(searchId) || searchId.length > 50) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid search ID format',
        data: 'Search ID contains invalid characters or is too long'
      })
    }
    
    // Parse and validate request body (for delete confirmation and options)
    const body = await readBody(event)
    const validatedRequest = DeleteSavedSearchSchema.parse(body)
    
    const { user_id, workspace_id, hard_delete } = validatedRequest
    
    // Verify ownership and get search data
    const { isOwner, searchData } = await verifyOwnershipAndGetData(searchId, user_id, workspace_id)
    if (!isOwner) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Not Found',
        data: 'Saved search not found or access denied'
      })
    }
    
    // Initialize Firestore
    const { db } = useFirestore()
    const docRef = doc(db, 'saved_searches', searchId)
    
    if (hard_delete) {
      // Hard delete - completely remove the document
      // Note: In production, you might want to restrict this or require special permissions
      await Promise.race([
        deleteDoc(docRef),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Delete timeout')), 5000)
        )
      ])
      
      return {
        id: searchId,
        name: searchData?.name || 'Unknown',
        message: 'Saved search permanently deleted'
      }
    } else {
      // Soft delete - mark as inactive
      const updateData = {
        active: false,
        deleted_at: serverTimestamp(),
        updated_at: serverTimestamp()
      }
      
      await Promise.race([
        updateDoc(docRef, updateData),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Update timeout')), 5000)
        )
      ])
      
      // Set security headers
      setHeader(event, 'X-Content-Type-Options', 'nosniff')
      setHeader(event, 'X-Frame-Options', 'DENY')
      setHeader(event, 'X-XSS-Protection', '1; mode=block')
      
      // Add response time header for monitoring
      setHeader(event, 'X-Response-Time', `${Date.now() - startTime}ms`)
      
      return {
        id: searchId,
        name: searchData?.name || 'Unknown',
        message: 'Saved search deleted successfully'
      }
    }
    
  } catch (error) {
    console.error('Delete saved search error:', error)
    
    if (error instanceof z.ZodError) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request data',
        data: error.errors
      })
    }
    
    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        throw createError({
          statusCode: 504,
          statusMessage: 'Request timeout',
          data: 'The request took too long to complete'
        })
      } else if (error.message.includes('permission')) {
        throw createError({
          statusCode: 403,
          statusMessage: 'Access denied',
          data: 'You do not have permission to delete this resource'
        })
      }
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
      data: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})