import { z } from 'zod'
import { collection, query, where, orderBy, limit, getDocs } from 'firebase/firestore'
import { useFirestore } from '~/layers/core/composables/useFirestore'

// Suggestions request schema
const SuggestionsRequestSchema = z.object({
  query: z.string().min(1).max(50),
  workspaceId: z.string().optional(),
  profileId: z.string().optional(),
  entityTypes: z.array(z.enum(['account', 'contact', 'company', 'product', 'order'])).optional(),
  limit: z.number().min(1).max(20).optional().default(10)
})

// Suggestions response interface
interface Suggestion {
  text: string
  type: 'completion' | 'popular' | 'entity'
  frequency?: number
  entityType?: string
}

interface SuggestionsResponse {
  suggestions: Suggestion[]
  query: string
}

// In-memory cache for popular suggestions
const popularSuggestionsCache = new Map<string, Suggestion[]>()
const CACHE_TTL = 30 * 60 * 1000 // 30 minutes

/**
 * Get cached popular suggestions
 */
function getCachedSuggestions(key: string): Suggestion[] | null {
  const cached = popularSuggestionsCache.get(key)
  if (cached) {
    return cached
  }
  return null
}

/**
 * Cache popular suggestions
 */
function setCachedSuggestions(key: string, suggestions: Suggestion[]): void {
  popularSuggestionsCache.set(key, suggestions)
  
  // Clean up cache periodically
  if (popularSuggestionsCache.size > 100) {
    const oldestEntries = Array.from(popularSuggestionsCache.keys()).slice(0, 20)
    oldestEntries.forEach(key => popularSuggestionsCache.delete(key))
  }
}

/**
 * Generate entity-based suggestions
 */
async function getEntitySuggestions(
  queryText: string,
  entityTypes: string[],
  workspaceId?: string,
  suggestionLimit: number = 5
): Promise<Suggestion[]> {
  const suggestions: Suggestion[] = []
  
  try {
    const { db } = useFirestore()
    const lowerQuery = queryText.toLowerCase()
    
    // Search for matching entities across specified types
    for (const entityType of entityTypes) {
      const collectionName = entityType === 'company' ? 'companies' : `${entityType}s`
      const collectionRef = collection(db, collectionName)
      
      const constraints = []
      
      // Add workspace filter if provided
      if (workspaceId) {
        constraints.push(where('workspace_ids', 'array-contains', workspaceId))
      }
      
      // Only active entities
      constraints.push(where('active', '==', true))
      
      // Order by name for prefix matching
      constraints.push(orderBy('name'))
      constraints.push(limit(20)) // Get more to filter locally
      
      const q = query(collectionRef, ...constraints)
      const querySnapshot = await getDocs(q)
      
      // Filter entities that start with or contain the query
      const entities = querySnapshot.docs
        .map(doc => ({ id: doc.id, ...doc.data() }))
        .filter(entity => {
          const name = entity.name?.toLowerCase() || ''
          return name.includes(lowerQuery)
        })
        .slice(0, 3) // Limit per entity type
      
      // Add entity suggestions
      entities.forEach(entity => {
        suggestions.push({
          text: entity.name,
          type: 'entity',
          entityType
        })
      })
    }
    
    return suggestions.slice(0, suggestionLimit)
  } catch (error) {
    console.error('Error getting entity suggestions:', error)
    return []
  }
}

/**
 * Get popular query suggestions from search analytics
 */
async function getPopularSuggestions(
  queryText: string,
  workspaceId?: string,
  suggestionLimit: number = 5
): Promise<Suggestion[]> {
  const cacheKey = `popular:${queryText}:${workspaceId || 'global'}`
  
  // Check cache first
  const cached = getCachedSuggestions(cacheKey)
  if (cached) {
    return cached.slice(0, suggestionLimit)
  }
  
  try {
    const { db } = useFirestore()
    const analyticsRef = collection(db, 'search_analytics')
    
    const constraints = []
    
    // Filter by workspace if provided
    if (workspaceId) {
      constraints.push(where('workspaceId', '==', workspaceId))
    }
    
    // Only queries with results
    constraints.push(where('hasResults', '==', true))
    
    // Recent queries (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    constraints.push(where('timestamp', '>=', thirtyDaysAgo))
    
    constraints.push(limit(1000)) // Get a good sample
    
    const q = query(analyticsRef, ...constraints)
    const querySnapshot = await getDocs(q)
    
    // Aggregate query frequencies
    const queryMap = new Map<string, number>()
    const lowerInput = queryText.toLowerCase()
    
    querySnapshot.docs.forEach(doc => {
      const data = doc.data()
      const searchQuery = data.query?.toLowerCase() || ''
      
      // Only include queries that start with or contain the input
      if (searchQuery.includes(lowerInput) && searchQuery !== lowerInput) {
        queryMap.set(data.query, (queryMap.get(data.query) || 0) + 1)
      }
    })
    
    // Convert to suggestions and sort by frequency
    const suggestions: Suggestion[] = Array.from(queryMap.entries())
      .map(([text, frequency]) => ({
        text,
        type: 'popular' as const,
        frequency
      }))
      .sort((a, b) => (b.frequency || 0) - (a.frequency || 0))
      .slice(0, suggestionLimit)
    
    // Cache the results
    setCachedSuggestions(cacheKey, suggestions)
    
    return suggestions
  } catch (error) {
    console.error('Error getting popular suggestions:', error)
    return []
  }
}

/**
 * Generate completion suggestions based on common patterns
 */
function getCompletionSuggestions(queryText: string, limit: number = 5): Suggestion[] {
  const lowerQuery = queryText.toLowerCase().trim()
  
  const completionPatterns = [
    // Account patterns
    { text: 'active accounts', pattern: ['active', 'acc'] },
    { text: 'new accounts this month', pattern: ['new', 'acc', 'month'] },
    { text: 'premium accounts', pattern: ['premium', 'acc'] },
    
    // Contact patterns
    { text: 'contacts by email', pattern: ['contact', 'email'] },
    { text: 'new contacts', pattern: ['new', 'contact'] },
    { text: 'contacts by company', pattern: ['contact', 'company'] },
    
    // Company patterns
    { text: 'companies by industry', pattern: ['company', 'industry'] },
    { text: 'large companies', pattern: ['large', 'company'] },
    { text: 'companies by revenue', pattern: ['company', 'revenue'] },
    
    // Product patterns
    { text: 'products by category', pattern: ['product', 'category'] },
    { text: 'out of stock products', pattern: ['stock', 'product'] },
    { text: 'bestselling products', pattern: ['best', 'product'] },
    
    // Order patterns
    { text: 'pending orders', pattern: ['pending', 'order'] },
    { text: 'orders this week', pattern: ['order', 'week'] },
    { text: 'high value orders', pattern: ['high', 'order', 'value'] },
    
    // Status patterns
    { text: 'active status', pattern: ['active'] },
    { text: 'pending status', pattern: ['pending'] },
    { text: 'completed status', pattern: ['completed'] }
  ]
  
  const suggestions: Suggestion[] = []
  
  completionPatterns.forEach(pattern => {
    // Check if the query matches any of the pattern words
    const matches = pattern.pattern.some(word => 
      word.includes(lowerQuery) || lowerQuery.includes(word)
    )
    
    if (matches && pattern.text.toLowerCase().includes(lowerQuery)) {
      suggestions.push({
        text: pattern.text,
        type: 'completion'
      })
    }
  })
  
  return suggestions
    .filter((suggestion, index, self) => 
      self.findIndex(s => s.text === suggestion.text) === index
    ) // Remove duplicates
    .slice(0, limit)
}

/**
 * Search suggestions endpoint
 * POST /api/search/suggestions
 */
export default defineEventHandler(async (event) => {
  try {
    // Validate request method
    if (getMethod(event) !== 'POST') {
      throw createError({
        statusCode: 405,
        statusMessage: 'Method Not Allowed'
      })
    }

    // Parse and validate request body
    const body = await readBody(event)
    const validatedRequest = SuggestionsRequestSchema.parse(body)

    const { query: queryText, workspaceId, profileId, entityTypes, limit: suggestionLimit } = validatedRequest

    // Get suggestions from different sources
    const [
      completionSuggestions,
      popularSuggestions,
      entitySuggestions
    ] = await Promise.all([
      getCompletionSuggestions(queryText, 3),
      getPopularSuggestions(queryText, workspaceId, 3),
      entityTypes ? getEntitySuggestions(queryText, entityTypes, workspaceId, 4) : []
    ])

    // Combine and deduplicate suggestions
    const allSuggestions = [
      ...completionSuggestions,
      ...popularSuggestions,
      ...entitySuggestions
    ]

    // Remove duplicates and sort by relevance
    const uniqueSuggestions = allSuggestions
      .filter((suggestion, index, self) => 
        self.findIndex(s => s.text.toLowerCase() === suggestion.text.toLowerCase()) === index
      )
      .sort((a, b) => {
        // Prioritize by type: entity > popular > completion
        const typePriority = { entity: 3, popular: 2, completion: 1 }
        const aPriority = typePriority[a.type] || 0
        const bPriority = typePriority[b.type] || 0
        
        if (aPriority !== bPriority) {
          return bPriority - aPriority
        }
        
        // Then by frequency if available
        return (b.frequency || 0) - (a.frequency || 0)
      })
      .slice(0, suggestionLimit)

    const response: SuggestionsResponse = {
      suggestions: uniqueSuggestions,
      query: queryText
    }

    // Set security headers
    setHeader(event, 'X-Content-Type-Options', 'nosniff')
    setHeader(event, 'X-Frame-Options', 'DENY')
    setHeader(event, 'X-XSS-Protection', '1; mode=block')

    return response

  } catch (error) {
    console.error('Search suggestions error:', error)

    if (error instanceof z.ZodError) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request',
        data: error.errors
      })
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
      data: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})