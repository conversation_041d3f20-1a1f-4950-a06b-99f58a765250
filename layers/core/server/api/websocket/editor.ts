import { WebSocket, WebSocketServer } from 'ws'
import jwt from 'jsonwebtoken'
import { getAuth } from 'firebase-admin/auth'
import { getFirestore } from 'firebase-admin/firestore'

interface EditorWebSocketClient extends WebSocket {
  workspaceId?: string
  projectId?: string
  userId?: string
  isAuthenticated?: boolean
}

interface EditorConnectionData {
  workspaceId: string
  projectId?: string
  userId?: string
  connectedAt: Date
}

// Connection pool for workspace-level sessions
const connectionPool = new Map<string, Set<EditorWebSocketClient>>()
const userConnections = new Map<string, EditorWebSocketClient>()

/**
 * Initialize WebSocket server for editor real-time collaboration
 * Implements Story 1.1 acceptance criteria
 */
export default defineWebSocketHandler({
  async open(peer) {
    const client = peer as EditorWebSocketClient
    console.log('WebSocket connection opened')
    
    // Extract authentication and connection parameters from URL
    const url = new URL(peer.url || '', 'ws://localhost')
    const token = url.searchParams.get('token')
    const workspaceId = url.searchParams.get('workspaceId')
    const projectId = url.searchParams.get('projectId')
    const userId = url.searchParams.get('userId')

    if (!token || !workspaceId) {
      client.close(1008, 'Missing required parameters')
      return
    }

    try {
      // Verify Firebase authentication token
      const auth = getAuth()
      const decodedToken = await auth.verifyIdToken(token)
      
      client.workspaceId = workspaceId
      client.projectId = projectId || undefined
      client.userId = userId || decodedToken.uid
      client.isAuthenticated = true

      // Add to workspace connection pool
      if (!connectionPool.has(workspaceId)) {
        connectionPool.set(workspaceId, new Set())
      }
      connectionPool.get(workspaceId)!.add(client)
      
      // Track user connection
      if (client.userId) {
        userConnections.set(client.userId, client)
      }

      console.log(`User ${client.userId} connected to workspace ${workspaceId}`)
      
      // Notify other users in the workspace about new connection
      broadcastToWorkspace(workspaceId, {
        type: 'user-joined',
        userId: client.userId,
        workspaceId,
        projectId,
        timestamp: Date.now()
      }, client)

      // Send connection confirmation
      client.send(JSON.stringify({
        type: 'connection-established',
        workspaceId,
        projectId,
        userId: client.userId,
        timestamp: Date.now()
      }))

    } catch (error: any) {
      console.error('WebSocket authentication failed:', error)
      client.close(1008, 'Authentication failed')
    }
  },

  async message(peer, message) {
    const client = peer as EditorWebSocketClient
    
    if (!client.isAuthenticated || !client.workspaceId) {
      client.close(1008, 'Not authenticated')
      return
    }

    try {
      const data = JSON.parse(message.text())
      await handleMessage(client, data)
    } catch (error) {
      console.error('Error handling WebSocket message:', error)
      client.send(JSON.stringify({
        type: 'error',
        message: 'Invalid message format',
        timestamp: Date.now()
      }))
    }
  },

  async close(peer, details) {
    const client = peer as EditorWebSocketClient
    console.log('WebSocket connection closed:', details)
    
    if (client.workspaceId && client.userId) {
      // Remove from connection pool
      const workspaceConnections = connectionPool.get(client.workspaceId)
      if (workspaceConnections) {
        workspaceConnections.delete(client)
        if (workspaceConnections.size === 0) {
          connectionPool.delete(client.workspaceId)
        }
      }

      // Remove user connection tracking
      userConnections.delete(client.userId)

      // Notify other users about disconnection
      broadcastToWorkspace(client.workspaceId, {
        type: 'user-left',
        userId: client.userId,
        workspaceId: client.workspaceId,
        projectId: client.projectId,
        timestamp: Date.now()
      })

      console.log(`User ${client.userId} disconnected from workspace ${client.workspaceId}`)
    }
  },

  async error(peer, error) {
    const client = peer as EditorWebSocketClient
    console.error('WebSocket error:', error)
    
    client.send(JSON.stringify({
      type: 'error',
      message: 'Internal server error',
      timestamp: Date.now()
    }))
  }
})

/**
 * Handle incoming WebSocket messages
 */
async function handleMessage(client: EditorWebSocketClient, data: any) {
  const { type, ...payload } = data

  switch (type) {
    case 'ping':
      // Heartbeat response
      client.send(JSON.stringify({
        type: 'pong',
        timestamp: Date.now()
      }))
      break

    case 'layer-update':
      await handleLayerUpdate(client, payload)
      break

    case 'cursor-position':
      await handleCursorUpdate(client, payload)
      break

    case 'user-presence':
      await handlePresenceUpdate(client, payload)
      break

    case 'project-sync-request':
      await handleProjectSyncRequest(client, payload)
      break

    case 'layer-operations-batch':
      await handleLayerOperationsBatch(client, payload)
      break

    case 'layer-sync-request':
      await handleLayerSyncRequest(client, payload)
      break

    case 'layer-sync-response':
      await handleLayerSyncResponse(client, payload)
      break

    case 'presence-update':
      await handlePresenceUpdate(client, payload)
      break

    default:
      console.warn('Unknown message type:', type)
      client.send(JSON.stringify({
        type: 'error',
        message: `Unknown message type: ${type}`,
        timestamp: Date.now()
      }))
  }
}

/**
 * Handle real-time layer updates
 */
async function handleLayerUpdate(client: EditorWebSocketClient, payload: any) {
  if (!client.workspaceId) return

  // Validate layer update data
  const { layerId, operation, data: layerData, projectId } = payload
  
  if (!layerId || !operation) {
    client.send(JSON.stringify({
      type: 'error',
      message: 'Invalid layer update data',
      timestamp: Date.now()
    }))
    return
  }

  // Broadcast layer update to all users in workspace except sender
  broadcastToWorkspace(client.workspaceId, {
    type: 'layer-update',
    layerId,
    operation,
    data: layerData,
    projectId,
    userId: client.userId,
    timestamp: Date.now()
  }, client)

  // Optionally persist to Firestore for conflict resolution
  if (projectId) {
    try {
      const db = getFirestore()
      await db.collection('editor_layer_updates').add({
        workspaceId: client.workspaceId,
        projectId,
        layerId,
        operation,
        data: layerData,
        userId: client.userId,
        timestamp: new Date()
      })
    } catch (error) {
      console.error('Error persisting layer update:', error)
    }
  }
}

/**
 * Handle cursor position updates for real-time collaboration
 */
async function handleCursorUpdate(client: EditorWebSocketClient, payload: any) {
  if (!client.workspaceId) return

  const { x, y, tool, layerId } = payload

  broadcastToWorkspace(client.workspaceId, {
    type: 'cursor-position',
    userId: client.userId,
    x,
    y,
    tool,
    layerId,
    timestamp: Date.now()
  }, client)
}

/**
 * Handle user presence updates
 */
async function handlePresenceUpdate(client: EditorWebSocketClient, payload: any) {
  if (!client.workspaceId) return

  const { status, currentTool, activeLayer } = payload

  broadcastToWorkspace(client.workspaceId, {
    type: 'user-presence',
    userId: client.userId,
    status,
    currentTool,
    activeLayer,
    timestamp: Date.now()
  }, client)
}

/**
 * Handle project synchronization requests
 */
async function handleProjectSyncRequest(client: EditorWebSocketClient, payload: any) {
  const { projectId } = payload
  
  if (!projectId || !client.workspaceId) {
    client.send(JSON.stringify({
      type: 'error',
      message: 'Invalid sync request',
      timestamp: Date.now()
    }))
    return
  }

  try {
    // Get current project state from Firestore
    const db = getFirestore()
    const projectDoc = await db.collection('editor_projects').doc(projectId).get()
    
    if (!projectDoc.exists) {
      client.send(JSON.stringify({
        type: 'error',
        message: 'Project not found',
        timestamp: Date.now()
      }))
      return
    }

    // Send current project state to client
    client.send(JSON.stringify({
      type: 'project-sync-response',
      projectId,
      data: projectDoc.data(),
      timestamp: Date.now()
    }))

  } catch (error) {
    console.error('Error handling project sync request:', error)
    client.send(JSON.stringify({
      type: 'error',
      message: 'Failed to sync project',
      timestamp: Date.now()
    }))
  }
}

/**
 * Handle batched layer operations for real-time synchronization
 * Implements Story 1.3: Real-time Layer Synchronization
 */
async function handleLayerOperationsBatch(client: EditorWebSocketClient, payload: any) {
  if (!client.workspaceId) return

  const { operations, projectId, timestamp } = payload
  
  if (!Array.isArray(operations) || operations.length === 0) {
    client.send(JSON.stringify({
      type: 'error',
      message: 'Invalid layer operations batch',
      timestamp: Date.now()
    }))
    return
  }

  try {
    // Validate operations
    const validOperations = operations.filter(op => 
      op.id && op.type && op.layerId && op.userId && op.timestamp
    )

    if (validOperations.length === 0) {
      client.send(JSON.stringify({
        type: 'error',
        message: 'No valid operations in batch',
        timestamp: Date.now()
      }))
      return
    }

    // Broadcast operations to all users in workspace except sender
    broadcastToWorkspace(client.workspaceId, {
      type: 'layer-operations-batch',
      operations: validOperations,
      projectId,
      senderId: client.userId,
      timestamp: Date.now()
    }, client)

    // Send confirmation back to sender
    client.send(JSON.stringify({
      type: 'layer-operation-confirmation',
      operationIds: validOperations.map(op => op.id),
      timestamp: Date.now()
    }))

    // Persist operations to Firestore for conflict resolution and history
    if (projectId) {
      const db = getFirestore()
      const batch = db.batch()
      
      validOperations.forEach(operation => {
        const docRef = db.collection('editor_layer_operations').doc()
        batch.set(docRef, {
          ...operation,
          workspaceId: client.workspaceId,
          projectId,
          serverTimestamp: new Date(),
          processed: true
        })
      })
      
      await batch.commit()
    }

    console.log(`Processed batch of ${validOperations.length} layer operations from ${client.userId}`)

  } catch (error) {
    console.error('Error processing layer operations batch:', error)
    client.send(JSON.stringify({
      type: 'error',
      message: 'Failed to process layer operations',
      timestamp: Date.now()
    }))
  }
}

/**
 * Handle layer synchronization requests
 */
async function handleLayerSyncRequest(client: EditorWebSocketClient, payload: any) {
  if (!client.workspaceId) return

  const { requestId, projectId } = payload

  try {
    if (projectId) {
      // Get current project state from Firestore
      const db = getFirestore()
      const projectDoc = await db.collection('editor_projects').doc(projectId).get()
      
      if (projectDoc.exists) {
        const projectData = projectDoc.data()
        
        // Get recent layer operations for version tracking
        const operationsQuery = await db
          .collection('editor_layer_operations')
          .where('projectId', '==', projectId)
          .where('workspaceId', '==', client.workspaceId)
          .orderBy('serverTimestamp', 'desc')
          .limit(100)
          .get()

        const recentOperations = operationsQuery.docs.map(doc => doc.data())

        // Send current state to requesting client
        client.send(JSON.stringify({
          type: 'layer-sync-response',
          requestId,
          data: {
            project: projectData,
            recentOperations,
            layers: projectData?.layers || [],
            timestamp: Date.now()
          },
          timestamp: Date.now()
        }))

        console.log(`Sent layer sync response to ${client.userId}`)
      } else {
        client.send(JSON.stringify({
          type: 'error',
          message: 'Project not found for sync',
          requestId,
          timestamp: Date.now()
        }))
      }
    } else {
      // Send empty sync response if no project
      client.send(JSON.stringify({
        type: 'layer-sync-response',
        requestId,
        data: {
          layers: [],
          recentOperations: [],
          timestamp: Date.now()
        },
        timestamp: Date.now()
      }))
    }

  } catch (error) {
    console.error('Error handling layer sync request:', error)
    client.send(JSON.stringify({
      type: 'error',
      message: 'Failed to process sync request',
      requestId,
      timestamp: Date.now()
    }))
  }
}

/**
 * Handle layer synchronization responses (when client sends state)
 */
async function handleLayerSyncResponse(client: EditorWebSocketClient, payload: any) {
  if (!client.workspaceId) return

  const { data, requestId } = payload

  // Broadcast sync data to other clients if needed
  broadcastToWorkspace(client.workspaceId, {
    type: 'layer-state-update',
    data,
    senderId: client.userId,
    timestamp: Date.now()
  }, client)

  console.log(`Processed layer sync response from ${client.userId}`)
}

/**
 * Enhanced presence update handler
 */
async function handlePresenceUpdateEnhanced(client: EditorWebSocketClient, payload: any) {
  if (!client.workspaceId) return

  const { subType, data: updateData } = payload

  // Broadcast presence update to all users in workspace except sender
  broadcastToWorkspace(client.workspaceId, {
    type: 'presence-update',
    subType,
    data: updateData,
    senderId: client.userId,
    workspaceId: client.workspaceId,
    projectId: client.projectId,
    timestamp: Date.now()
  }, client)

  // Handle specific presence events
  switch (subType) {
    case 'user-joined':
      console.log(`User ${updateData.name || client.userId} joined workspace ${client.workspaceId}`)
      break
    case 'user-left':
      console.log(`User ${client.userId} left workspace ${client.workspaceId}`)
      break
    case 'layer-focus':
      // Track which layer user is currently working on
      console.log(`User ${client.userId} focused on layer ${updateData.layerId}`)
      break
  }
}

/**
 * Broadcast message to all connections in a workspace
 */
function broadcastToWorkspace(workspaceId: string, message: any, excludeClient?: EditorWebSocketClient) {
  const workspaceConnections = connectionPool.get(workspaceId)
  if (!workspaceConnections) return

  const messageStr = JSON.stringify(message)
  
  workspaceConnections.forEach(client => {
    if (client !== excludeClient && client.readyState === WebSocket.OPEN) {
      try {
        client.send(messageStr)
      } catch (error) {
        console.error('Error broadcasting message:', error)
        // Remove failed connection
        workspaceConnections.delete(client)
      }
    }
  })
}

/**
 * Get active users in a workspace
 */
export function getWorkspaceUsers(workspaceId: string): string[] {
  const workspaceConnections = connectionPool.get(workspaceId)
  if (!workspaceConnections) return []

  return Array.from(workspaceConnections)
    .filter(client => client.userId)
    .map(client => client.userId!)
}

/**
 * Send message to specific user
 */
export function sendToUser(userId: string, message: any): boolean {
  const client = userConnections.get(userId)
  if (!client || client.readyState !== WebSocket.OPEN) {
    return false
  }

  try {
    client.send(JSON.stringify(message))
    return true
  } catch (error) {
    console.error('Error sending message to user:', error)
    return false
  }
}