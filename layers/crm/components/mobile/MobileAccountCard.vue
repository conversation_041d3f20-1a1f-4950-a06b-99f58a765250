<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface Account {
  id: string
  name: string
  company?: string
  email: string
  phone: string
  avatar?: string
  status: 'active' | 'inactive' | 'prospect'
  lastContact?: Date
  value?: number
  tags?: string[]
  location?: {
    lat: number
    lng: number
    address: string
  }
}

interface Props {
  account: Account
  showActions?: boolean
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true,
  compact: false
})

const emit = defineEmits<{
  call: [account: Account]
  text: [account: Account]
  email: [account: Account]
  edit: [account: Account]
  delete: [account: Account]
  navigate: [account: Account]
}>()

// Touch and swipe handling
const cardRef = ref<HTMLElement>()
const swipeOffset = ref(0)
const isSwipeActive = ref(false)
const touchStartX = ref(0)
const touchStartY = ref(0)
const actionRevealThreshold = 80 // pixels to reveal actions

// Animation state
const isAnimating = ref(false)

// Status styling
const statusConfig = computed(() => {
  const configs = {
    active: {
      color: 'success',
      label: 'Active',
      dot: 'bg-success-500'
    },
    inactive: {
      color: 'muted',
      label: 'Inactive',
      dot: 'bg-muted-400'
    },
    prospect: {
      color: 'warning',
      label: 'Prospect',
      dot: 'bg-warning-500'
    }
  }
  return configs[props.account.status] || configs.active
})

// Format last contact time
const lastContactFormatted = computed(() => {
  if (!props.account.lastContact) return 'Never'
  
  const now = new Date()
  const diff = now.getTime() - props.account.lastContact.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) return 'Today'
  if (days === 1) return 'Yesterday'
  if (days < 7) return `${days} days ago`
  if (days < 30) return `${Math.floor(days / 7)} weeks ago`
  return `${Math.floor(days / 30)} months ago`
})

// Touch event handlers
const handleTouchStart = (event: TouchEvent) => {
  if (!props.showActions) return
  
  const touch = event.touches[0]
  touchStartX.value = touch.clientX
  touchStartY.value = touch.clientY
  isSwipeActive.value = true
  isAnimating.value = false
}

const handleTouchMove = (event: TouchEvent) => {
  if (!isSwipeActive.value || !props.showActions) return
  
  const touch = event.touches[0]
  const deltaX = touch.clientX - touchStartX.value
  const deltaY = Math.abs(touch.clientY - touchStartY.value)
  
  // Ignore vertical swipes
  if (deltaY > 30) {
    isSwipeActive.value = false
    return
  }
  
  // Only allow left swipe (negative deltaX)
  if (deltaX < 0) {
    swipeOffset.value = Math.max(deltaX, -actionRevealThreshold * 1.2)
    event.preventDefault()
  }
}

const handleTouchEnd = () => {
  if (!isSwipeActive.value) return
  
  isSwipeActive.value = false
  isAnimating.value = true
  
  // Snap to position based on swipe distance
  if (Math.abs(swipeOffset.value) > actionRevealThreshold / 2) {
    swipeOffset.value = -actionRevealThreshold
  } else {
    swipeOffset.value = 0
  }
  
  // Reset animation flag after transition
  setTimeout(() => {
    isAnimating.value = false
  }, 300)
}

// Handle tap on card (when not swiped)
const handleCardTap = () => {
  if (Math.abs(swipeOffset.value) < 10) {
    emit('navigate', props.account)
  } else {
    // Reset swipe if card is swiped
    swipeOffset.value = 0
    isAnimating.value = true
    setTimeout(() => {
      isAnimating.value = false
    }, 300)
  }
}

// Quick action handlers
const handleCall = (event: Event) => {
  event.stopPropagation()
  emit('call', props.account)
  resetSwipe()
}

const handleText = (event: Event) => {
  event.stopPropagation()
  emit('text', props.account)
  resetSwipe()
}

const handleEmail = (event: Event) => {
  event.stopPropagation()
  emit('email', props.account)
  resetSwipe()
}

const resetSwipe = () => {
  swipeOffset.value = 0
  isAnimating.value = true
  setTimeout(() => {
    isAnimating.value = false
  }, 300)
}

// Reset swipe when clicking outside
const handleClickOutside = (event: Event) => {
  if (cardRef.value && !cardRef.value.contains(event.target as Node)) {
    resetSwipe()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// Transform style for swipe animation
const cardTransform = computed(() => {
  return {
    transform: `translateX(${swipeOffset.value}px)`,
    transition: isAnimating.value ? 'transform 0.3s ease-out' : 'none'
  }
})
</script>

<template>
  <div class="relative overflow-hidden">
    <!-- Action buttons (revealed on swipe) -->
    <div 
      v-if="showActions"
      class="absolute inset-y-0 right-0 flex items-center"
      :style="{ width: `${actionRevealThreshold}px` }"
    >
      <div class="flex h-full">
        <!-- Call button -->
        <button
          class="flex-1 bg-success-500 hover:bg-success-600 flex items-center justify-center text-white transition-colors touch-manipulation"
          @click="handleCall"
          :style="{ minWidth: '44px' }"
        >
          <Icon name="lucide:phone" class="h-5 w-5" />
        </button>
        
        <!-- Text button -->
        <button
          class="flex-1 bg-info-500 hover:bg-info-600 flex items-center justify-center text-white transition-colors touch-manipulation"
          @click="handleText"
          :style="{ minWidth: '44px' }"
        >
          <Icon name="lucide:message-circle" class="h-5 w-5" />
        </button>
        
        <!-- Email button -->
        <button
          class="flex-1 bg-warning-500 hover:bg-warning-600 flex items-center justify-center text-white transition-colors touch-manipulation"
          @click="handleEmail"
          :style="{ minWidth: '44px' }"
        >
          <Icon name="lucide:mail" class="h-5 w-5" />
        </button>
      </div>
    </div>

    <!-- Main card content -->
    <BaseCard
      ref="cardRef"
      :class="[
        'relative z-10 touch-manipulation cursor-pointer',
        compact ? 'p-3' : 'p-4'
      ]"
      :style="cardTransform"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      @click="handleCardTap"
    >
      <div class="flex items-center space-x-3">
        <!-- Avatar -->
        <div class="flex-shrink-0">
          <BaseAvatar
            v-if="account.avatar"
            :src="account.avatar"
            :size="compact ? 'sm' : 'md'"
          />
          <div
            v-else
            :class="[
              'rounded-full bg-primary-100 dark:bg-primary-900/20 flex items-center justify-center',
              compact ? 'h-8 w-8' : 'h-12 w-12'
            ]"
          >
            <Icon 
              name="lucide:user" 
              :class="[
                'text-primary-600 dark:text-primary-400',
                compact ? 'h-4 w-4' : 'h-5 w-5'
              ]"
            />
          </div>
        </div>

        <!-- Account info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2 mb-1">
            <BaseHeading 
              as="h3" 
              :size="compact ? 'sm' : 'md'" 
              weight="semibold" 
              class="truncate"
            >
              {{ account.name }}
            </BaseHeading>
            
            <!-- Status indicator -->
            <div class="flex items-center space-x-1">
              <div 
                :class="[
                  'h-2 w-2 rounded-full',
                  statusConfig.dot
                ]"
              />
              <BaseText size="xs" class="text-muted-500">
                {{ statusConfig.label }}
              </BaseText>
            </div>
          </div>

          <!-- Company and contact info -->
          <div class="space-y-1">
            <BaseText 
              v-if="account.company" 
              :size="compact ? 'xs' : 'sm'" 
              class="text-muted-600 truncate"
            >
              {{ account.company }}
            </BaseText>
            
            <div class="flex items-center space-x-4">
              <BaseText 
                :size="compact ? 'xs' : 'sm'" 
                class="text-muted-500 truncate"
              >
                {{ account.email }}
              </BaseText>
              
              <BaseText 
                v-if="!compact" 
                size="xs" 
                class="text-muted-400"
              >
                {{ lastContactFormatted }}
              </BaseText>
            </div>
          </div>

          <!-- Tags (non-compact only) -->
          <div v-if="!compact && account.tags?.length" class="flex flex-wrap gap-1 mt-2">
            <BaseBadge
              v-for="tag in account.tags.slice(0, 3)"
              :key="tag"
              size="xs"
              variant="pastel"
              color="muted"
            >
              {{ tag }}
            </BaseBadge>
            <BaseBadge
              v-if="account.tags.length > 3"
              size="xs"
              variant="pastel"
              color="muted"
            >
              +{{ account.tags.length - 3 }}
            </BaseBadge>
          </div>
        </div>

        <!-- Value and chevron -->
        <div class="flex-shrink-0 flex items-center space-x-2">
          <div v-if="account.value && !compact" class="text-right">
            <BaseText size="sm" weight="semibold" class="text-success-600 dark:text-success-400">
              ${{ account.value.toLocaleString() }}
            </BaseText>
          </div>
          
          <Icon 
            name="lucide:chevron-right" 
            class="h-4 w-4 text-muted-400"
          />
        </div>
      </div>

      <!-- Swipe indicator (subtle hint) -->
      <div 
        v-if="showActions && swipeOffset === 0"
        class="absolute top-1/2 right-2 transform -translate-y-1/2 opacity-30"
      >
        <Icon name="lucide:chevrons-left" class="h-3 w-3 text-muted-400" />
      </div>
    </BaseCard>
  </div>
</template>

<style scoped>
/* Prevent text selection during swipe */
.touch-manipulation {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  touch-action: pan-y; /* Allow vertical scrolling but handle horizontal */
}

/* Smooth transitions */
.transition-transform {
  transition: transform 0.3s ease-out;
}

/* Ensure minimum touch targets */
button.touch-manipulation {
  min-height: 44px;
  min-width: 44px;
}

/* Prevent accidental selections */
.cursor-pointer {
  cursor: pointer;
}

@media (hover: hover) {
  .hover\:bg-success-600:hover {
    background-color: rgb(34 197 94);
  }
  
  .hover\:bg-info-600:hover {
    background-color: rgb(37 99 235);
  }
  
  .hover\:bg-warning-600:hover {
    background-color: rgb(217 119 6);
  }
}
</style>