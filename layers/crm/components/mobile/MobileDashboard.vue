<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useIndexedDBCache } from '../../../core/composables/useIndexedDBCache'
import { useCrmState } from '../../composables/useCrmState'

const { cacheLayer, getCachedLayer, isInitialized } = useIndexedDBCache({
  dbName: 'CRMMobileCache',
  maxCacheSize: 50, // 50MB for mobile
  maxAge: 12 * 60 * 60 * 1000 // 12 hours for mobile
})

const { accounts, contacts, recentActivity, keyMetrics } = useCrmState()

// Mobile-optimized state
const isOnline = ref(navigator.onLine)
const lastSyncTime = ref<Date | null>(null)
const syncStatus = ref<'synced' | 'syncing' | 'error' | 'pending'>('synced')

// Handle online/offline status
const updateOnlineStatus = () => {
  isOnline.value = navigator.onLine
  if (navigator.onLine && syncStatus.value === 'pending') {
    syncData()
  }
}

window.addEventListener('online', updateOnlineStatus)
window.addEventListener('offline', updateOnlineStatus)

// Dashboard stats optimized for mobile viewing
const dashboardStats = computed(() => [
  {
    label: 'Accounts',
    value: accounts.value?.length || 0,
    icon: 'lucide:user-circle',
    color: 'primary',
    trend: '+12%'
  },
  {
    label: 'Contacts',
    value: contacts.value?.length || 0,
    icon: 'lucide:users',
    color: 'success',
    trend: '+8%'
  },
  {
    label: 'Pipeline',
    value: keyMetrics.value?.pipeline || '$0',
    icon: 'lucide:trending-up',
    color: 'info',
    trend: '+15%'
  },
  {
    label: 'Tasks',
    value: keyMetrics.value?.pendingTasks || 0,
    icon: 'lucide:check-square',
    color: 'warning',
    trend: '-5%'
  }
])

// Quick actions for mobile
const quickActions = ref([
  {
    id: 'add-contact',
    label: 'Add Contact',
    icon: 'lucide:user-plus',
    color: 'primary',
    action: () => navigateTo('/crm/contacts/create')
  },
  {
    id: 'scan-card',
    label: 'Scan Card',
    icon: 'lucide:camera',
    color: 'success',
    action: () => openCamera()
  },
  {
    id: 'voice-note',
    label: 'Voice Note',
    icon: 'lucide:mic',
    color: 'info',
    action: () => startVoiceNote()
  },
  {
    id: 'check-in',
    label: 'Check In',
    icon: 'lucide:map-pin',
    color: 'warning',
    action: () => performCheckIn()
  }
])

// Sync functionality
const syncData = async () => {
  if (!isOnline.value) {
    syncStatus.value = 'pending'
    return
  }

  try {
    syncStatus.value = 'syncing'
    
    // Cache current data for offline access
    await cacheLayer('dashboard-stats', 'core', dashboardStats.value)
    await cacheLayer('recent-activity', 'core', recentActivity.value)
    
    lastSyncTime.value = new Date()
    syncStatus.value = 'synced'
  } catch (error) {
    console.error('Sync failed:', error)
    syncStatus.value = 'error'
  }
}

// Placeholder functions for mobile features
const openCamera = () => {
  // Will be implemented in camera integration task
  console.log('Opening camera for business card scan')
}

const startVoiceNote = () => {
  // Will be implemented in voice notes task
  console.log('Starting voice note recording')
}

const performCheckIn = () => {
  // Will be implemented in GPS check-ins task
  console.log('Performing GPS check-in')
}

// Load cached data if offline
const loadCachedData = async () => {
  if (!isOnline.value && isInitialized.value) {
    const cachedStats = await getCachedLayer('dashboard-stats')
    const cachedActivity = await getCachedLayer('recent-activity')
    
    if (cachedStats) {
      // Use cached data when offline
      console.log('Using cached dashboard data')
    }
  }
}

onMounted(() => {
  syncData()
  loadCachedData()
})

// Format time for mobile display
const formatSyncTime = (date: Date | null) => {
  if (!date) return 'Never'
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.round(diffMs / 60000)
  
  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins}m ago`
  const diffHours = Math.round(diffMins / 60)
  if (diffHours < 24) return `${diffHours}h ago`
  return date.toLocaleDateString()
}

// Touch event handling
const handleTouchStart = (event: TouchEvent) => {
  // Store initial touch position for swipe detection
  const touch = event.touches[0]
  event.currentTarget.setAttribute('data-touch-start-x', touch.clientX.toString())
  event.currentTarget.setAttribute('data-touch-start-y', touch.clientY.toString())
}

const handleTouchEnd = (event: TouchEvent, action: () => void) => {
  const touch = event.changedTouches[0]
  const startX = parseFloat(event.currentTarget.getAttribute('data-touch-start-x') || '0')
  const startY = parseFloat(event.currentTarget.getAttribute('data-touch-start-y') || '0')
  
  const deltaX = Math.abs(touch.clientX - startX)
  const deltaY = Math.abs(touch.clientY - startY)
  
  // If it's a tap (minimal movement), execute action
  if (deltaX < 10 && deltaY < 10) {
    action()
  }
}
</script>

<template>
  <div class="min-h-screen bg-muted-50 dark:bg-muted-900">
    <!-- Mobile Header with Sync Status -->
    <div class="sticky top-0 z-40 bg-white dark:bg-muted-800 border-b border-muted-200 dark:border-muted-700">
      <div class="flex items-center justify-between px-4 py-3">
        <div class="flex items-center space-x-3">
          <BaseHeading as="h1" size="lg" weight="bold">
            CRM Mobile
          </BaseHeading>
          
          <!-- Sync Status Indicator -->
          <div class="flex items-center space-x-1">
            <div 
              :class="[
                'h-2 w-2 rounded-full',
                {
                  'bg-success-500': syncStatus === 'synced',
                  'bg-warning-500 animate-pulse': syncStatus === 'syncing',
                  'bg-danger-500': syncStatus === 'error',
                  'bg-muted-400': syncStatus === 'pending'
                }
              ]"
            />
            <BaseText size="xs" class="text-muted-500">
              {{ formatSyncTime(lastSyncTime) }}
            </BaseText>
          </div>
        </div>

        <!-- Connection Status -->
        <div class="flex items-center space-x-2">
          <Icon 
            :name="isOnline ? 'lucide:wifi' : 'lucide:wifi-off'" 
            :class="[
              'h-4 w-4',
              isOnline ? 'text-success-500' : 'text-danger-500'
            ]"
          />
          <BaseText size="xs" class="text-muted-500">
            {{ isOnline ? 'Online' : 'Offline' }}
          </BaseText>
        </div>
      </div>
    </div>

    <!-- Dashboard Content -->
    <div class="px-4 py-6">
      <!-- Key Metrics Cards -->
      <div class="grid grid-cols-2 gap-4 mb-6">
        <BaseCard
          v-for="stat in dashboardStats"
          :key="stat.label"
          class="p-4 touch-manipulation"
          @touchstart="handleTouchStart"
          @touchend="(e) => handleTouchEnd(e, () => console.log('Navigate to', stat.label))"
        >
          <div class="flex items-center justify-between mb-2">
            <div 
              :class="[
                'h-8 w-8 rounded-lg flex items-center justify-center',
                `bg-${stat.color}-100 dark:bg-${stat.color}-900/20`
              ]"
            >
              <Icon 
                :name="stat.icon" 
                :class="[
                  'h-4 w-4',
                  `text-${stat.color}-600 dark:text-${stat.color}-400`
                ]"
              />
            </div>
            <BaseText size="xs" class="text-success-500 font-medium">
              {{ stat.trend }}
            </BaseText>
          </div>
          <BaseText size="lg" weight="bold" class="mb-1">
            {{ stat.value }}
          </BaseText>
          <BaseText size="xs" class="text-muted-500">
            {{ stat.label }}
          </BaseText>
        </BaseCard>
      </div>

      <!-- Quick Actions -->
      <div class="mb-6">
        <BaseHeading as="h2" size="md" weight="semibold" class="mb-4">
          Quick Actions
        </BaseHeading>
        <div class="grid grid-cols-2 gap-3">
          <BaseButton
            v-for="action in quickActions"
            :key="action.id"
            :color="action.color"
            variant="pastel"
            size="lg"
            class="h-16 touch-manipulation flex-col space-y-1"
            @click="action.action"
            @touchstart="handleTouchStart"
            @touchend="(e) => handleTouchEnd(e, action.action)"
          >
            <Icon :name="action.icon" class="h-5 w-5" />
            <BaseText size="xs" weight="medium">
              {{ action.label }}
            </BaseText>
          </BaseButton>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="mb-6">
        <div class="flex items-center justify-between mb-4">
          <BaseHeading as="h2" size="md" weight="semibold">
            Recent Activity
          </BaseHeading>
          <BaseButton size="xs" variant="pastel" color="muted">
            View All
          </BaseButton>
        </div>
        
        <div class="space-y-3">
          <BaseCard
            v-for="activity in recentActivity?.slice(0, 5) || []"
            :key="activity.id"
            class="p-4 touch-manipulation"
          >
            <div class="flex items-start space-x-3">
              <BaseAvatar
                v-if="activity.avatar"
                :src="activity.avatar"
                size="sm"
              />
              <div
                v-else
                class="h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-900/20 flex items-center justify-center"
              >
                <Icon name="lucide:user" class="h-4 w-4 text-primary-600 dark:text-primary-400" />
              </div>
              
              <div class="flex-1 min-w-0">
                <BaseText size="sm" weight="medium" class="mb-1">
                  {{ activity.title }}
                </BaseText>
                <BaseText size="xs" class="text-muted-500 mb-1">
                  {{ activity.description }}
                </BaseText>
                <BaseText size="xs" class="text-muted-400">
                  {{ activity.timestamp }}
                </BaseText>
              </div>
            </div>
          </BaseCard>
        </div>
      </div>

      <!-- Offline Notice -->
      <div 
        v-if="!isOnline"
        class="bg-warning-50 dark:bg-warning-900/20 border border-warning-200 dark:border-warning-800 rounded-lg p-4"
      >
        <div class="flex items-center space-x-2">
          <Icon name="lucide:wifi-off" class="h-4 w-4 text-warning-600 dark:text-warning-400" />
          <BaseText size="sm" class="text-warning-700 dark:text-warning-300">
            You're offline. Some features may be limited.
          </BaseText>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Mobile-specific optimizations */
.touch-manipulation {
  touch-action: manipulation; /* Prevents double-tap zoom */
  -webkit-touch-callout: none; /* Prevents callout menu */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Ensure minimum touch target size (44px) */
.touch-manipulation {
  min-height: 44px;
  min-width: 44px;
}

/* Smooth transitions for mobile */
.transition-all {
  transition: all 0.2s ease-in-out;
}

/* Optimized scrolling for mobile */
.overflow-scroll {
  -webkit-overflow-scrolling: touch;
}
</style>