<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface Contact {
  id: string
  name: string
  phone?: string
  email?: string
  company?: string
}

interface Props {
  contact?: Contact
  layout?: 'horizontal' | 'vertical' | 'grid'
  size?: 'sm' | 'md' | 'lg'
  showLabels?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  layout: 'horizontal',
  size: 'md',
  showLabels: true
})

const emit = defineEmits<{
  call: [contact: Contact]
  text: [contact: Contact]
  email: [contact: Contact]
  voiceNote: []
  camera: []
  location: []
}>()

// Haptic feedback support
const hasHapticFeedback = ref(false)

onMounted(() => {
  // Check for haptic feedback support
  hasHapticFeedback.value = 'vibrate' in navigator
})

// Trigger haptic feedback
const triggerHaptic = (type: 'light' | 'medium' | 'heavy' = 'light') => {
  if (!hasHapticFeedback.value) return
  
  try {
    // Use Web Vibration API as fallback
    const patterns = {
      light: [10],
      medium: [20],
      heavy: [30]
    }
    navigator.vibrate(patterns[type])
  } catch (error) {
    console.log('Haptic feedback not available')
  }
}

// Action configurations
const actions = computed(() => [
  {
    id: 'call',
    icon: 'lucide:phone',
    label: 'Call',
    color: 'success',
    disabled: !props.contact?.phone,
    action: () => handleCall(),
    testId: 'quick-action-call'
  },
  {
    id: 'text',
    icon: 'lucide:message-circle',
    label: 'Text',
    color: 'info',
    disabled: !props.contact?.phone,
    action: () => handleText(),
    testId: 'quick-action-text'
  },
  {
    id: 'email',
    icon: 'lucide:mail',
    label: 'Email',
    color: 'warning',
    disabled: !props.contact?.email,
    action: () => handleEmail(),
    testId: 'quick-action-email'
  },
  {
    id: 'voice-note',
    icon: 'lucide:mic',
    label: 'Voice',
    color: 'primary',
    disabled: false,
    action: () => handleVoiceNote(),
    testId: 'quick-action-voice'
  },
  {
    id: 'camera',
    icon: 'lucide:camera',
    label: 'Camera',
    color: 'violet',
    disabled: false,
    action: () => handleCamera(),
    testId: 'quick-action-camera'
  },
  {
    id: 'location',
    icon: 'lucide:map-pin',
    label: 'Location',
    color: 'rose',
    disabled: false,
    action: () => handleLocation(),
    testId: 'quick-action-location'
  }
])

// Size configurations
const sizeConfig = computed(() => {
  const configs = {
    sm: {
      button: 'h-10 w-10',
      icon: 'h-4 w-4',
      text: 'text-xs',
      spacing: 'space-x-2'
    },
    md: {
      button: 'h-12 w-12',
      icon: 'h-5 w-5',
      text: 'text-sm',
      spacing: 'space-x-3'
    },
    lg: {
      button: 'h-16 w-16',
      icon: 'h-6 w-6',
      text: 'text-base',
      spacing: 'space-x-4'
    }
  }
  return configs[props.size]
})

// Layout classes
const layoutClasses = computed(() => {
  const layouts = {
    horizontal: `flex items-center ${sizeConfig.value.spacing} overflow-x-auto pb-2`,
    vertical: 'flex flex-col space-y-3',
    grid: 'grid grid-cols-3 gap-3'
  }
  return layouts[props.layout]
})

// Action handlers
const handleCall = async () => {
  if (!props.contact?.phone) return
  
  triggerHaptic('medium')
  emit('call', props.contact)
  
  try {
    // Attempt to initiate call using tel: protocol
    window.location.href = `tel:${props.contact.phone}`
  } catch (error) {
    console.error('Failed to initiate call:', error)
  }
}

const handleText = async () => {
  if (!props.contact?.phone) return
  
  triggerHaptic('medium')
  emit('text', props.contact)
  
  try {
    // Attempt to initiate SMS using sms: protocol
    window.location.href = `sms:${props.contact.phone}`
  } catch (error) {
    console.error('Failed to initiate SMS:', error)
  }
}

const handleEmail = async () => {
  if (!props.contact?.email) return
  
  triggerHaptic('medium')
  emit('email', props.contact)
  
  try {
    // Attempt to open email client using mailto: protocol
    const subject = encodeURIComponent(`Follow up with ${props.contact.name}`)
    window.location.href = `mailto:${props.contact.email}?subject=${subject}`
  } catch (error) {
    console.error('Failed to open email client:', error)
  }
}

const handleVoiceNote = () => {
  triggerHaptic('light')
  emit('voiceNote')
}

const handleCamera = () => {
  triggerHaptic('light')
  emit('camera')
}

const handleLocation = () => {
  triggerHaptic('light')
  emit('location')
}

// Touch event handling for better responsiveness
const handleTouchStart = (action: any, event: TouchEvent) => {
  event.preventDefault()
  
  // Add visual feedback
  const button = event.currentTarget as HTMLElement
  button.style.transform = 'scale(0.95)'
  button.style.transition = 'transform 0.1s ease-out'
}

const handleTouchEnd = (action: any, event: TouchEvent) => {
  event.preventDefault()
  
  // Reset visual feedback
  const button = event.currentTarget as HTMLElement
  button.style.transform = 'scale(1)'
  
  // Execute action after a short delay for visual feedback
  setTimeout(() => {
    if (!action.disabled) {
      action.action()
    }
  }, 50)
}
</script>

<template>
  <div :class="layoutClasses">
    <div
      v-for="action in actions"
      :key="action.id"
      :class="[
        'flex flex-col items-center touch-manipulation',
        props.layout === 'horizontal' ? 'flex-shrink-0' : ''
      ]"
    >
      <!-- Action Button -->
      <button
        :disabled="action.disabled"
        :data-testid="action.testId"
        :class="[
          sizeConfig.button,
          'rounded-full flex items-center justify-center transition-all duration-200',
          `bg-${action.color}-100 dark:bg-${action.color}-900/20`,
          `hover:bg-${action.color}-200 dark:hover:bg-${action.color}-900/30`,
          action.disabled 
            ? 'opacity-50 cursor-not-allowed' 
            : 'active:scale-95 hover:scale-105'
        ]"
        @click="!action.disabled && action.action()"
        @touchstart="(e) => handleTouchStart(action, e)"
        @touchend="(e) => handleTouchEnd(action, e)"
      >
        <Icon 
          :name="action.icon"
          :class="[
            sizeConfig.icon,
            `text-${action.color}-600 dark:text-${action.color}-400`,
            action.disabled ? 'opacity-50' : ''
          ]"
        />
      </button>

      <!-- Action Label -->
      <BaseText
        v-if="showLabels"
        :class="[
          sizeConfig.text,
          'mt-1 font-medium text-center',
          action.disabled ? 'text-muted-400' : 'text-muted-700 dark:text-muted-300'
        ]"
      >
        {{ action.label }}
      </BaseText>
    </div>
  </div>
</template>

<style scoped>
/* Touch optimizations */
.touch-manipulation {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  touch-action: manipulation;
}

/* Ensure minimum touch target size */
button {
  min-height: 44px;
  min-width: 44px;
}

/* Smooth scrolling for horizontal layout */
.overflow-x-auto {
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.overflow-x-auto::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* Enhanced focus states for accessibility */
button:focus-visible {
  outline: 2px solid;
  outline-offset: 2px;
}

button:focus-visible.bg-success-100 {
  outline-color: rgb(34 197 94);
}

button:focus-visible.bg-info-100 {
  outline-color: rgb(59 130 246);
}

button:focus-visible.bg-warning-100 {
  outline-color: rgb(245 158 11);
}

button:focus-visible.bg-primary-100 {
  outline-color: rgb(99 102 241);
}

button:focus-visible.bg-violet-100 {
  outline-color: rgb(139 92 246);
}

button:focus-visible.bg-rose-100 {
  outline-color: rgb(244 63 94);
}

/* Ripple effect for touch feedback */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

button:active::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  background: currentColor;
  opacity: 0.2;
  animation: ripple 0.6s ease-out;
  pointer-events: none;
}

/* Grid layout responsive adjustments */
@media (max-width: 320px) {
  .grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* Horizontal layout scroll indicators */
.overflow-x-auto {
  position: relative;
}

.overflow-x-auto::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 20px;
  background: linear-gradient(to right, transparent, white);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.overflow-x-auto:hover::after {
  opacity: 1;
}

/* Dark mode gradient fix */
.dark .overflow-x-auto::after {
  background: linear-gradient(to right, transparent, rgb(23 23 23));
}
</style>