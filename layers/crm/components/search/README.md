# CRM Global Search & Filtering System

This directory contains the complete implementation of **Story P1-1: Global Smart Search & Filtering** for the PartnersInBiz CRM module.

## Overview

The Global Search system provides powerful, AI-enhanced search capabilities across all CRM entities with smart filtering, result highlighting, search history, and saved searches functionality.

## Components

### 1. GlobalSearchComponent.vue
The main search input component with autocomplete and dropdown results.

**Props:**
- `placeholder` (string): Search input placeholder text
- `showFilters` (boolean): Whether to show filter toggle button
- `entityTypes` (SearchEntityType[]): Entity types to search
- `workspaceId` (string): Current workspace ID
- `profileId` (string): Current user profile ID
- `size` ('sm' | 'md' | 'lg'): Component size
- `autoFocus` (boolean): Auto-focus the input on mount

**Events:**
- `select`: Emitted when a search result is selected
- `clear`: Emitted when search is cleared

**Example Usage:**
```vue
<template>
  <GlobalSearchComponent
    placeholder="Search accounts, contacts, companies..."
    :entity-types="['account', 'contact', 'company']"
    :workspace-id="currentWorkspace"
    :profile-id="currentUser"
    size="md"
    auto-focus
    @select="handleResultSelect"
    @clear="handleSearchClear"
  />
</template>

<script setup>
import { GlobalSearchComponent } from '~/layers/crm/components/search'

const handleResultSelect = (result) => {
  // Navigate to the selected entity
  navigateTo(`/crm/${result.type}/${result.id}`)
}

const handleSearchClear = () => {
  // Handle search clear if needed
  console.log('Search cleared')
}
</script>
```

### 2. SearchResultsList.vue
Displays search results with highlighting and metadata.

**Props:**
- `results` (SearchResult[]): Array of search results
- `searchQuery` (string): Current search query for highlighting
- `activeIndex` (number): Currently active result index
- `loading` (boolean): Loading state
- `totalResults` (number): Total number of results
- `hasMore` (boolean): Whether more results are available
- `compact` (boolean): Use compact display mode
- `highlightMatches` (boolean): Enable search term highlighting
- `showMetadata` (boolean): Show entity metadata
- `groupByType` (boolean): Group results by entity type

**Events:**
- `select`: Emitted when a result is selected
- `loadMore`: Emitted when load more is requested
- `viewAll`: Emitted when view all for entity type is requested

**Example Usage:**
```vue
<template>
  <SearchResultsList
    :results="searchResults"
    :search-query="query"
    :active-index="activeIndex"
    :loading="isSearching"
    :total-results="totalResults"
    :has-more="hasMore"
    highlight-matches
    show-metadata
    group-by-type
    @select="handleSelect"
    @load-more="loadMoreResults"
    @view-all="viewAllByType"
  />
</template>
```

### 3. SmartFilters.vue
Advanced filtering interface with presets and custom filters.

**Props:**
- `modelValue` (boolean): Whether the filter modal is open
- `compact` (boolean): Use compact filter button
- `showPresets` (boolean): Show filter presets
- `showDateRange` (boolean): Show date range filters
- `showCustomFilters` (boolean): Show custom filter options

**Events:**
- `update:modelValue`: Updates the modal open state
- `apply`: Emitted when filters are applied
- `reset`: Emitted when filters are reset
- `preset-applied`: Emitted when a preset is applied

**Example Usage:**
```vue
<template>
  <SmartFilters
    v-model="showFilters"
    show-presets
    show-date-range
    show-custom-filters
    @apply="applyFilters"
    @reset="resetFilters"
    @preset-applied="handlePresetApplied"
  />
</template>

<script setup>
import { ref } from 'vue'
import { SmartFilters } from '~/layers/crm/components/search'

const showFilters = ref(false)

const applyFilters = () => {
  // Filters are automatically applied through the composable
  console.log('Filters applied')
}

const resetFilters = () => {
  console.log('Filters reset')
}

const handlePresetApplied = (presetId) => {
  console.log('Preset applied:', presetId)
}
</script>
```

### 4. SearchPreview.vue
Individual search result preview card with detailed information.

**Props:**
- `result` (SearchResult): The search result to display
- `searchQuery` (string): Current search query for highlighting
- `highlightMatches` (boolean): Enable search term highlighting
- `showMetadata` (boolean): Show entity metadata
- `compact` (boolean): Use compact display mode
- `clickable` (boolean): Enable click interactions

**Events:**
- `click`: Emitted when the preview is clicked
- `view`: Emitted when view action is triggered
- `edit`: Emitted when edit action is triggered

**Example Usage:**
```vue
<template>
  <SearchPreview
    :result="result"
    :search-query="currentQuery"
    highlight-matches
    show-metadata
    clickable
    @click="viewEntity"
    @view="viewEntity"
    @edit="editEntity"
  />
</template>

<script setup>
import { SearchPreview } from '~/layers/crm/components/search'

const viewEntity = (result) => {
  navigateTo(`/crm/${result.type}/${result.id}`)
}

const editEntity = (result) => {
  navigateTo(`/crm/${result.type}/${result.id}/edit`)
}
</script>
```

## Composables

### useGlobalSearch()
Main composable for global search functionality.

**Key Methods:**
- `initializeUserContext(userId, workspaceId)`: Initialize user context
- `performSearch(options)`: Execute a search
- `clearSearch()`: Clear current search
- `setFilters(filters)`: Update search filters
- `saveCurrentSearch(name, description, tags)`: Save current search
- `executeSavedSearch(searchId)`: Execute a saved search

**Key Reactive Properties:**
- `isSearching`: Whether a search is in progress
- `searchQuery`: Current search query
- `searchResults`: Array of current search results
- `totalResults`: Total number of results
- `hasMore`: Whether more results are available
- `error`: Current error state
- `searchHistory`: User's search history
- `savedSearches`: User's saved searches

**Example Usage:**
```vue
<script setup>
import { useGlobalSearch } from '~/layers/crm/composables/useGlobalSearch'

const {
  isSearching,
  searchQuery,
  searchResults,
  totalResults,
  hasMore,
  error,
  initializeUserContext,
  performSearch,
  clearSearch,
  saveCurrentSearch
} = useGlobalSearch()

// Initialize with user context
onMounted(() => {
  initializeUserContext('user-123', 'workspace-456')
})

// Perform a search
const searchProducts = async () => {
  await performSearch({
    query: 'premium software',
    filters: {
      entityTypes: ['product'],
      status: 'active'
    },
    limit: 20
  })
}

// Save current search
const saveSearch = async () => {
  const savedSearch = await saveCurrentSearch(
    'Premium Products',
    'Active premium software products',
    ['products', 'premium']
  )
  console.log('Search saved:', savedSearch)
}
</script>
```

### useAdvancedFilters()
Composable for managing advanced search filters.

**Key Methods:**
- `applyDatePreset(preset)`: Apply a date range preset
- `toggleEntityType(type)`: Toggle entity type filter
- `toggleStatus(status)`: Toggle status filter
- `addTag(tag)`: Add a tag filter
- `saveAsPreset(name, description)`: Save current filters as preset
- `resetFilters()`: Reset all filters to defaults

**Example Usage:**
```vue
<script setup>
import { useAdvancedFilters } from '~/layers/crm/composables/useAdvancedFilters'

const {
  filters,
  hasActiveFilters,
  toggleEntityType,
  applyDatePreset,
  saveAsPreset,
  getActiveFilterCount
} = useAdvancedFilters()

// Toggle entity types
const showOnlyContacts = () => {
  toggleEntityType('contact')
}

// Apply date range
const showLastWeek = () => {
  applyDatePreset('last7days')
}

// Save as preset
const saveCurrentFilters = () => {
  saveAsPreset('My Custom Filter', 'Description of the filter')
}
</script>
```

### useSearchHistory()
Composable for managing search history and saved searches.

**Key Methods:**
- `addToHistory(userId, query, filters, resultCount, searchTime)`: Add search to history
- `getHistory(options)`: Get user's search history
- `clearHistory(userId)`: Clear search history
- `createSavedSearch(userId, name, query, filters)`: Create a saved search
- `getSavedSearches(options)`: Get user's saved searches
- `toggleSavedSearchFavorite(searchId, userId)`: Toggle favorite status

**Example Usage:**
```vue
<script setup>
import { useSearchHistory } from '~/layers/crm/composables/useSearchHistory'

const {
  searchHistory,
  savedSearches,
  recentSearches,
  hasHistory,
  createSavedSearch,
  getSavedSearches,
  clearHistory
} = useSearchHistory()

// Get search history
const loadHistory = async () => {
  await getHistory({
    userId: 'user-123',
    workspaceId: 'workspace-456',
    limit: 20
  })
}

// Create saved search
const saveSearch = async () => {
  const saved = await createSavedSearch(
    'user-123',
    'High Value Customers',
    'enterprise premium',
    { entityTypes: ['account'], status: 'active' },
    'workspace-456',
    'Customers with enterprise plans'
  )
  console.log('Saved search created:', saved)
}
</script>
```

### useSearchHighlight()
Composable for safely highlighting search terms in text.

**Key Methods:**
- `highlightText(text, searchQuery, options)`: Highlight search terms in text
- `parseSearchQuery(query)`: Parse search query into terms
- `getHighlightClasses(options)`: Get CSS classes for highlighting

**Example Usage:**
```vue
<script setup>
import { useSearchHighlight } from '~/layers/crm/composables/useSearchHighlight'

const { highlightText, getHighlightClasses } = useSearchHighlight()

const text = "John Doe works at Acme Corporation"
const query = "john acme"

const highlighted = highlightText(text, query, {
  caseSensitive: false,
  wholeWords: false,
  maxHighlights: 10
})

// highlighted will be an array of segments with isHighlighted flag
</script>

<template>
  <p>
    <span
      v-for="(segment, index) in highlighted"
      :key="index"
      :class="segment.isHighlighted ? getHighlightClasses() : ''"
    >
      {{ segment.text }}
    </span>
  </p>
</template>
```

## Backend API

### Global Search Endpoint
**POST** `/api/search/global`

**Request Body:**
```typescript
{
  query: string,
  filters?: {
    entityTypes?: SearchEntityType[],
    workspaceId?: string,
    profileId?: string,
    status?: string,
    tags?: string[]
  },
  limit?: number,
  includeDeleted?: boolean
}
```

**Response:**
```typescript
{
  results: SearchResult[],
  total: number,
  hasMore: boolean,
  nextCursor?: string,
  suggestions?: string[],
  searchTime?: number
}
```

**Features:**
- ✅ Input validation and sanitization
- ✅ Rate limiting (30 requests per minute)
- ✅ Search result caching (5-minute TTL)
- ✅ Relevance scoring algorithm
- ✅ Performance optimization with timeouts
- ✅ Security headers and XSS prevention
- ✅ Search analytics tracking
- ✅ Automatic search history creation

## Complete Integration Example

Here's a complete example showing how to integrate all components:

```vue
<template>
  <div class="crm-search-container">
    <!-- Main Search Interface -->
    <div class="flex items-center space-x-4 mb-6">
      <div class="flex-1">
        <GlobalSearchComponent
          :workspace-id="workspace.id"
          :profile-id="user.id"
          :entity-types="['account', 'contact', 'company', 'product', 'order']"
          auto-focus
          @select="handleResultSelect"
        />
      </div>
      
      <SmartFilters
        v-model="showFilters"
        show-presets
        show-date-range
        show-custom-filters
        @apply="handleFiltersApply"
      />
    </div>

    <!-- Search Results -->
    <div v-if="hasResults" class="space-y-4">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold">
          Search Results ({{ totalResults }})
        </h3>
        
        <div class="flex items-center space-x-2">
          <BaseButton
            v-if="!isCurrentSearchSaved"
            size="sm"
            variant="outline"
            @click="saveCurrentSearch"
          >
            <Icon name="lucide:bookmark" class="h-4 w-4" />
            Save Search
          </BaseButton>
        </div>
      </div>
      
      <SearchResultsList
        :results="searchResults"
        :search-query="searchQuery"
        :loading="isSearching"
        :total-results="totalResults"
        :has-more="hasMore"
        highlight-matches
        show-metadata
        @select="handleResultSelect"
        @load-more="loadMoreResults"
      />
    </div>

    <!-- Search History & Saved Searches -->
    <div v-else-if="!hasQuery" class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Recent Searches -->
      <div v-if="hasRecentSearches">
        <h4 class="font-medium mb-3">Recent Searches</h4>
        <div class="space-y-2">
          <button
            v-for="search in recentSearches"
            :key="search.id"
            @click="applyHistorySearch(search)"
            class="w-full text-left p-3 rounded-lg border hover:bg-muted-50"
          >
            <div class="font-medium">{{ search.query }}</div>
            <div class="text-sm text-muted-500">
              {{ search.result_count }} results • {{ formatDate(search.timestamp) }}
            </div>
          </button>
        </div>
      </div>

      <!-- Saved Searches -->
      <div v-if="hasSavedSearches">
        <h4 class="font-medium mb-3">Saved Searches</h4>
        <div class="space-y-2">
          <button
            v-for="search in savedSearches"
            :key="search.id"
            @click="executeSavedSearch(search.id)"
            class="w-full text-left p-3 rounded-lg border hover:bg-muted-50"
          >
            <div class="flex items-center justify-between">
              <div class="font-medium">{{ search.name }}</div>
              <Icon
                v-if="search.is_favorite"
                name="lucide:star"
                class="h-4 w-4 text-warning-500"
              />
            </div>
            <div class="text-sm text-muted-500">{{ search.description }}</div>
          </button>
        </div>
      </div>
    </div>

    <!-- No Results -->
    <div v-else-if="hasQuery && !hasResults && !isSearching" class="text-center py-12">
      <Icon name="lucide:search-x" class="h-12 w-12 text-muted-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium mb-2">No results found</h3>
      <p class="text-muted-500">
        Try adjusting your search terms or filters
      </p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { 
  GlobalSearchComponent,
  SearchResultsList,
  SmartFilters
} from '~/layers/crm/components/search'
import { useGlobalSearch } from '~/layers/crm/composables/useGlobalSearch'

// User and workspace context
const user = useCurrentUser()
const workspace = useCurrentWorkspace()

// Global search composable
const {
  isSearching,
  searchQuery,
  searchResults,
  totalResults,
  hasMore,
  hasResults,
  hasQuery,
  recentSearches,
  savedSearches,
  hasRecentSearches,
  hasSavedSearches,
  initializeUserContext,
  saveCurrentSearch,
  executeSavedSearch,
  applyHistorySearch,
  isSearchSaved
} = useGlobalSearch()

// Component state
const showFilters = ref(false)

// Computed
const isCurrentSearchSaved = computed(() => 
  isSearchSaved(searchQuery.value, {})
)

// Initialize search context
onMounted(() => {
  initializeUserContext(user.value.id, workspace.value.id)
})

// Event handlers
const handleResultSelect = (result) => {
  navigateTo(`/crm/${result.type}/${result.id}`)
}

const handleFiltersApply = () => {
  showFilters.value = false
}

const loadMoreResults = () => {
  // Load more functionality is handled by the composable
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit'
  }).format(new Date(date))
}
</script>
```

## Performance Considerations

1. **Debouncing**: Search queries are debounced with a 300ms delay to prevent excessive API calls
2. **Caching**: Search results are cached for 5 minutes to improve performance
3. **Rate Limiting**: API endpoint is rate-limited to 30 requests per minute per IP
4. **Pagination**: Results are paginated with configurable limits
5. **Lazy Loading**: Search history and saved searches are loaded on demand
6. **Memory Management**: Search requests are cancelable to prevent memory leaks

## Security Features

1. **Input Sanitization**: All user input is sanitized to prevent XSS attacks
2. **Query Validation**: Search queries are validated using Zod schemas
3. **Rate Limiting**: Prevents abuse with per-IP rate limiting
4. **Access Control**: Results are filtered by workspace and profile permissions
5. **Safe Highlighting**: Text highlighting uses structured data instead of HTML injection

## Accessibility

1. **Keyboard Navigation**: Full keyboard support with arrow keys, Enter, and Escape
2. **Screen Reader Support**: Proper ARIA labels and announcements
3. **Focus Management**: Logical focus flow and visible focus indicators
4. **High Contrast**: Support for high contrast mode and dark themes
5. **Responsive Design**: Works across all device sizes and orientations

## Testing

The system includes comprehensive test coverage:

- **Unit Tests**: Individual component and composable testing
- **Integration Tests**: Full workflow testing with all components
- **API Tests**: Backend endpoint testing with various scenarios
- **Performance Tests**: Load testing and timing validation
- **Accessibility Tests**: WCAG 2.1 AA compliance testing

Run tests with:
```bash
# Run all CRM search tests
pnpm test:crm

# Run specific test file
pnpm test tests/crm/global-search-integration.test.ts

# Run with coverage
pnpm test:coverage
```

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## License

This implementation is part of the PartnersInBiz platform and follows the project's license terms.