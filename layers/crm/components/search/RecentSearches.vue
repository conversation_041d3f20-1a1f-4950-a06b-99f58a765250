<script setup lang="ts">
import { computed } from 'vue'
import { useSearchHistory } from '../../composables/useSearchHistory'
import type { SearchHistoryEntry, SavedSearch } from '../../types/crm'

// Component props
interface Props {
  maxRecent?: number
  maxSaved?: number
  showSaved?: boolean
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  maxRecent: 5,
  maxSaved: 3,
  showSaved: true,
  compact: false
})

// Component emits
const emit = defineEmits<{
  select: [entry: SearchHistoryEntry | SavedSearch]
  remove: [id: string, type: 'recent' | 'saved']
  clear: [type: 'recent' | 'saved']
}>()

// Composables
const {
  recentSearches,
  savedSearches,
  pinnedSavedSearches,
  hasRecentSearches,
  hasSavedSearches,
  removeFromRecentSearches,
  removeSavedSearch,
  clearRecentSearches,
  formatEntityTypes,
  formatFiltersDisplay
} = useSearchHistory()

// Computed
const displayRecentSearches = computed(() => 
  recentSearches.value.slice(0, props.maxRecent)
)

const displaySavedSearches = computed(() => {
  const pinned = pinnedSavedSearches.value
  const regular = savedSearches.value.filter(s => !s.pinned).slice(0, Math.max(0, props.maxSaved - pinned.length))
  return [...pinned, ...regular].slice(0, props.maxSaved)
})

// Handle search selection
const selectSearch = (entry: SearchHistoryEntry | SavedSearch) => {
  emit('select', entry)
}

// Handle remove search
const removeSearch = (id: string, type: 'recent' | 'saved') => {
  if (type === 'recent') {
    removeFromRecentSearches(id)
  } else {
    removeSavedSearch(id)
  }
  emit('remove', id, type)
}

// Handle clear searches
const clearSearches = (type: 'recent' | 'saved') => {
  if (type === 'recent') {
    clearRecentSearches()
  }
  emit('clear', type)
}

// Format time ago
const formatTimeAgo = (date: Date): string => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return 'Just now'
  if (minutes < 60) return `${minutes}m ago`
  if (hours < 24) return `${hours}h ago`
  if (days < 7) return `${days}d ago`
  
  return date.toLocaleDateString()
}

// Check if entry is saved search
const isSavedSearch = (entry: SearchHistoryEntry | SavedSearch): entry is SavedSearch => {
  return 'name' in entry
}
</script>

<template>
  <div class="recent-searches">
    <!-- Saved Searches -->
    <div v-if="showSaved && hasSavedSearches" class="space-y-2">
      <div class="flex items-center justify-between px-4 py-2">
        <div class="flex items-center space-x-2">
          <Icon name="lucide:bookmark" class="h-4 w-4 text-muted-500" />
          <BaseText size="sm" weight="medium" class="text-muted-700 dark:text-muted-300">
            Saved Searches
          </BaseText>
        </div>
      </div>
      
      <div class="space-y-1">
        <button
          v-for="search in displaySavedSearches"
          :key="search.id"
          @click="selectSearch(search)"
          :class="[
            'w-full px-4 py-2 flex items-center space-x-3 hover:bg-muted-50 dark:hover:bg-muted-700/50 transition-colors text-left group',
            compact ? 'py-1.5' : 'py-2'
          ]"
        >
          <!-- Search Icon -->
          <div class="shrink-0">
            <div
              :class="[
                'rounded-full flex items-center justify-center',
                compact ? 'h-6 w-6' : 'h-8 w-8',
                search.color === 'primary' ? 'bg-primary-100 dark:bg-primary-900/20' :
                search.color === 'success' ? 'bg-success-100 dark:bg-success-900/20' :
                search.color === 'warning' ? 'bg-warning-100 dark:bg-warning-900/20' :
                search.color === 'danger' ? 'bg-danger-100 dark:bg-danger-900/20' :
                search.color === 'info' ? 'bg-info-100 dark:bg-info-900/20' :
                'bg-muted-100 dark:bg-muted-900/20'
              ]"
            >
              <Icon
                name="lucide:bookmark"
                :class="[
                  compact ? 'h-3 w-3' : 'h-4 w-4',
                  search.color === 'primary' ? 'text-primary-600 dark:text-primary-400' :
                  search.color === 'success' ? 'text-success-600 dark:text-success-400' :
                  search.color === 'warning' ? 'text-warning-600 dark:text-warning-400' :
                  search.color === 'danger' ? 'text-danger-600 dark:text-danger-400' :
                  search.color === 'info' ? 'text-info-600 dark:text-info-400' :
                  'text-muted-600 dark:text-muted-400'
                ]"
              />
            </div>
          </div>

          <!-- Search Content -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center space-x-2">
              <BaseText
                :size="compact ? 'xs' : 'sm'"
                weight="medium"
                class="truncate"
              >
                {{ search.name }}
              </BaseText>
              
              <Icon
                v-if="search.pinned"
                name="lucide:pin"
                class="h-3 w-3 text-muted-400 shrink-0"
              />
            </div>
            
            <BaseText
              size="xs"
              class="text-muted-500 truncate"
            >
              "{{ search.query }}"
            </BaseText>
            
            <BaseText
              v-if="!compact"
              size="xs"
              class="text-muted-400 truncate"
            >
              {{ formatFiltersDisplay(search.filters) }}
            </BaseText>
          </div>

          <!-- Actions -->
          <div class="shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              @click.stop="removeSearch(search.id, 'saved')"
              class="p-1 rounded hover:bg-danger-100 dark:hover:bg-danger-900/20 transition-colors"
            >
              <Icon name="lucide:x" class="h-3 w-3 text-danger-500" />
            </button>
          </div>
        </button>
      </div>
    </div>

    <!-- Divider -->
    <div
      v-if="showSaved && hasSavedSearches && hasRecentSearches"
      class="border-t border-muted-200 dark:border-muted-700 my-2"
    />

    <!-- Recent Searches -->
    <div v-if="hasRecentSearches" class="space-y-2">
      <div class="flex items-center justify-between px-4 py-2">
        <div class="flex items-center space-x-2">
          <Icon name="lucide:clock" class="h-4 w-4 text-muted-500" />
          <BaseText size="sm" weight="medium" class="text-muted-700 dark:text-muted-300">
            Recent Searches
          </BaseText>
        </div>
        
        <BaseButton
          size="xs"
          variant="ghost"
          @click="clearSearches('recent')"
          class="opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <Icon name="lucide:x" class="h-3 w-3" />
          <span>Clear</span>
        </BaseButton>
      </div>
      
      <div class="space-y-1">
        <button
          v-for="search in displayRecentSearches"
          :key="search.id"
          @click="selectSearch(search)"
          :class="[
            'w-full px-4 py-2 flex items-center space-x-3 hover:bg-muted-50 dark:hover:bg-muted-700/50 transition-colors text-left group',
            compact ? 'py-1.5' : 'py-2'
          ]"
        >
          <!-- Search Icon -->
          <div class="shrink-0">
            <div
              :class="[
                'rounded-full flex items-center justify-center bg-muted-100 dark:bg-muted-900/20',
                compact ? 'h-6 w-6' : 'h-8 w-8'
              ]"
            >
              <Icon
                name="lucide:search"
                :class="[
                  compact ? 'h-3 w-3' : 'h-4 w-4',
                  'text-muted-600 dark:text-muted-400'
                ]"
              />
            </div>
          </div>

          <!-- Search Content -->
          <div class="flex-1 min-w-0">
            <BaseText
              :size="compact ? 'xs' : 'sm'"
              weight="medium"
              class="truncate"
            >
              "{{ search.query }}"
            </BaseText>
            
            <div class="flex items-center space-x-2">
              <BaseText
                size="xs"
                class="text-muted-500"
              >
                {{ formatEntityTypes(search.filters.entityTypes || []) }}
              </BaseText>
              
              <BaseText
                v-if="search.resultCount !== undefined"
                size="xs"
                class="text-muted-400"
              >
                • {{ search.resultCount }} results
              </BaseText>
              
              <BaseText
                size="xs"
                class="text-muted-400"
              >
                • {{ formatTimeAgo(search.timestamp) }}
              </BaseText>
            </div>
            
            <BaseText
              v-if="!compact && search.filters.status"
              size="xs"
              class="text-muted-400 truncate"
            >
              Status: {{ search.filters.status }}
            </BaseText>
          </div>

          <!-- Actions -->
          <div class="shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              @click.stop="removeSearch(search.id, 'recent')"
              class="p-1 rounded hover:bg-danger-100 dark:hover:bg-danger-900/20 transition-colors"
            >
              <Icon name="lucide:x" class="h-3 w-3 text-danger-500" />
            </button>
          </div>
        </button>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!hasRecentSearches && (!showSaved || !hasSavedSearches)" class="px-4 py-6 text-center">
      <Icon name="lucide:search" class="h-8 w-8 text-muted-400 mx-auto mb-2" />
      <BaseText size="sm" class="text-muted-500">
        No recent searches
      </BaseText>
      <BaseText size="xs" class="text-muted-400 mt-1">
        Start typing to search across your CRM data
      </BaseText>
    </div>
  </div>
</template>

<style scoped>
.recent-searches {
  /* Component-specific styles */
}
</style>