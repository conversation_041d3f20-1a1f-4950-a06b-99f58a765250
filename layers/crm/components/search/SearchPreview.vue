<script setup lang="ts">
import { computed, ref } from 'vue'
import { SearchResult, SearchEntityType } from '../../types/crm'
import { useSearchHighlight } from '../../composables/useSearchHighlight'

// Component props
interface Props {
  result: SearchResult
  searchQuery: string
  highlightMatches?: boolean
  showMetadata?: boolean
  compact?: boolean
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  highlightMatches: true,
  showMetadata: true,
  compact: false,
  clickable: true
})

// Component emits
const emit = defineEmits<{
  click: [result: SearchResult]
  view: [result: SearchResult]
  edit: [result: SearchResult]
}>()

// Composables
const { highlightText, parseSearchQuery, getHighlightClasses } = useSearchHighlight()

// Get entity type icon
const getEntityIcon = (type: SearchEntityType): string => {
  const icons = {
    account: 'lucide:user-circle',
    contact: 'lucide:user',
    company: 'lucide:building',
    product: 'lucide:package',
    order: 'lucide:shopping-cart'
  }
  return icons[type] || 'lucide:circle'
}

// Get entity type color
const getEntityColor = (type: SearchEntityType): string => {
  const colors = {
    account: 'primary',
    contact: 'success',
    company: 'info',
    product: 'warning',
    order: 'danger'
  }
  return colors[type] || 'muted'
}

// Safe entity color classes mapping (prevents XSS)
const entityColorClasses = {
  account: {
    background: 'bg-primary-100 dark:bg-primary-900/20',
    text: 'text-primary-600 dark:text-primary-400',
    border: 'border-primary-200 dark:border-primary-800'
  },
  contact: {
    background: 'bg-success-100 dark:bg-success-900/20',
    text: 'text-success-600 dark:text-success-400',
    border: 'border-success-200 dark:border-success-800'
  },
  company: {
    background: 'bg-info-100 dark:bg-info-900/20',
    text: 'text-info-600 dark:text-info-400',
    border: 'border-info-200 dark:border-info-800'
  },
  product: {
    background: 'bg-warning-100 dark:bg-warning-900/20',
    text: 'text-warning-600 dark:text-warning-400',
    border: 'border-warning-200 dark:border-warning-800'
  },
  order: {
    background: 'bg-danger-100 dark:bg-danger-900/20',
    text: 'text-danger-600 dark:text-danger-400',
    border: 'border-danger-200 dark:border-danger-800'
  },
  default: {
    background: 'bg-muted-100 dark:bg-muted-900/20',
    text: 'text-muted-600 dark:text-muted-400',
    border: 'border-muted-200 dark:border-muted-800'
  }
}

// Get entity type label
const getEntityLabel = (type: SearchEntityType): string => {
  const labels = {
    account: 'Account',
    contact: 'Contact',
    company: 'Company',
    product: 'Product',
    order: 'Order'
  }
  return labels[type] || 'Unknown'
}

// Highlight text with search terms
const getHighlightedText = (text: string) => {
  if (!props.highlightMatches || !props.searchQuery.trim()) {
    return [{ text, isHighlighted: false }]
  }
  
  const searchTerms = parseSearchQuery(props.searchQuery)
  return highlightText(text, searchTerms.join(' '), {
    caseSensitive: false,
    wholeWords: false,
    maxHighlights: 10
  })
}

// Get metadata display for result
const metadataItems = computed(() => {
  if (!props.showMetadata || !props.result.metadata) return []
  
  const metadata: Array<{ label: string, value: string, icon?: string }> = []
  
  switch (props.result.type) {
    case 'account':
      if (props.result.metadata.status) {
        metadata.push({ 
          label: 'Status', 
          value: props.result.metadata.status,
          icon: 'lucide:activity'
        })
      }
      if (props.result.metadata.contactCount) {
        metadata.push({ 
          label: 'Contacts', 
          value: `${props.result.metadata.contactCount}`,
          icon: 'lucide:users'
        })
      }
      break
      
    case 'contact':
      if (props.result.metadata.jobTitle) {
        metadata.push({ 
          label: 'Job Title', 
          value: props.result.metadata.jobTitle,
          icon: 'lucide:briefcase'
        })
      }
      if (props.result.metadata.phone) {
        metadata.push({ 
          label: 'Phone', 
          value: props.result.metadata.phone,
          icon: 'lucide:phone'
        })
      }
      break
      
    case 'company':
      if (props.result.metadata.employeeCount) {
        metadata.push({ 
          label: 'Employees', 
          value: `${props.result.metadata.employeeCount}`,
          icon: 'lucide:users'
        })
      }
      if (props.result.metadata.revenue) {
        metadata.push({ 
          label: 'Revenue', 
          value: `$${props.result.metadata.revenue}`,
          icon: 'lucide:dollar-sign'
        })
      }
      break
      
    case 'product':
      if (props.result.metadata.price) {
        metadata.push({ 
          label: 'Price', 
          value: `$${props.result.metadata.price}`,
          icon: 'lucide:tag'
        })
      }
      if (props.result.metadata.stockStatus) {
        metadata.push({ 
          label: 'Stock', 
          value: props.result.metadata.stockStatus,
          icon: 'lucide:package'
        })
      }
      break
      
    case 'order':
      if (props.result.metadata.status) {
        metadata.push({ 
          label: 'Status', 
          value: props.result.metadata.status,
          icon: 'lucide:truck'
        })
      }
      if (props.result.metadata.total) {
        metadata.push({ 
          label: 'Total', 
          value: `${props.result.metadata.currency || '$'}${props.result.metadata.total}`,
          icon: 'lucide:dollar-sign'
        })
      }
      break
  }
  
  return metadata.slice(0, props.compact ? 2 : 4) // Limit metadata items
})

// Handle clicks
const handleClick = () => {
  if (props.clickable) {
    emit('click', props.result)
  }
}

const handleView = (event: Event) => {
  event.stopPropagation()
  emit('view', props.result)
}

const handleEdit = (event: Event) => {
  event.stopPropagation()
  emit('edit', props.result)
}

// Sanitize user input to prevent XSS attacks
const sanitizeText = (text: string): string => {
  if (typeof text !== 'string') return ''
  
  return text
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
    .trim()
}

// Safely display search result data
const getSafeTitle = (): string => {
  return sanitizeText(props.result.title || 'Untitled')
}

const getSafeSubtitle = (): string => {
  return sanitizeText(props.result.subtitle || '')
}

const getSafeDescription = (): string => {
  return sanitizeText(props.result.description || '')
}
</script>

<template>
  <div
    :class="[
      'search-preview-card border rounded-lg transition-all duration-200',
      compact ? 'p-3' : 'p-4',
      clickable ? 'cursor-pointer hover:shadow-md hover:border-muted-300 dark:hover:border-muted-600' : '',
      entityColorClasses[result.type]?.border || entityColorClasses.default.border,
      'bg-white dark:bg-muted-800'
    ]"
    @click="handleClick"
  >
    <!-- Header -->
    <div class="flex items-start space-x-3">
      <!-- Entity Avatar/Icon -->
      <div class="shrink-0">
        <BaseAvatar
          v-if="result.avatar"
          :src="result.avatar"
          :size="compact ? 'sm' : 'md'"
          :class="compact ? 'h-10 w-10' : 'h-12 w-12'"
        />
        <div
          v-else
          :class="[
            'rounded-full flex items-center justify-center',
            compact ? 'h-10 w-10' : 'h-12 w-12',
            entityColorClasses[result.type]?.background || entityColorClasses.default.background
          ]"
        >
          <Icon
            :name="getEntityIcon(result.type)"
            :class="[
              compact ? 'h-5 w-5' : 'h-6 w-6',
              entityColorClasses[result.type]?.text || entityColorClasses.default.text
            ]"
          />
        </div>
      </div>

      <!-- Content -->
      <div class="flex-1 min-w-0">
        <!-- Title and Badge -->
        <div class="flex items-center space-x-2 mb-1">
          <BaseHeading
            as="h3"
            :size="compact ? 'sm' : 'md'"
            weight="semibold"
            class="truncate"
          >
            <!-- Highlighted Title -->
            <template v-if="highlightMatches && searchQuery.trim()">
              <span
                v-for="(segment, segIndex) in getHighlightedText(result.title)"
                :key="segIndex"
                :class="segment.isHighlighted ? getHighlightClasses() : ''"
                v-html="segment.text"
              />
            </template>
            <span v-else>{{ getSafeTitle() }}</span>
          </BaseHeading>
          
          <BaseBadge
            :color="getEntityColor(result.type)"
            variant="pastel"
            size="xs"
          >
            {{ getEntityLabel(result.type) }}
          </BaseBadge>
        </div>
        
        <!-- Subtitle -->
        <BaseText
          v-if="result.subtitle"
          :size="compact ? 'xs' : 'sm'"
          class="text-muted-600 dark:text-muted-400 truncate mb-2"
        >
          <!-- Highlighted Subtitle -->
          <template v-if="highlightMatches && searchQuery.trim()">
            <span
              v-for="(segment, segIndex) in getHighlightedText(result.subtitle)"
              :key="segIndex"
              :class="segment.isHighlighted ? getHighlightClasses({ variant: 'secondary' }) : ''"
              v-html="segment.text"
            />
          </template>
          <span v-else>{{ getSafeSubtitle() }}</span>
        </BaseText>
        
        <!-- Description -->
        <BaseText
          v-if="result.description && !compact"
          size="sm"
          class="text-muted-500 dark:text-muted-400 line-clamp-2 mb-3"
        >
          <!-- Highlighted Description -->
          <template v-if="highlightMatches && searchQuery.trim()">
            <span
              v-for="(segment, segIndex) in getHighlightedText(result.description)"
              :key="segIndex"
              :class="segment.isHighlighted ? getHighlightClasses({ variant: 'secondary', intensity: 'light' }) : ''"
              v-html="segment.text"
            />
          </template>
          <span v-else>{{ getSafeDescription() }}</span>
        </BaseText>

        <!-- Metadata -->
        <div v-if="showMetadata && metadataItems.length > 0" class="space-y-2">
          <div
            :class="[
              'grid gap-2',
              compact ? 'grid-cols-1' : 'grid-cols-2'
            ]"
          >
            <div
              v-for="meta in metadataItems"
              :key="meta.label"
              class="flex items-center space-x-2"
            >
              <Icon
                v-if="meta.icon"
                :name="meta.icon"
                class="h-3 w-3 text-muted-400"
              />
              <BaseText size="xs" class="text-muted-500">
                <span class="font-medium">{{ meta.label }}:</span>
                {{ meta.value }}
              </BaseText>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div v-if="clickable" class="shrink-0 flex items-center space-x-1">
        <BaseButton
          size="xs"
          variant="ghost"
          @click="handleView"
          :title="`View ${getEntityLabel(result.type)}`"
        >
          <Icon name="lucide:eye" class="h-3 w-3" />
        </BaseButton>
        
        <BaseButton
          size="xs"
          variant="ghost"
          @click="handleEdit"
          :title="`Edit ${getEntityLabel(result.type)}`"
        >
          <Icon name="lucide:edit" class="h-3 w-3" />
        </BaseButton>
        
        <Icon name="lucide:chevron-right" class="h-4 w-4 text-muted-400" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.search-preview-card {
  /* Component-specific styles if needed */
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>