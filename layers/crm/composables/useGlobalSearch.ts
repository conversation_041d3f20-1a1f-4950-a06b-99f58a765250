import { ref, computed, watch, readonly, nextTick, onUnmounted } from 'vue'
import { useDataApi } from '~/layers/core/composables/useDataApi'
import { useSearchHistory } from './useSearchHistory'
import { 
  SearchOptions, 
  SearchResponse, 
  SearchResult, 
  SearchEntityType,
  SearchFilter,
  SearchError,
  SearchState,
  Account,
  Contact,
  Company,
  Product,
  Order
} from '../types/crm'

/**
 * Composable for global search functionality across CRM entities
 * Provides debounced search with 300ms delay, result formatting, and search history integration
 */
export function useGlobalSearch() {
  const dataApi = useDataApi()
  const searchHistory = useSearchHistory()
  
  // Reactive state
  const isSearching = ref(false)
  const searchQuery = ref('')
  const searchResults = ref<SearchResult[]>([])
  const totalResults = ref(0)
  const hasMore = ref(false)
  const nextCursor = ref<string | undefined>()
  const error = ref<SearchError | null>(null)
  const hasRetried = ref(false)
  const retryCount = ref(0)
  const maxRetries = 2
  
  // Search filters
  const searchFilters = ref<SearchFilter>({
    entityTypes: ['account', 'contact', 'company', 'product', 'order']
  })
  
  // Current user context (these should be set by the parent component)
  const currentUserId = ref<string>('')
  const currentWorkspaceId = ref<string>('')
  
  /**
   * Initialize user context for search history tracking
   */
  const initializeUserContext = (userId: string, workspaceId?: string) => {
    currentUserId.value = userId
    currentWorkspaceId.value = workspaceId || ''
    
    // Load initial search history for the user
    if (userId) {
      searchHistory.getHistory({ userId, workspaceId }).catch(err => {
        console.warn('Failed to load initial search history:', err)
      })
    }
  }
  
  /**
   * Format entity data to search result
   */
  const formatSearchResult = (entity: any, type: SearchEntityType): SearchResult => {
    switch (type) {
      case 'account':
        return {
          id: entity.id,
          type: 'account',
          title: entity.name,
          subtitle: entity.type,
          description: entity.description,
          avatar: entity.avatar,
          metadata: {
            status: entity.status,
            contactCount: entity.contact_ids?.length || 0
          },
          entity: entity as Account
        }
      
      case 'contact':
        return {
          id: entity.id,
          type: 'contact',
          title: entity.name,
          subtitle: entity.email,
          description: `${entity.job_title || ''} ${entity.company_name ? `at ${entity.company_name}` : ''}`.trim(),
          avatar: entity.avatar,
          metadata: {
            status: entity.status,
            phone: entity.phone,
            jobTitle: entity.job_title
          },
          entity: entity as Contact
        }
      
      case 'company':
        return {
          id: entity.id,
          type: 'company',
          title: entity.name,
          subtitle: entity.industry,
          description: entity.description,
          avatar: entity.logo,
          metadata: {
            status: entity.status,
            employeeCount: entity.employees_count,
            revenue: entity.annual_revenue
          },
          entity: entity as Company
        }
      
      case 'product':
        return {
          id: entity.id,
          type: 'product',
          title: entity.name,
          subtitle: entity.sku,
          description: entity.description,
          avatar: entity.images?.[0],
          metadata: {
            status: entity.status,
            price: entity.price,
            currency: entity.currency || 'USD',
            stockStatus: entity.stock_status
          },
          entity: entity as Product
        }
      
      case 'order':
        return {
          id: entity.id,
          type: 'order',
          title: entity.order_number,
          subtitle: `Total: ${entity.currency || '$'}${entity.total}`,
          description: entity.description,
          metadata: {
            status: entity.status,
            paymentStatus: entity.payment_status,
            deliveryStatus: entity.delivery_status,
            total: entity.total,
            currency: entity.currency
          },
          entity: entity as Order
        }
      
      default:
        return {
          id: entity.id,
          type: type,
          title: entity.name || 'Unknown',
          subtitle: '',
          description: entity.description || '',
          metadata: {},
          entity: entity
        }
    }
  }
  
  // Entity type to collection name mapping (proper mapping instead of simple pluralization)
  const ENTITY_COLLECTION_MAP: Record<SearchEntityType, string> = {
    account: 'accounts',
    contact: 'contacts', 
    company: 'companies', // Proper plural
    product: 'products',
    order: 'orders'
  }

  /**
   * Search across a specific entity type
   */
  const searchEntityType = async (entityType: SearchEntityType, query: string, filters: SearchFilter): Promise<SearchResult[]> => {
    const collectionName = ENTITY_COLLECTION_MAP[entityType]
    
    try {
      // Build search filters for the API
      const searchFields = ['name']
      if (entityType === 'contact') {
        searchFields.push('email', 'job_title', 'company_name')
      } else if (entityType === 'company') {
        searchFields.push('industry')
      } else if (entityType === 'product') {
        searchFields.push('sku', 'categories')
      } else if (entityType === 'order') {
        searchFields.push('order_number')
      }
      
      // For now, we'll use a simple name-based search with the getAll method
      // In a real implementation, you'd want a dedicated search API endpoint
      const entities = await dataApi.getAll(collectionName, {
        workspaceId: filters.workspaceId,
        profileId: filters.profileId,
        limit: 20, // Limit per entity type
        includeDeleted: false
      })
      
      // Filter results that match the search query
      const filteredEntities = entities.filter((entity: any) => {
        if (!query) return true
        
        const searchText = query.toLowerCase()
        
        // Search in name (primary field for all entities)
        if (entity.name?.toLowerCase().includes(searchText)) return true
        
        // Entity-specific search fields
        if (entityType === 'contact') {
          return entity.email?.toLowerCase().includes(searchText) ||
                 entity.job_title?.toLowerCase().includes(searchText) ||
                 entity.company_name?.toLowerCase().includes(searchText)
        } else if (entityType === 'company') {
          return entity.industry?.toLowerCase().includes(searchText)
        } else if (entityType === 'product') {
          return entity.sku?.toLowerCase().includes(searchText) ||
                 entity.description?.toLowerCase().includes(searchText)
        } else if (entityType === 'order') {
          return entity.order_number?.toLowerCase().includes(searchText)
        }
        
        return false
      })
      
      return filteredEntities.map((entity: any) => formatSearchResult(entity, entityType))
    } catch (err) {
      console.error(`Error searching ${entityType}:`, err)
      return []
    }
  }
  
  /**
   * Perform global search across all entity types using the new search API with cancellation support
   */
  const performSearch = async (options: SearchOptions): Promise<SearchResponse> => {
    const { query, filters = {}, limit = 50, includeDeleted = false } = options
    
    isSearching.value = true
    error.value = null
    
    try {
      // Reset error state on new search
      error.value = null
      hasRetried.value = false
      
      // Add signal to fetch request for cancellation support
      const response = await $fetch('/api/search/global', {
        method: 'POST',
        signal: currentSearchController?.signal,
        body: {
          query,
          filters: {
            ...filters,
            includeDeleted
          },
          limit
        }
      })
      
      // Reset retry count on successful search
      retryCount.value = 0
      
      return response as SearchResponse
    } catch (err) {
      // Don't log or handle errors if request was cancelled
      if (currentSearchController?.signal.aborted) {
        return {
          results: [],
          total: 0,
          hasMore: false,
          nextCursor: undefined
        }
      }
      
      console.error('Global search error:', err)
      
      // Parse error details
      let searchError: SearchError = {
        message: 'Search failed. Please try again.',
        code: 'SEARCH_ERROR'
      }
      
      if (err && typeof err === 'object') {
        const errorObj = err as any
        
        // Handle different error types
        if (errorObj.statusCode === 400) {
          searchError = {
            message: 'Invalid search query. Please check your input and try again.',
            code: 'INVALID_QUERY',
            details: errorObj.data
          }
        } else if (errorObj.statusCode === 500) {
          searchError = {
            message: 'Server error occurred. Please try again in a moment.',
            code: 'SERVER_ERROR',
            details: errorObj.data
          }
        } else if (errorObj.statusCode === 429) {
          searchError = {
            message: 'Too many search requests. Please wait a moment and try again.',
            code: 'RATE_LIMITED'
          }
        } else if (errorObj.name === 'AbortError') {
          searchError = {
            message: 'Search request was cancelled.',
            code: 'CANCELLED'
          }
        } else if (!navigator.onLine) {
          searchError = {
            message: 'No internet connection. Please check your connection and try again.',
            code: 'OFFLINE'
          }
        } else if (err instanceof Error) {
          searchError.message = err.message
          searchError.details = err.stack
        }
      }
      
      error.value = searchError
      
      // Return empty response on error
      return {
        results: [],
        total: 0,
        hasMore: false,
        nextCursor: undefined
      }
    } finally {
      isSearching.value = false
    }
  }
  
  /**
   * Retry search with exponential backoff
   */
  const retrySearch = async (query: string) => {
    if (retryCount.value >= maxRetries) {
      return
    }
    
    hasRetried.value = true
    retryCount.value += 1
    
    // Exponential backoff: 1s, 2s, 4s
    const delay = Math.pow(2, retryCount.value - 1) * 1000
    
    setTimeout(async () => {
      try {
        const response = await performSearch({
          query: query.trim(),
          filters: searchFilters.value,
          limit: 50,
          includeDeleted: false
        })
        
        searchResults.value = response.results
        totalResults.value = response.total
        hasMore.value = response.hasMore
        nextCursor.value = response.nextCursor
      } catch (err) {
        console.error('Retry search failed:', err)
      }
    }, delay)
  }

  /**
   * Search request cancellation and cleanup management
   */
  let searchTimeout: NodeJS.Timeout | null = null
  let currentSearchController: AbortController | null = null
  
  /**
   * Cancel any ongoing search request and cleanup
   */
  const cancelSearch = () => {
    // Cancel any pending HTTP request
    if (currentSearchController) {
      currentSearchController.abort()
      currentSearchController = null
    }
    
    // Clear debounce timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout)
      searchTimeout = null
    }
    
    isSearching.value = false
  }

  /**
   * Debounced search function with proper cancellation (300ms delay)
   */
  const debouncedSearch = (query: string) => {
    // Cancel any ongoing search
    cancelSearch()
    
    searchTimeout = setTimeout(async () => {
      if (!query.trim()) {
        searchResults.value = []
        totalResults.value = 0
        hasMore.value = false
        nextCursor.value = undefined
        error.value = null
        return
      }
      
      try {
        // Create new abort controller for this search
        currentSearchController = new AbortController()
        
        const response = await performSearch({
          query: query.trim(),
          filters: searchFilters.value,
          limit: 50,
          includeDeleted: false
        })
        
        // Only update state if search wasn't cancelled
        if (currentSearchController && !currentSearchController.signal.aborted) {
          searchResults.value = response.results
          totalResults.value = response.total
          hasMore.value = response.hasMore
          nextCursor.value = response.nextCursor
          currentSearchController = null
        }
      } catch (err) {
        // Only handle error if search wasn't intentionally cancelled
        if (currentSearchController && !currentSearchController.signal.aborted) {
          console.error('Search error:', err)
          searchResults.value = []
          totalResults.value = 0
          hasMore.value = false
          nextCursor.value = undefined
          currentSearchController = null
        }
      }
    }, 300)
  }
  
  /**
   * Watch for changes in search query and trigger debounced search
   */
  watch(searchQuery, (newQuery) => {
    debouncedSearch(newQuery)
  })
  
  /**
   * Clear search results and cleanup timeouts and requests
   */
  const clearSearch = () => {
    // Cancel any ongoing search operations
    cancelSearch()
    
    // Clear search state
    searchQuery.value = ''
    searchResults.value = []
    totalResults.value = 0
    hasMore.value = false
    nextCursor.value = undefined
    error.value = null
  }
  
  /**
   * Set search filters
   */
  const setFilters = (filters: Partial<SearchFilter>) => {
    searchFilters.value = { ...searchFilters.value, ...filters }
    
    // Re-run search if there's a current query
    if (searchQuery.value.trim()) {
      debouncedSearch(searchQuery.value)
    }
  }
  
  /**
   * Execute a saved search
   */
  const executeSavedSearch = async (savedSearchId: string) => {
    if (!currentUserId.value) {
      console.warn('Cannot execute saved search: User ID not set')
      return
    }
    
    try {
      // Update execution tracking
      const updatedSearch = await searchHistory.executeSavedSearch(
        savedSearchId, 
        currentUserId.value, 
        currentWorkspaceId.value
      )
      
      if (updatedSearch) {
        // Set the query and filters from the saved search
        searchQuery.value = updatedSearch.query
        searchFilters.value = updatedSearch.filters
        
        // Trigger search with the saved search parameters
        debouncedSearch(updatedSearch.query)
      }
    } catch (err) {
      console.error('Failed to execute saved search:', err)
      error.value = {
        message: 'Failed to execute saved search',
        code: 'SAVED_SEARCH_EXECUTION_ERROR'
      }
    }
  }
  
  /**
   * Save current search query and filters
   */
  const saveCurrentSearch = async (name: string, description?: string, tags?: string[]) => {
    if (!currentUserId.value || !searchQuery.value.trim()) {
      console.warn('Cannot save search: User ID not set or no query')
      return null
    }
    
    try {
      return await searchHistory.createSavedSearch(
        currentUserId.value,
        name,
        searchQuery.value,
        searchFilters.value,
        currentWorkspaceId.value,
        description,
        tags
      )
    } catch (err) {
      console.error('Failed to save search:', err)
      error.value = {
        message: 'Failed to save search',
        code: 'SAVE_SEARCH_ERROR'
      }
      return null
    }
  }
  
  /**
   * Apply a search from history
   */
  const applyHistorySearch = (historyEntry: any) => {
    searchQuery.value = historyEntry.query
    searchFilters.value = historyEntry.filters
    
    // Trigger search
    debouncedSearch(historyEntry.query)
  }
  
  /**
   * Get search suggestions based on history
   */
  const getSearchSuggestions = async (limit = 5) => {
    if (!currentUserId.value) return []
    
    try {
      return await searchHistory.getFrequentSearches(
        currentUserId.value, 
        currentWorkspaceId.value, 
        limit
      )
    } catch (err) {
      console.error('Failed to get search suggestions:', err)
      return []
    }
  }
  
  /**
   * Cleanup all resources on component unmount to prevent memory leaks
   */
  onUnmounted(() => {
    // Cancel any ongoing operations
    cancelSearch()
  })

  /**
   * Computed properties
   */
  const hasResults = computed(() => searchResults.value.length > 0)
  const hasQuery = computed(() => searchQuery.value.trim().length > 0)
  const shouldShowResults = computed(() => hasQuery.value && !isSearching.value)
  
  return {
    // State
    isSearching: readonly(isSearching),
    searchQuery,
    searchResults: readonly(searchResults),
    totalResults: readonly(totalResults),
    hasMore: readonly(hasMore),
    nextCursor: readonly(nextCursor),
    error: readonly(error),
    hasRetried: readonly(hasRetried),
    searchFilters: readonly(searchFilters),
    
    // Search History State (exposed from useSearchHistory)
    searchHistory: searchHistory.searchHistory,
    savedSearches: searchHistory.savedSearches,
    recentSearches: searchHistory.recentSearches,
    hasHistory: searchHistory.hasHistory,
    hasSavedSearches: searchHistory.hasSavedSearches,
    favoriteSavedSearches: searchHistory.favoriteSavedSearches,
    
    // Computed
    hasResults,
    hasQuery,
    shouldShowResults,
    
    // Search Methods
    performSearch,
    clearSearch,
    setFilters,
    retrySearch,
    formatSearchResult,
    cancelSearch,
    
    // Search History & Saved Search Methods
    initializeUserContext,
    executeSavedSearch,
    saveCurrentSearch,
    applyHistorySearch,
    getSearchSuggestions,
    
    // Search History Methods (from useSearchHistory)
    getHistory: searchHistory.getHistory,
    clearHistory: searchHistory.clearHistory,
    removeFromHistory: searchHistory.removeFromHistory,
    
    // Saved Search Methods (from useSearchHistory)
    getSavedSearches: searchHistory.getSavedSearches,
    createSavedSearch: searchHistory.createSavedSearch,
    updateSavedSearch: searchHistory.updateSavedSearch,
    deleteSavedSearch: searchHistory.deleteSavedSearch,
    toggleSavedSearchFavorite: searchHistory.toggleSavedSearchFavorite,
    isSearchSaved: searchHistory.isSearchSaved,
    
    // Utility Methods
    formatEntityTypes: searchHistory.formatEntityTypes,
    formatFiltersDisplay: searchHistory.formatFiltersDisplay
  }
}