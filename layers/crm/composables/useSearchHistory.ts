import { ref, computed, readonly } from 'vue'
import { useDataApi } from '~/layers/core/composables/useDataApi'
import { 
  SearchHistoryEntry, 
  SearchHistoryOptions,
  SearchHistoryResponse,
  SavedSearch,
  SavedSearchOptions, 
  SavedSearchResponse,
  SearchFilter,
  SearchEntityType,
  SearchError 
} from '../types/crm'

/**
 * Composable for managing search history and saved searches with Firestore persistence
 * Provides recent searches (last 10) and saved searches functionality
 */
export function useSearchHistory() {
  const dataApi = useDataApi()
  
  // Reactive state
  const isLoading = ref(false)
  const searchHistory = ref<SearchHistoryEntry[]>([])
  const savedSearches = ref<SavedSearch[]>([])
  const totalEntries = ref(0)
  const hasMore = ref(false)
  const error = ref<SearchError | null>(null)

  /**
   * Add a search query to history
   */
  const addToHistory = async (
    userId: string,
    query: string,
    filters: SearchFilter,
    resultCount: number,
    searchTime: number,
    workspaceId?: string
  ): Promise<void> => {
    try {
      // Don't add empty queries to history
      if (!query.trim()) return
      
      // Don't add duplicate consecutive searches
      if (searchHistory.value[0]?.query === query.trim() && 
          JSON.stringify(searchHistory.value[0]?.filters) === JSON.stringify(filters)) {
        return
      }
      
      const historyEntry: Omit<SearchHistoryEntry, 'id'> = {
        user_id: userId,
        workspace_id: workspaceId,
        query: query.trim(),
        filters,
        result_count: resultCount,
        search_time: searchTime,
        timestamp: new Date(),
        active: true
      }
      
      const newEntry = await dataApi.create('search_history', historyEntry, {
        workspaceId,
        profileId: userId
      })
      
      // Add to local state (prepend to beginning)
      searchHistory.value.unshift({
        id: newEntry.id,
        ...historyEntry
      })
      
      // Keep only last 10 entries in local state
      if (searchHistory.value.length > 10) {
        searchHistory.value = searchHistory.value.slice(0, 10)
      }
      
      // Clean up old entries in database (keep last 50 per user)
      await cleanupOldHistory(userId, workspaceId)
      
    } catch (err) {
      console.error('Failed to add search to history:', err)
      // Don't throw error - history is not critical functionality
    }
  }

  /**
   * Get search history for a user
   */
  const getHistory = async (options: SearchHistoryOptions): Promise<SearchHistoryResponse> => {
    isLoading.value = true
    error.value = null
    
    try {
      const { userId, workspaceId, limit = 10, offset = 0 } = options
      
      // Use dataApi to get search history with proper filtering
      const entries = await dataApi.getAll('search_history', {
        workspaceId,
        profileId: userId,
        limit: limit,
        orderBy: 'timestamp',
        orderDirection: 'desc',
        offset,
        filters: [
          { field: 'user_id', operator: 'eq', value: userId },
          { field: 'active', operator: 'eq', value: true },
          ...(workspaceId ? [{ field: 'workspace_id', operator: 'eq', value: workspaceId }] : [])
        ]
      })
      
      // Get total count for pagination
      const totalCount = await dataApi.count('search_history', {
        workspaceId,
        profileId: userId,
        filters: [
          { field: 'user_id', operator: 'eq', value: userId },
          { field: 'active', operator: 'eq', value: true },
          ...(workspaceId ? [{ field: 'workspace_id', operator: 'eq', value: workspaceId }] : [])
        ]
      })
      
      // Convert Firestore timestamps to Date objects
      const formattedEntries: SearchHistoryEntry[] = entries.map((entry: any) => ({
        ...entry,
        timestamp: entry.timestamp?.toDate ? entry.timestamp.toDate() : new Date(entry.timestamp)
      }))
      
      // Update local state
      if (offset === 0) {
        searchHistory.value = formattedEntries
      } else {
        searchHistory.value = [...searchHistory.value, ...formattedEntries]
      }
      
      totalEntries.value = totalCount
      hasMore.value = (offset + entries.length) < totalCount
      
      return {
        entries: formattedEntries,
        total: totalCount,
        hasMore: hasMore.value
      }
      
    } catch (err) {
      console.error('Failed to get search history:', err)
      
      const searchError: SearchError = {
        message: 'Failed to load search history',
        code: 'HISTORY_LOAD_ERROR',
        details: err
      }
      
      error.value = searchError
      
      return {
        entries: [],
        total: 0,
        hasMore: false
      }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Clear all search history for a user
   */
  const clearHistory = async (userId: string, workspaceId?: string): Promise<void> => {
    isLoading.value = true
    error.value = null
    
    try {
      // Mark all entries as inactive rather than deleting
      const entries = await dataApi.getAll('search_history', {
        workspaceId,
        profileId: userId,
        filters: [
          { field: 'user_id', operator: 'eq', value: userId },
          { field: 'active', operator: 'eq', value: true },
          ...(workspaceId ? [{ field: 'workspace_id', operator: 'eq', value: workspaceId }] : [])
        ]
      })
      
      // Batch update to mark as inactive
      for (const entry of entries) {
        await dataApi.update('search_history', entry.id, {
          active: false
        }, {
          workspaceId,
          profileId: userId
        })
      }
      
      // Clear local state
      searchHistory.value = []
      totalEntries.value = 0
      hasMore.value = false
      
    } catch (err) {
      console.error('Failed to clear search history:', err)
      
      const searchError: SearchError = {
        message: 'Failed to clear search history',
        code: 'HISTORY_CLEAR_ERROR',
        details: err
      }
      
      error.value = searchError
      throw searchError
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Remove a specific entry from history
   */
  const removeFromHistory = async (entryId: string, userId: string, workspaceId?: string): Promise<void> => {
    try {
      // Mark entry as inactive
      await dataApi.update('search_history', entryId, {
        active: false
      }, {
        workspaceId,
        profileId: userId
      })
      
      // Remove from local state
      searchHistory.value = searchHistory.value.filter(entry => entry.id !== entryId)
      totalEntries.value = Math.max(0, totalEntries.value - 1)
      
    } catch (err) {
      console.error('Failed to remove search history entry:', err)
      
      const searchError: SearchError = {
        message: 'Failed to remove search from history',
        code: 'HISTORY_REMOVE_ERROR',
        details: err
      }
      
      error.value = searchError
      throw searchError
    }
  }

  /**
   * Clean up old history entries (keep last 50 per user)
   */
  const cleanupOldHistory = async (userId: string, workspaceId?: string): Promise<void> => {
    try {
      // Get all entries for user, ordered by timestamp desc
      const allEntries = await dataApi.getAll('search_history', {
        workspaceId,
        profileId: userId,
        orderBy: 'timestamp',
        orderDirection: 'desc',
        filters: [
          { field: 'user_id', operator: 'eq', value: userId },
          { field: 'active', operator: 'eq', value: true },
          ...(workspaceId ? [{ field: 'workspace_id', operator: 'eq', value: workspaceId }] : [])
        ]
      })
      
      // If more than 50 entries, mark older ones as inactive
      if (allEntries.length > 50) {
        const entriesToDeactivate = allEntries.slice(50)
        
        for (const entry of entriesToDeactivate) {
          await dataApi.update('search_history', entry.id, {
            active: false
          }, {
            workspaceId,
            profileId: userId
          })
        }
      }
    } catch (err) {
      console.error('Failed to cleanup old history:', err)
      // Don't throw error - cleanup is maintenance operation
    }
  }

  /**
   * Get saved searches for a user
   */
  const getSavedSearches = async (options: SavedSearchOptions): Promise<SavedSearchResponse> => {
    isLoading.value = true
    error.value = null
    
    try {
      const { userId, workspaceId, limit = 50, offset = 0, includeInactive = false } = options
      
      const searches = await dataApi.getAll('saved_searches', {
        workspaceId,
        profileId: userId,
        limit,
        orderBy: 'updated_at',
        orderDirection: 'desc',
        offset,
        filters: [
          { field: 'user_id', operator: 'eq', value: userId },
          ...(includeInactive ? [] : [{ field: 'active', operator: 'eq', value: true }]),
          ...(workspaceId ? [{ field: 'workspace_id', operator: 'eq', value: workspaceId }] : [])
        ]
      })
      
      const totalCount = await dataApi.count('saved_searches', {
        workspaceId,
        profileId: userId,
        filters: [
          { field: 'user_id', operator: 'eq', value: userId },
          ...(includeInactive ? [] : [{ field: 'active', operator: 'eq', value: true }]),
          ...(workspaceId ? [{ field: 'workspace_id', operator: 'eq', value: workspaceId }] : [])
        ]
      })
      
      // Convert Firestore timestamps to Date objects
      const formattedSearches: SavedSearch[] = searches.map((search: any) => ({
        ...search,
        created_at: search.created_at?.toDate ? search.created_at.toDate() : new Date(search.created_at),
        updated_at: search.updated_at?.toDate ? search.updated_at.toDate() : new Date(search.updated_at),
        last_executed: search.last_executed?.toDate ? search.last_executed.toDate() : (search.last_executed ? new Date(search.last_executed) : undefined)
      }))
      
      // Update local state
      if (offset === 0) {
        savedSearches.value = formattedSearches
      } else {
        savedSearches.value = [...savedSearches.value, ...formattedSearches]
      }
      
      return {
        searches: formattedSearches,
        total: totalCount,
        hasMore: (offset + searches.length) < totalCount
      }
      
    } catch (err) {
      console.error('Failed to get saved searches:', err)
      
      const searchError: SearchError = {
        message: 'Failed to load saved searches',
        code: 'SAVED_SEARCHES_LOAD_ERROR',
        details: err
      }
      
      error.value = searchError
      
      return {
        searches: [],
        total: 0,
        hasMore: false
      }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Create a new saved search
   */
  const createSavedSearch = async (
    userId: string,
    name: string,
    query: string,
    filters: SearchFilter,
    workspaceId?: string,
    description?: string,
    tags?: string[]
  ): Promise<SavedSearch> => {
    try {
      const savedSearch: Omit<SavedSearch, 'id'> = {
        user_id: userId,
        workspace_id: workspaceId,
        name: name.trim(),
        description: description?.trim(),
        query: query.trim(),
        filters,
        is_favorite: false,
        notification_enabled: false,
        created_at: new Date(),
        updated_at: new Date(),
        execution_count: 0,
        active: true,
        tags: tags || []
      }
      
      const newSearch = await dataApi.create('saved_searches', savedSearch, {
        workspaceId,
        profileId: userId
      })
      
      const formattedSearch: SavedSearch = {
        id: newSearch.id,
        ...savedSearch
      }
      
      // Add to local state
      savedSearches.value.unshift(formattedSearch)
      
      return formattedSearch
      
    } catch (err) {
      console.error('Failed to create saved search:', err)
      
      const searchError: SearchError = {
        message: 'Failed to save search',
        code: 'SAVED_SEARCH_CREATE_ERROR',
        details: err
      }
      
      error.value = searchError
      throw searchError
    }
  }

  /**
   * Update an existing saved search
   */
  const updateSavedSearch = async (
    searchId: string,
    userId: string,
    updates: Partial<Omit<SavedSearch, 'id' | 'user_id' | 'created_at'>>,
    workspaceId?: string
  ): Promise<SavedSearch> => {
    try {
      const updatedData = {
        ...updates,
        updated_at: new Date()
      }
      
      await dataApi.update('saved_searches', searchId, updatedData, {
        workspaceId,
        profileId: userId
      })
      
      // Update local state
      const index = savedSearches.value.findIndex(search => search.id === searchId)
      if (index !== -1) {
        savedSearches.value[index] = {
          ...savedSearches.value[index],
          ...updatedData
        }
        return savedSearches.value[index]
      }
      
      // If not in local state, fetch the updated search
      const updatedSearch = await dataApi.getById('saved_searches', searchId, {
        workspaceId,
        profileId: userId
      })
      
      return {
        ...updatedSearch,
        created_at: updatedSearch.created_at?.toDate ? updatedSearch.created_at.toDate() : new Date(updatedSearch.created_at),
        updated_at: updatedSearch.updated_at?.toDate ? updatedSearch.updated_at.toDate() : new Date(updatedSearch.updated_at),
        last_executed: updatedSearch.last_executed?.toDate ? updatedSearch.last_executed.toDate() : (updatedSearch.last_executed ? new Date(updatedSearch.last_executed) : undefined)
      }
      
    } catch (err) {
      console.error('Failed to update saved search:', err)
      
      const searchError: SearchError = {
        message: 'Failed to update saved search',
        code: 'SAVED_SEARCH_UPDATE_ERROR',
        details: err
      }
      
      error.value = searchError
      throw searchError
    }
  }

  /**
   * Delete a saved search
   */
  const deleteSavedSearch = async (searchId: string, userId: string, workspaceId?: string): Promise<void> => {
    try {
      // Mark as inactive rather than deleting
      await dataApi.update('saved_searches', searchId, {
        active: false,
        updated_at: new Date()
      }, {
        workspaceId,
        profileId: userId
      })
      
      // Remove from local state
      savedSearches.value = savedSearches.value.filter(search => search.id !== searchId)
      
    } catch (err) {
      console.error('Failed to delete saved search:', err)
      
      const searchError: SearchError = {
        message: 'Failed to delete saved search',
        code: 'SAVED_SEARCH_DELETE_ERROR',
        details: err
      }
      
      error.value = searchError
      throw searchError
    }
  }

  /**
   * Execute a saved search and update execution tracking
   */
  const executeSavedSearch = async (
    searchId: string,
    userId: string,
    workspaceId?: string
  ): Promise<SavedSearch | null> => {
    try {
      const search = savedSearches.value.find(s => s.id === searchId)
      if (!search) return null
      
      // Update execution tracking
      const updatedSearch = await updateSavedSearch(searchId, userId, {
        last_executed: new Date(),
        execution_count: search.execution_count + 1
      }, workspaceId)
      
      return updatedSearch
      
    } catch (err) {
      console.error('Failed to execute saved search:', err)
      return null
    }
  }

  /**
   * Toggle favorite status of a saved search
   */
  const toggleSavedSearchFavorite = async (
    searchId: string,
    userId: string,
    workspaceId?: string
  ): Promise<void> => {
    try {
      const search = savedSearches.value.find(s => s.id === searchId)
      if (!search) return
      
      await updateSavedSearch(searchId, userId, {
        is_favorite: !search.is_favorite
      }, workspaceId)
      
    } catch (err) {
      console.error('Failed to toggle saved search favorite:', err)
      throw err
    }
  }

  /**
   * Get most frequent search queries for suggestions
   */
  const getFrequentSearches = async (userId: string, workspaceId?: string, limit = 5): Promise<string[]> => {
    try {
      const entries = await dataApi.getAll('search_history', {
        workspaceId,
        profileId: userId,
        limit: 100, // Get larger sample for analysis
        orderBy: 'timestamp',
        orderDirection: 'desc',
        filters: [
          { field: 'user_id', operator: 'eq', value: userId },
          { field: 'active', operator: 'eq', value: true },
          ...(workspaceId ? [{ field: 'workspace_id', operator: 'eq', value: workspaceId }] : [])
        ]
      })
      
      // Count query frequency
      const queryFrequency = new Map<string, number>()
      
      entries.forEach((entry: any) => {
        const query = entry.query.toLowerCase().trim()
        if (query && query.length > 2) { // Only include meaningful queries
          queryFrequency.set(query, (queryFrequency.get(query) || 0) + 1)
        }
      })
      
      // Sort by frequency and return top results
      return Array.from(queryFrequency.entries())
        .sort(([, a], [, b]) => b - a)
        .slice(0, limit)
        .map(([query]) => query)
      
    } catch (err) {
      console.error('Failed to get frequent searches:', err)
      return []
    }
  }

  /**
   * Search within history entries
   */
  const searchInHistory = async (searchQuery: string, userId: string, workspaceId?: string): Promise<SearchHistoryEntry[]> => {
    try {
      if (!searchQuery.trim()) {
        return searchHistory.value
      }
      
      const query = searchQuery.toLowerCase().trim()
      
      // Filter local state first for immediate feedback
      const localResults = searchHistory.value.filter(entry =>
        entry.query.toLowerCase().includes(query)
      )
      
      // If we have enough local results, return them
      if (localResults.length >= 5) {
        return localResults.slice(0, 10)
      }
      
      // Otherwise, search the full database
      const entries = await dataApi.getAll('search_history', {
        workspaceId,
        profileId: userId,
        limit: 50,
        orderBy: 'timestamp',
        orderDirection: 'desc',
        filters: [
          { field: 'user_id', operator: 'eq', value: userId },
          { field: 'active', operator: 'eq', value: true },
          ...(workspaceId ? [{ field: 'workspace_id', operator: 'eq', value: workspaceId }] : [])
        ]
      })
      
      // Filter results that match the search query
      return entries
        .filter((entry: any) => entry.query.toLowerCase().includes(query))
        .map((entry: any) => ({
          ...entry,
          timestamp: entry.timestamp?.toDate ? entry.timestamp.toDate() : new Date(entry.timestamp)
        }))
        .slice(0, 10)
      
    } catch (err) {
      console.error('Failed to search history:', err)
      return []
    }
  }

  /**
   * Check if a search query/filter combination is already saved
   */
  const isSearchSaved = (query: string, filters: SearchFilter): boolean => {
    return savedSearches.value.some(
      search => search.query === query && 
               JSON.stringify(search.filters) === JSON.stringify(filters)
    )
  }

  /**
   * Get formatted display text for entity types
   */
  const formatEntityTypes = (entityTypes: SearchEntityType[]): string => {
    if (!entityTypes || entityTypes.length === 0) return 'All types'
    if (entityTypes.length === 1) {
      const labels: Record<SearchEntityType, string> = {
        account: 'Accounts',
        contact: 'Contacts',
        company: 'Companies',
        product: 'Products',
        order: 'Orders'
      }
      return labels[entityTypes[0]]
    }
    if (entityTypes.length === 5) return 'All types'
    return `${entityTypes.length} types`
  }

  /**
   * Get formatted display text for filters
   */
  const formatFiltersDisplay = (filters: SearchFilter): string => {
    const parts: string[] = []
    
    if (filters.entityTypes && filters.entityTypes.length > 0 && filters.entityTypes.length < 5) {
      parts.push(formatEntityTypes(filters.entityTypes))
    }
    
    if (filters.status) {
      parts.push(`Status: ${filters.status}`)
    }
    
    if (filters.tags && filters.tags.length > 0) {
      parts.push(`Tags: ${filters.tags.slice(0, 2).join(', ')}${filters.tags.length > 2 ? '...' : ''}`)
    }
    
    return parts.length > 0 ? parts.join(' • ') : 'No filters'
  }

  /**
   * Computed properties
   */
  const hasHistory = computed(() => searchHistory.value.length > 0)
  const recentSearches = computed(() => searchHistory.value.slice(0, 10))
  const favoriteSavedSearches = computed(() => 
    savedSearches.value.filter(search => search.is_favorite)
  )
  const hasSavedSearches = computed(() => savedSearches.value.length > 0)

  return {
    // State
    isLoading: readonly(isLoading),
    searchHistory: readonly(searchHistory),
    savedSearches: readonly(savedSearches),
    totalEntries: readonly(totalEntries),
    hasMore: readonly(hasMore),
    error: readonly(error),
    
    // Computed
    hasHistory,
    recentSearches,
    favoriteSavedSearches,
    hasSavedSearches,
    
    // Search History Methods
    addToHistory,
    getHistory,
    clearHistory,
    removeFromHistory,
    getFrequentSearches,
    searchInHistory,
    
    // Saved Searches Methods
    getSavedSearches,
    createSavedSearch,
    updateSavedSearch,
    deleteSavedSearch,
    executeSavedSearch,
    toggleSavedSearchFavorite,
    isSearchSaved,
    
    // Utils
    formatEntityTypes,
    formatFiltersDisplay
  }
}