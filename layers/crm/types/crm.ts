import { Timestamp } from 'firebase/firestore'

// Base entity interface with common fields
/**
 * Base entity with common fields shared by all CRM entities
 */
export interface BaseEntity {
  id: string
  name: string
  description?: string
  status: string
  active: boolean
  workspace_ids: string[]
  profile_ids: string[]
  owner_id?: string
  created_at: Date | null
  updated_at: Date | null
  deleted_at: Date | null
  tags?: string[]
  metadata?: Record<string, any>
}

// Account entity
/**
 * Account entity representing a customer account
 */
export interface Account extends BaseEntity {
  type: string
  avatar?: string
  address?: {
    street?: string
    city?: string
    state?: string
    zip?: string
    country?: string
    coordinates?: {
      lat: number
      lng: number
    }
  }
  team?: string[]
  contact_ids?: string[]
  company_ids?: string[]
  files?: string[]
}

// Contact entity
/**
 * Contact entity representing an individual person
 */
export interface Contact extends BaseEntity {
  type: string
  avatar?: string
  email: string
  phone?: string
  job_title?: string
  company_id?: string
  company_name?: string
  account_ids?: string[]
  address?: {
    street?: string
    city?: string
    state?: string
    zip?: string
    country?: string
    coordinates?: {
      lat: number
      lng: number
    }
  }
  social?: {
    linkedin?: string
    twitter?: string
    facebook?: string
    instagram?: string
    website?: string
    other?: Record<string, string>
  }
  notes?: string
  files?: string[]
  communication_preference?: string
}

// Company/Business entity
/**
 * Company entity representing a business organization
 */
export interface Company extends BaseEntity {
  type: string
  logo?: string
  address?: {
    street?: string
    city?: string
    state?: string
    zip?: string
    country?: string
    coordinates?: {
      lat: number
      lng: number
    }
  }
  contact_ids?: string[]
  account_ids?: string[]
  industry?: string
  employees_count?: number
  annual_revenue?: number
  website?: string
  social?: {
    linkedin?: string
    twitter?: string
    facebook?: string
    instagram?: string
    other?: Record<string, string>
  }
  notes?: string
  files?: string[]
}

// Order entity
/**
 * Order entity representing a customer order
 */
export interface Order extends BaseEntity {
  order_number: string
  account_id: string
  contact_id?: string
  company_id?: string
  products: {
    product_id: string
    name: string
    quantity: number
    price: number
    total: number
  }[]
  subtotal: number
  tax: number
  discount: number
  shipping: number
  total: number
  currency?: string
  payment_status: string
  payment_method?: string
  delivery_status: string
  delivery_tracking?: string
  delivery_address?: {
    street?: string
    city?: string
    state?: string
    zip?: string
    country?: string
  }
  billing_address?: {
    street?: string
    city?: string
    state?: string
    zip?: string
    country?: string
  }
  notes?: string
  files?: string[]
}

// Product entity
/**
 * Product entity representing a product or service offered
 */
export interface Product extends BaseEntity {
  sku?: string
  type: string
  price: number
  cost?: number
  currency?: string
  tax_rate?: number
  stock_quantity?: number
  stock_status?: string
  images?: string[]
  categories?: string[]
  attributes?: Record<string, string | string[]>
  files?: string[]
  variations?: Partial<Product>[]
}

// Order item representing a product in an order
/**
 * Order item representing a product in an order
 */
export interface OrderItem {
  product_id: string
  name: string
  sku?: string
  quantity: number
  price: number
  subtotal: number
  tax?: number
  discount?: number
  total: number
}

// Relationship mapping entity to store many-to-many relationships
/**
 * Relationship map entity representing a connection between two entities
 */
export interface RelationshipMap extends BaseEntity {
  entity_type_1: 'account' | 'contact' | 'company' | 'workspace' | 'profile'
  entity_id_1: string
  entity_type_2: 'account' | 'contact' | 'company' | 'workspace' | 'profile' 
  entity_id_2: string
  relationship_type: string
  metadata?: Record<string, any>
}

// API response types
export interface CrmApiResponse<T> {
  data: T
  success: boolean
  error?: string
}

export interface CrmListApiResponse<T> {
  data: T[]
  total: number
  success: boolean
  error?: string
}

// Query filters
export interface CrmQueryFilter {
  field: string
  operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'in' | 'nin'
  value: any
}

export interface CrmQueryOptions {
  filters?: CrmQueryFilter[]
  limit?: number
  offset?: number
  orderBy?: string
  orderDirection?: 'asc' | 'desc'
  startAfter?: any
}

/**
 * Type Guard Functions
 */
export function isAccount(entity: BaseEntity): entity is Account {
  return 'type' in entity && 'contact_ids' in entity
}

export function isContact(entity: BaseEntity): entity is Contact {
  return 'type' in entity && 'email' in entity
}

export function isCompany(entity: BaseEntity): entity is Company {
  return 'type' in entity && 'industry' in entity
}

export function isProduct(entity: BaseEntity): entity is Product {
  return 'type' in entity && 'price' in entity
}

export function isOrder(entity: BaseEntity): entity is Order {
  return 'order_number' in entity && 'products' in entity && 'total' in entity
}

// Global Search Types
export type SearchEntityType = 'account' | 'contact' | 'company' | 'product' | 'order'

export interface SearchResult {
  id: string
  type: SearchEntityType
  title: string
  subtitle?: string
  description?: string
  avatar?: string
  metadata?: Record<string, any>
  entity: Account | Contact | Company | Product | Order
}

export interface SearchFilter {
  entityTypes?: SearchEntityType[]
  workspaceId?: string
  profileId?: string
  status?: string
  tags?: string[]
}

export interface SearchOptions {
  query: string
  filters?: SearchFilter
  limit?: number
  includeDeleted?: boolean
}

export interface SearchResponse {
  results: SearchResult[]
  total: number
  hasMore: boolean
  nextCursor?: string
  suggestions?: string[]
  searchTime?: number
}

// Search History & Persistence Types
export interface SearchHistoryEntry {
  id: string
  user_id: string
  workspace_id?: string
  query: string
  filters: SearchFilter
  result_count: number
  search_time: number
  timestamp: Date
  active: boolean
}

export interface SavedSearch {
  id: string
  user_id: string
  workspace_id?: string
  name: string
  description?: string
  query: string
  filters: SearchFilter
  is_favorite: boolean
  notification_enabled: boolean
  created_at: Date
  updated_at: Date
  last_executed?: Date
  execution_count: number
  active: boolean
  tags?: string[]
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  pinned?: boolean
}

export interface SearchHistoryOptions {
  userId: string
  workspaceId?: string
  limit?: number
  offset?: number
}

export interface SavedSearchOptions {
  userId: string
  workspaceId?: string
  limit?: number
  offset?: number
  includeInactive?: boolean
}

export interface SearchHistoryResponse {
  entries: SearchHistoryEntry[]
  total: number
  hasMore: boolean
}

export interface SavedSearchResponse {
  searches: SavedSearch[]
  total: number
  hasMore: boolean
}

// Error handling types
export interface SearchError {
  message: string
  code?: string
  details?: any
}

export interface SearchState {
  isLoading: boolean
  error: SearchError | null
  hasRetried: boolean
}