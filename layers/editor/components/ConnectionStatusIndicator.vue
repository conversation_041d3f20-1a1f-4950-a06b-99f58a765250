<template>
  <div class="connection-status-indicator">
    <!-- Connection Status Badge -->
    <div 
      class="flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200"
      :class="statusClasses"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
    >
      <!-- Status Icon -->
      <div 
        class="w-2 h-2 rounded-full transition-all duration-200"
        :class="iconClasses"
      />
      
      <!-- Status Text -->
      <span>{{ statusText }}</span>
      
      <!-- Reconnect <PERSON>ton (when disconnected) -->
      <button
        v-if="showReconnectButton && canShowReconnectButton"
        @click="reconnect"
        class="ml-2 px-2 py-1 text-xs bg-white bg-opacity-20 hover:bg-opacity-30 rounded transition-colors"
        :disabled="isConnecting"
      >
        {{ isConnecting ? 'Connecting...' : 'Reconnect' }}
      </button>
    </div>

    <!-- Detailed Status Tooltip (on hover) -->
    <div 
      v-if="showDetails"
      class="absolute top-full mt-2 left-0 z-50 bg-gray-900 text-white text-xs rounded-lg p-3 shadow-lg min-w-64"
    >
      <div class="space-y-1">
        <div><strong>Status:</strong> {{ statusText }}</div>
        <div v-if="connectionInfo.lastConnected">
          <strong>Last Connected:</strong> {{ formatTime(connectionInfo.lastConnected) }}
        </div>
        <div v-if="connectionInfo.reconnectAttempts > 0">
          <strong>Reconnect Attempts:</strong> {{ connectionInfo.reconnectAttempts }}
        </div>
        <div v-if="connectionInfo.error">
          <strong>Error:</strong> {{ connectionInfo.error }}
        </div>
        <div v-if="connectionInfo.url">
          <strong>URL:</strong> {{ connectionInfo.url }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { ConnectionInfo } from '../composables/useWebSocketConnection'

interface Props {
  connectionInfo: ConnectionInfo
  showReconnectButton?: boolean
  showDetailsOnHover?: boolean
}

interface Emits {
  (e: 'reconnect'): void
}

const props = withDefaults(defineProps<Props>(), {
  showReconnectButton: true,
  showDetailsOnHover: true
})

const emit = defineEmits<Emits>()

// Hover state for details tooltip
const showDetails = ref(false)

// Computed properties
const isConnecting = computed(() => 
  props.connectionInfo.status === 'connecting' || 
  props.connectionInfo.status === 'reconnecting'
)

const canShowReconnectButton = computed(() => 
  props.connectionInfo.status === 'disconnected' || 
  props.connectionInfo.status === 'error'
)

const statusText = computed(() => {
  switch (props.connectionInfo.status) {
    case 'connected':
      return 'Connected'
    case 'connecting':
      return 'Connecting...'
    case 'reconnecting':
      return `Reconnecting... (${props.connectionInfo.reconnectAttempts})`
    case 'disconnected':
      return 'Disconnected'
    case 'error':
      return 'Connection Error'
    default:
      return 'Unknown'
  }
})

const statusClasses = computed(() => {
  switch (props.connectionInfo.status) {
    case 'connected':
      return 'bg-green-100 text-green-800 border border-green-200'
    case 'connecting':
    case 'reconnecting':
      return 'bg-yellow-100 text-yellow-800 border border-yellow-200'
    case 'disconnected':
      return 'bg-gray-100 text-gray-800 border border-gray-200'
    case 'error':
      return 'bg-red-100 text-red-800 border border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border border-gray-200'
  }
})

const iconClasses = computed(() => {
  const baseClasses = 'transition-all duration-200'
  
  switch (props.connectionInfo.status) {
    case 'connected':
      return `${baseClasses} bg-green-500`
    case 'connecting':
    case 'reconnecting':
      return `${baseClasses} bg-yellow-500 animate-pulse`
    case 'disconnected':
      return `${baseClasses} bg-gray-400`
    case 'error':
      return `${baseClasses} bg-red-500 animate-pulse`
    default:
      return `${baseClasses} bg-gray-400`
  }
})

// Methods
const reconnect = () => {
  emit('reconnect')
}

const formatTime = (date: Date): string => {
  return date.toLocaleTimeString()
}

// Event handlers for hover
const handleMouseEnter = () => {
  if (props.showDetailsOnHover) {
    showDetails.value = true
  }
}

const handleMouseLeave = () => {
  showDetails.value = false
}
</script>

<style scoped>
.connection-status-indicator {
  @apply relative inline-block;
}

/* Ensure smooth animations */
.connection-status-indicator * {
  transition-property: background-color, border-color, color, opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Hover effect for the status indicator */
.connection-status-indicator:hover {
  @apply cursor-pointer;
}

/* Pulse animation for connecting states */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>