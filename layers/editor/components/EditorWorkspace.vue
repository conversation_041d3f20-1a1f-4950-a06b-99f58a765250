<template>
  <div class="editor-workspace">
    <!-- Project title -->
    <div class="editor-workspace__title" v-if="props.item">
      <div class="title-content">
        <div class="item-thumbnail" v-if="props.item.type.includes('image')">
          <img :src="props.item.url" :alt="props.item.name" />
        </div>
        <div class="item-thumbnail" v-else-if="props.item.type.includes('video')">
          <video :src="props.item.url" muted></video>
          <div class="video-icon"><i class="icon icon-video"></i></div>
        </div>
        <h1>Editing: {{ props.item.name }}</h1>
      </div>
    </div>
    
    <!-- Top toolbar with main editing tools -->
    <EditorToolbar class="editor-workspace__toolbar" />
    
    <div class="editor-workspace__main">
      <!-- Left sidebar with layers and properties panels -->
      <aside 
        v-if="showLeftPanel" 
        class="editor-workspace__panel editor-workspace__left-panel"
      >
        <div class="panel-tabs">
          <button 
            class="panel-tab" 
            :class="{ 'panel-tab--active': activeLeftTab === 'layers' }"
            @click="activeLeftTab = 'layers'"
          >
            Layers
          </button>
          <button 
            class="panel-tab" 
            :class="{ 'panel-tab--active': activeLeftTab === 'properties' }"
            @click="activeLeftTab = 'properties'"
          >
            Properties
          </button>
        </div>
        
        <div class="panel-content">
          <div v-if="activeLeftTab === 'layers'" class="panel-tab-content">
            <!-- Layers Panel -->
            <LayersPanel />
          </div>
          
          <div v-else-if="activeLeftTab === 'properties'" class="panel-tab-content">
            <!-- Properties Panel - Tool-specific controls -->
            <component 
              :is="activeTool ? `${capitalize(activeTool)}Tool` : 'DefaultProperties'"
              v-if="toolComponentExists"
            />
            <DefaultProperties v-else />
          </div>
        </div>
      </aside>
      
      <!-- Center area with the main canvas -->
      <main class="editor-workspace__canvas-container">
        <!-- Button to toggle left panel -->
        <button 
          class="panel-toggle panel-toggle--left"
          @click="showLeftPanel = !showLeftPanel"
          :title="showLeftPanel ? 'Hide panel' : 'Show panel'"
        >
          <i :class="['icon', showLeftPanel ? 'icon-chevron-left' : 'icon-chevron-right']"></i>
        </button>
        
        <!-- Main editing canvas -->
        <EditorCanvas class="editor-workspace__canvas" />
        
        <!-- Button to toggle right panel -->
        <button 
          class="panel-toggle panel-toggle--right"
          @click="showRightPanel = !showRightPanel"
          :title="showRightPanel ? 'Hide panel' : 'Show panel'"
        >
          <i :class="['icon', showRightPanel ? 'icon-chevron-right' : 'icon-chevron-left']"></i>
        </button>
      </main>
      
      <!-- Right sidebar with AI chat assistant -->
      <EditorChat 
        v-if="showRightPanel" 
        class="editor-workspace__panel editor-workspace__right-panel"
        @close="showRightPanel = false"
      />
    </div>
    
    <!-- Status bar with information and additional controls -->
    <div class="editor-workspace__statusbar">
      <div class="statusbar-section">
        <span v-if="isLoading" class="statusbar-loader"></span>
        <span class="statusbar-message">{{ statusMessage }}</span>
      </div>
      
      <div class="statusbar-section statusbar-section--center">
        <span v-if="activeLayer" class="statusbar-info">
          {{ activeLayer.name }} | {{ projectState.width }}×{{ projectState.height }}px
        </span>
        <span v-else-if="props.item" class="statusbar-info">
          {{ props.item.name }} | {{ props.item.type }}
        </span>
      </div>
      
      <div class="statusbar-section statusbar-section--right">
        <!-- User Presence Indicator -->
        <UserPresenceIndicator 
          :presence-state="presenceState"
          :max-users="10"
          :max-display-users="5"
          :show-avatars="true"
          :show-tooltips="true"
          class="mr-4"
        />
        
        <!-- WebSocket Connection Status -->
        <ConnectionStatusIndicator 
          :connection-info="connectionInfo"
          :show-reconnect-button="true"
          @reconnect="handleReconnect"
          class="mr-4"
        />
        
        <button class="statusbar-button" @click="handleSaveProject" title="Save project" :disabled="apiLoading">
          <i class="icon icon-save"></i>
          <span>{{ isSaved ? 'Saved' : 'Save' }}</span>
        </button>
        
        <button class="statusbar-button" @click="handleExportImage" title="Export image">
          <i class="icon icon-export"></i>
          <span>Export</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineAsyncComponent, onMounted, onUnmounted, watch } from 'vue';
import EditorToolbar from './EditorToolbar.vue';
import EditorCanvas from './EditorCanvas.vue';
import EditorChat from './EditorChat.vue';
import DefaultProperties from './properties/DefaultProperties.vue';
import LayersPanel from './panels/LayersPanel.vue';
import ConnectionStatusIndicator from './ConnectionStatusIndicator.vue';
import UserPresenceIndicator from './UserPresenceIndicator.vue';
import { useEditorState } from '../composables/useEditorState';
import { useWebSocketConnection } from '../composables/useWebSocketConnection';
import { useUserPresence } from '../composables/useUserPresence';
import { useCursorTracking } from '../composables/useCursorTracking';

// Define proper typing for Upload prop
interface Upload {
  id: string;
  name: string;
  type: string;
  url: string;
  // Add other properties of Upload as needed
}

const props = defineProps<{
  item: Upload
}>();

// Get editor state
const { 
  activeTool, 
  isLoading, 
  isSaved, 
  activeLayer,
  projectState,
  saveProject, 
  loadProject,
  getUserProjects,
  deleteProject,
  exportImage,
  loadItem,
  apiLoading,
  apiError
} = useEditorState();

// Initialize editor with the provided item
onMounted(() => {
  if (props.item) {
    loadItem(props.item);
  }
});

// Watch for changes to the item prop
watch(() => props.item, (newItem) => {
  if (newItem) {
    loadItem(newItem);
  }
}, { deep: true });

// Panel visibility state
const showLeftPanel = ref(true);
const showRightPanel = ref(true);
const activeLeftTab = ref('layers'); // 'layers' or 'properties'

// Workspace and profile context (would typically come from a global store or prop)
const workspaceId = ref<string | undefined>(undefined);
const profileId = ref<string | undefined>(undefined);

// WebSocket connection management for real-time collaboration
const {
  connectionInfo,
  isConnected,
  isConnecting,
  connect,
  disconnect,
  reconnect,
  send,
  on,
  off
} = useWebSocketConnection({
  workspaceId: workspaceId.value || 'default-workspace',
  projectId: props.item?.id,
  userId: profileId.value,
  autoConnect: true,
  reconnectInterval: 3000,
  maxReconnectAttempts: 10
});

// User presence management for live collaboration
const {
  presenceState,
  activeUsers,
  currentUser,
  totalUsers,
  isAtCapacity,
  initializeCurrentUser,
  updateCursorPosition,
  updateCurrentTool,
  updateTypingStatus,
  leavePresence
} = useUserPresence({
  workspaceId: workspaceId.value || 'default-workspace',
  projectId: props.item?.id,
  userId: profileId.value,
  maxUsers: 10,
  presenceUpdateInterval: 5000,
  cursorUpdateThrottle: 100
});

// Cursor tracking for real-time cursor synchronization
const {
  initializeCursorTracking,
  startTracking,
  stopTracking,
  isTracking
} = useCursorTracking(
  { presenceState, updateCursorPosition, activeUsers, currentUser },
  {
    trackingElement: '.editor-workspace__canvas',
    throttleMs: 50,
    showCursors: true
  }
);

// Status message based on editor state
const statusMessage = computed(() => {
  if (isLoading.value || apiLoading.value) {
    if (apiLoading.value) {
      return 'Saving project...';
    }
    return props.item ? `Loading ${props.item.name}...` : 'Processing...';
  }
  
  if (apiError.value) {
    return `Error: ${apiError.value}`;
  }
  
  if (activeTool.value) {
    return `Tool: ${activeTool.value}`;
  }
  
  if (activeLayer.value) {
    return `Editing: ${activeLayer.value.name}`;
  }
  
  return props.item ? `Ready to edit ${props.item.name}` : 'Ready';
});

// Helper to capitalize the first letter (for dynamic component names)
const capitalize = (str: string | null): string => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};

// Dynamic tool components
const toolComponentMapping: Record<string, string> = {
  'crop': 'CropTool',
  'text': 'TextTool',
  'shape': 'ShapeTool',
  'enhance': 'EnhanceTool',
  'background-removal': 'BackgroundRemovalTool',
};

// Check if the component for the active tool exists
const toolComponentExists = computed(() => {
  if (!activeTool.value) return false;
  
  try {
    return !!toolComponentMapping[activeTool.value];
  } catch (error) {
    console.warn(`Tool component for ${activeTool.value} not found`);
    return false;
  }
});

// Dynamically import tool components
// Wrapper functions for project operations with workspace/profile context
const handleSaveProject = async () => {
  await saveProject(workspaceId.value, profileId.value);
};

const handleExportImage = async () => {
  // This function would need to be updated in useEditorState to accept workspace/profile context
  return exportImage();
};

// WebSocket event handlers
const handleReconnect = () => {
  reconnect();
};

// Initialize user presence when component mounts
onMounted(() => {
  // Initialize current user presence if we have user data
  if (profileId.value) {
    initializeCurrentUser({
      id: profileId.value,
      name: 'Current User', // Would come from user profile
      email: '<EMAIL>', // Would come from user profile
      avatar: undefined // Would come from user profile
    });
  }
  
  // Initialize cursor tracking on the canvas
  setTimeout(() => {
    initializeCursorTracking();
  }, 100); // Small delay to ensure DOM is ready
});

// Watch for active tool changes to update presence
watch(activeTool, (newTool) => {
  if (newTool) {
    updateCurrentTool(newTool);
  }
});

// Setup WebSocket event listeners for real-time collaboration
onMounted(() => {
  // Listen for real-time events
  on('connected', () => {
    console.log('WebSocket connected - ready for real-time collaboration');
  });

  on('disconnected', () => {
    console.log('WebSocket disconnected - collaboration features disabled');
  });

  on('user-joined', (event: any) => {
    console.log('User joined:', event.detail);
    // Handle user presence updates
  });

  on('user-left', (event: any) => {
    console.log('User left:', event.detail);
    // Handle user presence updates
  });

  on('layer-update', (event: any) => {
    console.log('Layer update received:', event.detail);
    // Handle real-time layer synchronization
  });

  on('error', (event: any) => {
    console.error('WebSocket error:', event.detail);
  });
});

// Cleanup on unmount
onUnmounted(() => {
  // Stop cursor tracking
  stopTracking();
  
  // Leave presence
  leavePresence();
});

// Dynamically import tool components
const CropTool = defineAsyncComponent(() => import('./tools/CropTool.vue'));
const TextTool = defineAsyncComponent(() => import('./tools/TextTool.vue').catch(() => DefaultProperties));
const ShapeTool = defineAsyncComponent(() => import('./tools/ShapeTool.vue').catch(() => DefaultProperties));
const EnhanceTool = defineAsyncComponent(() => import('./tools/EnhanceTool.vue').catch(() => DefaultProperties));
const BackgroundRemovalTool = defineAsyncComponent(() => import('./tools/BackgroundRemovalTool.vue').catch(() => DefaultProperties));
</script>

<style scoped>
.editor-workspace {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--color-background);
}

.editor-workspace__title {
  padding: 8px 16px;
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
}

.title-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.item-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  background-color: var(--color-background-subtle);
  border: 1px solid var(--color-border);
}

.item-thumbnail img,
.item-thumbnail video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.editor-workspace__title h1 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 500;
  color: var(--color-text);
}

.editor-workspace__toolbar {
  margin: 8px;
  z-index: 10;
}

.editor-workspace__main {
  flex: 1;
  display: flex;
  position: relative;
  overflow: hidden;
}

.editor-workspace__panel {
  display: flex;
  flex-direction: column;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  z-index: 5;
  overflow: hidden;
}

.editor-workspace__left-panel {
  width: 280px;
  border-right: none;
  border-left: none;
  border-radius: 0 8px 8px 0;
}

.editor-workspace__right-panel {
  border-left: none;
  border-right: none;
  border-radius: 8px 0 0 8px;
}

.editor-workspace__canvas-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-background-subtle);
}

.editor-workspace__canvas {
  position: relative;
  margin: 0 auto;
}

.editor-workspace__statusbar {
  height: 36px;
  border-top: 1px solid var(--color-border);
  background-color: var(--color-surface);
  display: flex;
  align-items: center;
  padding: 0 16px;
  font-size: 0.875rem;
}

/* Panel components */
.panel-tabs {
  display: flex;
  border-bottom: 1px solid var(--color-border);
}

.panel-tab {
  flex: 1;
  padding: 10px;
  text-align: center;
  border: none;
  background-color: transparent;
  color: var(--color-text-muted);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.panel-tab:hover {
  color: var(--color-text);
  background-color: var(--color-surface-hover);
}

.panel-tab--active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
}

.panel-tab-content {
  padding: 16px;
  height: 100%;
}

/* Toggle buttons */
.panel-toggle {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 36px;
  border: 1px solid var(--color-border);
  background-color: var(--color-surface);
  color: var(--color-text-muted);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 6;
}

.panel-toggle:hover {
  color: var(--color-text);
  background-color: var(--color-surface-hover);
}

.panel-toggle--left {
  left: -1px;
  border-radius: 0 4px 4px 0;
}

.panel-toggle--right {
  right: -1px;
  border-radius: 4px 0 0 4px;
}

/* Status bar */
.statusbar-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.statusbar-section--center {
  flex: 1;
  justify-content: center;
}

.statusbar-section--right {
  margin-left: auto;
}

.statusbar-loader {
  width: 12px;
  height: 12px;
  border: 2px solid var(--color-border);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s infinite linear;
}

@keyframes spin {
  100% {
    transform: rotate(360deg);
  }
}

.statusbar-message {
  color: var(--color-text-muted);
}

.statusbar-info {
  color: var(--color-text-muted);
  font-size: 0.75rem;
}

.statusbar-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  border: none;
  background-color: transparent;
  color: var(--color-text);
  cursor: pointer;
  font-size: 0.75rem;
  transition: all 0.2s;
}

.statusbar-button:hover:not(:disabled) {
  background-color: var(--color-surface-hover);
}

.statusbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .editor-workspace__left-panel {
    width: 240px;
  }
  
  .editor-workspace__right-panel {
    width: 280px;
  }
}
</style>