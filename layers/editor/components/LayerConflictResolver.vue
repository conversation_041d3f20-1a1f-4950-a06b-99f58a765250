<template>
  <div v-if="hasConflicts" class="layer-conflict-resolver">
    <!-- Conflict notification banner -->
    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-4">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <ExclamationTriangleIcon class="h-5 w-5 text-yellow-400" />
        </div>
        <div class="ml-3 flex-1">
          <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
            Layer Synchronization Conflicts Detected
          </h3>
          <div class="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
            {{ conflicts.length }} conflict{{ conflicts.length > 1 ? 's' : '' }} found while synchronizing layers.
            <button 
              @click="showDetails = !showDetails"
              class="font-medium underline hover:no-underline"
            >
              {{ showDetails ? 'Hide' : 'Show' }} details
            </button>
          </div>
        </div>
        <div class="ml-auto pl-3">
          <div class="flex space-x-2">
            <button
              @click="resolveAllConflicts('auto')"
              class="bg-yellow-100 hover:bg-yellow-200 dark:bg-yellow-800 dark:hover:bg-yellow-700 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded text-sm font-medium transition-colors"
            >
              Auto Resolve
            </button>
            <button
              @click="clearAllConflicts"
              class="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-200 px-2 py-1 text-sm font-medium transition-colors"
            >
              Dismiss All
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Detailed conflict list -->
    <div v-if="showDetails" class="space-y-3">
      <div
        v-for="(conflict, index) in conflicts"
        :key="conflict.operation.id"
        class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm"
      >
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <!-- Conflict header -->
            <div class="flex items-center space-x-2 mb-2">
              <div class="flex items-center space-x-1">
                <LayerIcon class="h-4 w-4 text-gray-500" />
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Layer: {{ getLayerName(conflict.operation.layerId) }}
                </span>
              </div>
              <div class="px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 text-xs rounded-full">
                {{ formatConflictType(conflict.conflictType) }}
              </div>
              <div class="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs rounded-full">
                {{ conflict.operation.type }}
              </div>
            </div>

            <!-- Conflict details -->
            <div class="text-sm text-gray-600 dark:text-gray-400 mb-3">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <span class="font-medium">Operation by:</span>
                  <UserAvatar 
                    :user-id="conflict.operation.userId" 
                    :size="'xs'" 
                    class="ml-1 inline-block"
                  />
                  {{ getUserName(conflict.operation.userId) }}
                </div>
                <div>
                  <span class="font-medium">Timestamp:</span>
                  {{ formatTimestamp(conflict.operation.timestamp) }}
                </div>
              </div>
            </div>

            <!-- Operation data preview -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded p-3 mb-3">
              <div class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Operation Data:
              </div>
              <pre class="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap">{{ 
                JSON.stringify(conflict.operation.data, null, 2) 
              }}</pre>
            </div>

            <!-- Resolution strategy -->
            <div class="flex items-center space-x-4">
              <div class="text-sm">
                <span class="font-medium text-gray-700 dark:text-gray-300">Resolution:</span>
                <select
                  v-model="conflict.strategy"
                  @change="updateConflictStrategy(index, conflict.strategy)"
                  class="ml-2 text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-800"
                >
                  <option value="auto">Auto Merge</option>
                  <option value="manual">Manual Review</option>
                  <option value="overwrite">Overwrite</option>
                  <option value="discard">Discard</option>
                </select>
              </div>
              
              <div v-if="conflict.resolved" class="flex items-center text-green-600 dark:text-green-400">
                <CheckCircleIcon class="h-4 w-4 mr-1" />
                <span class="text-sm font-medium">Resolved</span>
              </div>
            </div>
          </div>

          <!-- Conflict actions -->
          <div class="flex flex-col space-y-2 ml-4">
            <button
              v-if="!conflict.resolved"
              @click="resolveConflict(index, 'merge')"
              class="bg-green-100 hover:bg-green-200 dark:bg-green-800 dark:hover:bg-green-700 text-green-800 dark:text-green-200 px-3 py-1 rounded text-sm font-medium transition-colors"
            >
              Apply Merge
            </button>
            <button
              v-if="!conflict.resolved"
              @click="resolveConflict(index, 'override')"
              class="bg-blue-100 hover:bg-blue-200 dark:bg-blue-800 dark:hover:bg-blue-700 text-blue-800 dark:text-blue-200 px-3 py-1 rounded text-sm font-medium transition-colors"
            >
              Override
            </button>
            <button
              @click="dismissConflict(index)"
              class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 px-3 py-1 text-sm font-medium transition-colors"
            >
              Dismiss
            </button>
          </div>
        </div>

        <!-- Merge preview for updates -->
        <div v-if="conflict.operation.type === 'update' && conflict.strategy === 'merge'" class="mt-4 border-t border-gray-200 dark:border-gray-600 pt-3">
          <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Merge Preview:</div>
          <div class="bg-green-50 dark:bg-green-900/20 rounded p-2">
            <div class="text-xs text-green-800 dark:text-green-300">
              <div v-for="(value, key) in getMergePreview(conflict)" :key="key" class="flex justify-between">
                <span class="font-medium">{{ key }}:</span>
                <span>{{ formatValue(value) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ExclamationTriangleIcon, CheckCircleIcon } from '@heroicons/vue/24/outline'
import type { ConflictResolution } from '../composables/useLayerSync'
import type { EditorLayer } from '../composables/useEditorState'

// Icons (placeholder - replace with actual icon components)
const LayerIcon = ExclamationTriangleIcon
const UserAvatar = defineComponent({
  props: ['userId', 'size'],
  template: '<div class="w-4 h-4 bg-gray-300 rounded-full"></div>'
})

interface Props {
  conflicts: ConflictResolution[]
  layers: EditorLayer[]
  users?: Record<string, { name: string; avatar?: string }>
}

interface Emits {
  (e: 'resolve-conflict', index: number, strategy: string): void
  (e: 'dismiss-conflict', index: number): void
  (e: 'clear-all-conflicts'): void
  (e: 'resolve-all-conflicts', strategy: string): void
  (e: 'update-strategy', index: number, strategy: string): void
}

const props = withDefaults(defineProps<Props>(), {
  conflicts: () => [],
  layers: () => [],
  users: () => ({})
})

const emit = defineEmits<Emits>()

// Local state
const showDetails = ref(false)

// Computed properties
const hasConflicts = computed(() => props.conflicts.length > 0)

// Methods
const getLayerName = (layerId: string): string => {
  const layer = props.layers.find(l => l.id === layerId)
  return layer?.name || layerId
}

const getUserName = (userId: string): string => {
  return props.users[userId]?.name || userId
}

const formatConflictType = (type: string): string => {
  return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatTimestamp = (timestamp: number): string => {
  return new Date(timestamp).toLocaleTimeString()
}

const formatValue = (value: any): string => {
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  return String(value)
}

const getMergePreview = (conflict: ConflictResolution): Record<string, any> => {
  if (conflict.operation.type === 'update') {
    return conflict.operation.data?.updates || {}
  }
  return {}
}

const resolveConflict = (index: number, strategy: string) => {
  emit('resolve-conflict', index, strategy)
}

const dismissConflict = (index: number) => {
  emit('dismiss-conflict', index)
}

const clearAllConflicts = () => {
  emit('clear-all-conflicts')
}

const resolveAllConflicts = (strategy: string) => {
  emit('resolve-all-conflicts', strategy)
}

const updateConflictStrategy = (index: number, strategy: string) => {
  emit('update-strategy', index, strategy)
}

// Watch for conflict changes
watch(
  () => props.conflicts.length,
  (newCount, oldCount) => {
    if (newCount > oldCount) {
      // New conflicts detected, auto-show details
      showDetails.value = true
    } else if (newCount === 0) {
      // All conflicts resolved, hide details
      showDetails.value = false
    }
  }
)
</script>

<style scoped>
.layer-conflict-resolver {
  @apply space-y-4;
}

/* Animation for conflict items */
.conflict-item-enter-active,
.conflict-item-leave-active {
  transition: all 0.3s ease;
}

.conflict-item-enter-from,
.conflict-item-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Custom scrollbar for code preview */
pre {
  scrollbar-width: thin;
  scrollbar-color: rgb(156 163 175) transparent;
}

pre::-webkit-scrollbar {
  width: 6px;
}

pre::-webkit-scrollbar-track {
  background: transparent;
}

pre::-webkit-scrollbar-thumb {
  background-color: rgb(156 163 175);
  border-radius: 3px;
}

pre::-webkit-scrollbar-thumb:hover {
  background-color: rgb(107 114 128);
}
</style>