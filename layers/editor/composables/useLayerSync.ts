import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue'
import { useWebSocketConnection } from './useWebSocketConnection'
import { useEditorState } from './useEditorState'
import type { WebSocketConnectionOptions } from './useWebSocketConnection'
import type { EditorLayer } from './useEditorState'

export interface LayerOperation {
  id: string
  type: 'create' | 'update' | 'delete' | 'reorder'
  layerId: string
  data: any
  userId: string
  timestamp: number
  version: number
  projectId?: string
}

export interface LayerVersionInfo {
  layerId: string
  version: number
  lastModified: number
  lastModifiedBy: string
}

export interface ConflictResolution {
  operation: LayerOperation
  conflictType: 'concurrent_edit' | 'version_mismatch' | 'deleted_layer'
  strategy: 'merge' | 'overwrite' | 'manual'
  resolved: boolean
}

export interface LayerSyncOptions extends WebSocketConnectionOptions {
  syncThreshold?: number // Max time to wait before sending batched operations (ms)
  maxBatchSize?: number // Maximum operations to batch together
  conflictResolutionStrategy?: 'auto' | 'manual'
  enableOptimisticUpdates?: boolean
}

export interface LayerSyncState {
  isSync: boolean
  pendingOperations: LayerOperation[]
  layerVersions: Map<string, LayerVersionInfo>
  conflicts: ConflictResolution[]
  lastSyncTime: number
  operationQueue: LayerOperation[]
}

/**
 * Real-time layer synchronization with operational transformation
 * Implements Story 1.3: Real-time Layer Synchronization
 */
export const useLayerSync = (options: LayerSyncOptions) => {
  const {
    syncThreshold = 50, // 50ms for sub-100ms sync requirement
    maxBatchSize = 10,
    conflictResolutionStrategy = 'auto',
    enableOptimisticUpdates = true
  } = options

  // Get editor state and WebSocket connection
  const editorState = useEditorState()
  const {
    connectionInfo,
    isConnected,
    send,
    on,
    off
  } = useWebSocketConnection(options)

  // Sync state
  const syncState = reactive<LayerSyncState>({
    isSync: false,
    pendingOperations: [],
    layerVersions: new Map(),
    conflicts: [],
    lastSyncTime: 0,
    operationQueue: []
  })

  // Batching state
  const batchTimer = ref<NodeJS.Timeout | null>(null)
  const currentBatch = ref<LayerOperation[]>([])
  
  // Operation tracking
  const operationHistory = ref<Map<string, LayerOperation>>(new Map())
  const nextVersion = ref(1)

  // Computed properties
  const hasPendingOperations = computed(() => syncState.pendingOperations.length > 0)
  const hasConflicts = computed(() => syncState.conflicts.length > 0)
  const syncLatency = computed(() => Date.now() - syncState.lastSyncTime)

  /**
   * Initialize layer synchronization
   */
  const initializeLayerSync = () => {
    // Initialize version tracking for existing layers
    editorState.layers.value.forEach(layer => {
      initializeLayerVersion(layer.id)
    })

    // Start watching for local layer changes
    startLocalChangeTracking()
    
    // Setup WebSocket message handlers
    setupWebSocketHandlers()
    
    syncState.isSync = true
    console.log('Layer synchronization initialized')
  }

  /**
   * Initialize version tracking for a layer
   */
  const initializeLayerVersion = (layerId: string, version = 1) => {
    syncState.layerVersions.set(layerId, {
      layerId,
      version,
      lastModified: Date.now(),
      lastModifiedBy: options.userId || 'unknown'
    })
  }

  /**
   * Create a layer operation
   */
  const createLayerOperation = (
    type: LayerOperation['type'],
    layerId: string,
    data: any,
    projectId?: string
  ): LayerOperation => {
    const operation: LayerOperation = {
      id: `op-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      layerId,
      data,
      userId: options.userId || 'unknown',
      timestamp: Date.now(),
      version: getNextVersion(layerId),
      projectId: projectId || options.projectId
    }

    // Store in operation history
    operationHistory.value.set(operation.id, operation)
    
    return operation
  }

  /**
   * Get next version number for a layer
   */
  const getNextVersion = (layerId: string): number => {
    const versionInfo = syncState.layerVersions.get(layerId)
    const newVersion = versionInfo ? versionInfo.version + 1 : 1
    
    // Update version info
    syncState.layerVersions.set(layerId, {
      layerId,
      version: newVersion,
      lastModified: Date.now(),
      lastModifiedBy: options.userId || 'unknown'
    })
    
    return newVersion
  }

  /**
   * Add operation to queue for batching
   */
  const queueOperation = (operation: LayerOperation) => {
    currentBatch.value.push(operation)
    syncState.operationQueue.push(operation)

    // Start batch timer if not already running
    if (!batchTimer.value) {
      batchTimer.value = setTimeout(() => {
        sendBatchedOperations()
      }, syncThreshold)
    }

    // Send immediately if batch is full
    if (currentBatch.value.length >= maxBatchSize) {
      sendBatchedOperations()
    }
  }

  /**
   * Send batched operations to other clients
   */
  const sendBatchedOperations = () => {
    if (currentBatch.value.length === 0 || !isConnected.value) {
      return
    }

    const batch = [...currentBatch.value]
    currentBatch.value = []
    
    if (batchTimer.value) {
      clearTimeout(batchTimer.value)
      batchTimer.value = null
    }

    // Send batch to server
    const success = send({
      type: 'layer-operations-batch',
      operations: batch,
      workspaceId: options.workspaceId,
      projectId: options.projectId,
      timestamp: Date.now()
    })

    if (success) {
      // Move operations to pending (waiting for confirmation)
      syncState.pendingOperations.push(...batch)
      syncState.lastSyncTime = Date.now()
      
      console.log(`Sent batch of ${batch.length} layer operations`)
    } else {
      // Re-queue failed operations
      currentBatch.value.unshift(...batch)
      console.warn('Failed to send layer operations batch')
    }
  }

  /**
   * Handle layer creation with sync
   */
  const syncLayerCreate = (layer: Partial<EditorLayer>) => {
    const layerId = editorState.addLayer(layer)
    
    if (layerId) {
      initializeLayerVersion(layerId)
      
      const operation = createLayerOperation('create', layerId, {
        layer: editorState.layers.value.find(l => l.id === layerId)
      })
      
      queueOperation(operation)
    }
    
    return layerId
  }

  /**
   * Handle layer updates with sync
   */
  const syncLayerUpdate = (layerId: string, updates: Partial<EditorLayer>) => {
    // Apply optimistic update locally
    if (enableOptimisticUpdates) {
      editorState.updateLayer(layerId, updates)
    }
    
    const operation = createLayerOperation('update', layerId, { updates })
    queueOperation(operation)
  }

  /**
   * Handle layer deletion with sync
   */
  const syncLayerDelete = (layerId: string) => {
    const operation = createLayerOperation('delete', layerId, { layerId })
    
    // Apply optimistic deletion
    if (enableOptimisticUpdates) {
      editorState.removeLayer(layerId)
    }
    
    queueOperation(operation)
  }

  /**
   * Handle layer reordering with sync
   */
  const syncLayerReorder = (fromIndex: number, toIndex: number) => {
    // Apply optimistic reorder
    if (enableOptimisticUpdates) {
      editorState.reorderLayers(fromIndex, toIndex)
    }
    
    const operation = createLayerOperation('reorder', 'reorder-op', {
      fromIndex,
      toIndex,
      layerOrder: editorState.layers.value.map(l => l.id)
    })
    
    queueOperation(operation)
  }

  /**
   * Apply incoming layer operation from remote client
   */
  const applyRemoteOperation = (operation: LayerOperation) => {
    console.log(`Applying remote operation: ${operation.type} on layer ${operation.layerId}`)

    try {
      // Check for conflicts
      const conflict = detectConflict(operation)
      if (conflict) {
        handleConflict(conflict)
        return
      }

      // Apply operational transformation if needed
      const transformedOperation = transformOperation(operation)
      
      // Apply the operation
      switch (transformedOperation.type) {
        case 'create':
          handleRemoteLayerCreate(transformedOperation)
          break
        case 'update':
          handleRemoteLayerUpdate(transformedOperation)
          break
        case 'delete':
          handleRemoteLayerDelete(transformedOperation)
          break
        case 'reorder':
          handleRemoteLayerReorder(transformedOperation)
          break
      }

      // Update version tracking
      updateLayerVersion(operation.layerId, operation.version, operation.userId)
      
    } catch (error) {
      console.error('Error applying remote operation:', error)
    }
  }

  /**
   * Detect conflicts in incoming operations
   */
  const detectConflict = (operation: LayerOperation): ConflictResolution | null => {
    const versionInfo = syncState.layerVersions.get(operation.layerId)
    
    if (!versionInfo) {
      // No version info means this is a new layer or we're out of sync
      return null
    }

    // Check for version conflicts
    if (operation.version <= versionInfo.version) {
      return {
        operation,
        conflictType: 'version_mismatch',
        strategy: conflictResolutionStrategy === 'auto' ? 'merge' : 'manual',
        resolved: false
      }
    }

    // Check for concurrent edits (same timestamp different users)
    if (Math.abs(operation.timestamp - versionInfo.lastModified) < 1000 && 
        operation.userId !== versionInfo.lastModifiedBy) {
      return {
        operation,
        conflictType: 'concurrent_edit',
        strategy: conflictResolutionStrategy === 'auto' ? 'merge' : 'manual',
        resolved: false
      }
    }

    return null
  }

  /**
   * Handle conflict resolution
   */
  const handleConflict = (conflict: ConflictResolution) => {
    syncState.conflicts.push(conflict)
    
    if (conflict.strategy === 'merge') {
      // Attempt automatic merge
      const merged = attemptAutoMerge(conflict)
      if (merged) {
        conflict.resolved = true
        console.log('Conflict auto-resolved through merge')
      }
    }
    
    console.warn('Layer operation conflict detected:', conflict.conflictType)
  }

  /**
   * Attempt automatic merge of conflicting operations
   */
  const attemptAutoMerge = (conflict: ConflictResolution): boolean => {
    const { operation } = conflict
    
    if (operation.type === 'update') {
      // For updates, we can often merge non-conflicting properties
      try {
        const existingLayer = editorState.layers.value.find(l => l.id === operation.layerId)
        if (existingLayer) {
          // Apply non-conflicting updates
          const safeUpdates = filterSafeUpdates(operation.data.updates, existingLayer)
          editorState.updateLayer(operation.layerId, safeUpdates)
          return true
        }
      } catch (error) {
        console.error('Auto-merge failed:', error)
      }
    }
    
    return false
  }

  /**
   * Filter updates that can be safely merged
   */
  const filterSafeUpdates = (updates: Partial<EditorLayer>, existingLayer: EditorLayer): Partial<EditorLayer> => {
    const safeUpdates: Partial<EditorLayer> = {}
    
    // Safe properties that can be merged without conflicts
    const safeProperties = ['opacity', 'visible', 'locked']
    
    for (const [key, value] of Object.entries(updates)) {
      if (safeProperties.includes(key)) {
        safeUpdates[key as keyof EditorLayer] = value
      }
    }
    
    return safeUpdates
  }

  /**
   * Transform operation using operational transformation
   */
  const transformOperation = (operation: LayerOperation): LayerOperation => {
    // For now, return the operation as-is
    // TODO: Implement full OT algorithm based on operation history
    return operation
  }

  /**
   * Handle remote layer creation
   */
  const handleRemoteLayerCreate = (operation: LayerOperation) => {
    const { layer } = operation.data
    
    // Check if layer already exists (duplicate operation)
    if (editorState.layers.value.find(l => l.id === layer.id)) {
      console.warn('Layer already exists, skipping creation')
      return
    }
    
    // Add layer without triggering local sync
    const tempSync = syncState.isSync
    syncState.isSync = false
    editorState.addLayer(layer)
    syncState.isSync = tempSync
  }

  /**
   * Handle remote layer updates
   */
  const handleRemoteLayerUpdate = (operation: LayerOperation) => {
    const { updates } = operation.data
    
    // Apply update without triggering local sync
    const tempSync = syncState.isSync
    syncState.isSync = false
    editorState.updateLayer(operation.layerId, updates)
    syncState.isSync = tempSync
  }

  /**
   * Handle remote layer deletion
   */
  const handleRemoteLayerDelete = (operation: LayerOperation) => {
    // Remove layer without triggering local sync
    const tempSync = syncState.isSync
    syncState.isSync = false
    editorState.removeLayer(operation.layerId)
    syncState.isSync = tempSync
    
    // Clean up version tracking
    syncState.layerVersions.delete(operation.layerId)
  }

  /**
   * Handle remote layer reordering
   */
  const handleRemoteLayerReorder = (operation: LayerOperation) => {
    const { fromIndex, toIndex } = operation.data
    
    // Apply reorder without triggering local sync
    const tempSync = syncState.isSync
    syncState.isSync = false
    editorState.reorderLayers(fromIndex, toIndex)
    syncState.isSync = tempSync
  }

  /**
   * Update layer version information
   */
  const updateLayerVersion = (layerId: string, version: number, userId: string) => {
    syncState.layerVersions.set(layerId, {
      layerId,
      version,
      lastModified: Date.now(),
      lastModifiedBy: userId
    })
  }

  /**
   * Start tracking local layer changes
   */
  const startLocalChangeTracking = () => {
    // Watch for layer array changes
    watch(
      () => editorState.layers.value,
      (newLayers, oldLayers) => {
        if (!syncState.isSync) return
        
        // Detect layer changes and queue operations
        // This is a simplified approach - in production, you'd want more sophisticated change detection
        console.log('Layer changes detected, queuing sync operations')
      },
      { deep: true }
    )
  }

  /**
   * Setup WebSocket message handlers
   */
  const setupWebSocketHandlers = () => {
    on('message', (event: any) => {
      const data = event.detail
      
      switch (data.type) {
        case 'layer-operations-batch':
          handleIncomingBatch(data)
          break
        case 'layer-operation-confirmation':
          handleOperationConfirmation(data)
          break
        case 'layer-sync-request':
          handleSyncRequest(data)
          break
      }
    })
  }

  /**
   * Handle incoming batch of layer operations
   */
  const handleIncomingBatch = (data: any) => {
    const { operations } = data
    
    for (const operation of operations) {
      // Skip operations from the current user
      if (operation.userId === options.userId) {
        continue
      }
      
      applyRemoteOperation(operation)
    }
  }

  /**
   * Handle operation confirmation from server
   */
  const handleOperationConfirmation = (data: any) => {
    const { operationIds } = data
    
    // Remove confirmed operations from pending list
    syncState.pendingOperations = syncState.pendingOperations.filter(
      op => !operationIds.includes(op.id)
    )
  }

  /**
   * Handle sync request from new client
   */
  const handleSyncRequest = (data: any) => {
    // Send current layer state to requesting client
    const currentState = {
      layers: editorState.layers.value,
      versions: Array.from(syncState.layerVersions.entries())
    }
    
    send({
      type: 'layer-sync-response',
      data: currentState,
      requestId: data.requestId,
      timestamp: Date.now()
    })
  }

  /**
   * Request full sync from server
   */
  const requestSync = () => {
    if (!isConnected.value) return
    
    send({
      type: 'layer-sync-request',
      requestId: `sync-${Date.now()}`,
      workspaceId: options.workspaceId,
      projectId: options.projectId,
      timestamp: Date.now()
    })
  }

  /**
   * Clear all conflicts
   */
  const clearConflicts = () => {
    syncState.conflicts = []
  }

  /**
   * Force sync all pending operations
   */
  const forceSyncPendingOperations = () => {
    if (currentBatch.value.length > 0) {
      sendBatchedOperations()
    }
  }

  /**
   * Cleanup layer sync
   */
  const cleanup = () => {
    if (batchTimer.value) {
      clearTimeout(batchTimer.value)
      batchTimer.value = null
    }
    
    // Force send any remaining operations
    forceSyncPendingOperations()
    
    syncState.isSync = false
    
    // Remove WebSocket handlers
    off('message', handleIncomingBatch)
  }

  // Initialize when WebSocket connects
  watch(isConnected, (connected) => {
    if (connected && !syncState.isSync) {
      initializeLayerSync()
      requestSync() // Request current state from server
    }
  })

  // Auto-initialize if already connected
  onMounted(() => {
    if (isConnected.value) {
      initializeLayerSync()
    }
  })

  // Cleanup on unmount
  onUnmounted(() => {
    cleanup()
  })

  return {
    // State
    syncState: readonly(syncState),
    hasPendingOperations,
    hasConflicts,
    syncLatency,
    
    // Layer operations with sync
    syncLayerCreate,
    syncLayerUpdate,
    syncLayerDelete,
    syncLayerReorder,
    
    // Sync management
    requestSync,
    forceSyncPendingOperations,
    clearConflicts,
    
    // Utilities
    initializeLayerSync,
    cleanup
  }
}