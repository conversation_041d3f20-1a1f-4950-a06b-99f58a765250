import type { LayerOperation } from './useLayerSync'
import type { EditorLayer } from './useEditorState'

export interface OTOperation extends LayerOperation {
  dependencies?: string[] // IDs of operations this depends on
  transforms?: TransformRule[] // Applied transformation rules
}

export interface TransformRule {
  type: 'position_adjust' | 'content_merge' | 'priority_override' | 'dependency_reorder'
  sourceOperationId: string
  targetOperationId: string
  transformation: any
}

export interface OperationContext {
  layerState: EditorLayer[]
  operationHistory: Map<string, OTOperation>
  conflictingOperations: OTOperation[]
}

/**
 * Operational Transformation (OT) system for collaborative layer editing
 * Implements conflict-free collaborative editing with automatic merge resolution
 */
export const useOperationalTransform = () => {
  
  /**
   * Transform an operation against a concurrent operation
   * Returns the transformed operation that can be safely applied
   */
  const transformOperation = (
    operation: OTOperation,
    concurrentOperation: OTOperation,
    context: OperationContext
  ): OTOperation => {
    
    // If operations don't conflict, return original
    if (!operationsConflict(operation, concurrentOperation)) {
      return operation
    }

    console.log(`Transforming ${operation.type} against concurrent ${concurrentOperation.type}`)

    // Apply transformation based on operation types
    switch (operation.type) {
      case 'create':
        return transformCreate(operation, concurrentOperation, context)
      case 'update':
        return transformUpdate(operation, concurrentOperation, context)
      case 'delete':
        return transformDelete(operation, concurrentOperation, context)
      case 'reorder':
        return transformReorder(operation, concurrentOperation, context)
      default:
        return operation
    }
  }

  /**
   * Check if two operations conflict with each other
   */
  const operationsConflict = (op1: OTOperation, op2: OTOperation): boolean => {
    // Same layer operations always conflict
    if (op1.layerId === op2.layerId && op1.layerId !== 'reorder-op') {
      return true
    }

    // Reorder operations conflict with any layer operations
    if (op1.type === 'reorder' || op2.type === 'reorder') {
      return true
    }

    // Operations within close timestamp range may conflict
    const timeDiff = Math.abs(op1.timestamp - op2.timestamp)
    return timeDiff < 500 // 500ms window for potential conflicts
  }

  /**
   * Transform create operations
   */
  const transformCreate = (
    operation: OTOperation,
    concurrent: OTOperation,
    context: OperationContext
  ): OTOperation => {
    
    switch (concurrent.type) {
      case 'create':
        // Concurrent creates - ensure unique layer IDs
        if (operation.layerId === concurrent.layerId) {
          return {
            ...operation,
            layerId: `${operation.layerId}-${operation.userId}`,
            data: {
              ...operation.data,
              layer: {
                ...operation.data.layer,
                id: `${operation.layerId}-${operation.userId}`,
                name: `${operation.data.layer.name} (${operation.userId})`
              }
            },
            transforms: [
              ...(operation.transforms || []),
              {
                type: 'content_merge',
                sourceOperationId: operation.id,
                targetOperationId: concurrent.id,
                transformation: { uniqueId: true }
              }
            ]
          }
        }
        break
        
      case 'delete':
        // If concurrent delete affects our layer, abort create
        if (concurrent.layerId === operation.layerId) {
          return {
            ...operation,
            type: 'update', // Convert to update of existing layer
            transforms: [
              ...(operation.transforms || []),
              {
                type: 'priority_override',
                sourceOperationId: operation.id,
                targetOperationId: concurrent.id,
                transformation: { convertToUpdate: true }
              }
            ]
          }
        }
        break
        
      case 'reorder':
        // Adjust position in reorder if our layer affects the order
        const reorderData = concurrent.data
        const newLayer = operation.data.layer
        
        return {
          ...operation,
          data: {
            ...operation.data,
            layer: {
              ...newLayer,
              // Insert at appropriate position considering reorder
              insertIndex: calculateInsertPosition(newLayer, reorderData, context)
            }
          },
          transforms: [
            ...(operation.transforms || []),
            {
              type: 'position_adjust',
              sourceOperationId: operation.id,
              targetOperationId: concurrent.id,
              transformation: { adjustedPosition: true }
            }
          ]
        }
    }

    return operation
  }

  /**
   * Transform update operations
   */
  const transformUpdate = (
    operation: OTOperation,
    concurrent: OTOperation,
    context: OperationContext
  ): OTOperation => {
    
    switch (concurrent.type) {
      case 'update':
        if (operation.layerId === concurrent.layerId) {
          // Merge non-conflicting updates
          const mergedUpdates = mergeUpdates(
            operation.data.updates,
            concurrent.data.updates,
            operation.timestamp > concurrent.timestamp
          )
          
          return {
            ...operation,
            data: {
              ...operation.data,
              updates: mergedUpdates
            },
            transforms: [
              ...(operation.transforms || []),
              {
                type: 'content_merge',
                sourceOperationId: operation.id,
                targetOperationId: concurrent.id,
                transformation: { mergedProperties: Object.keys(mergedUpdates) }
              }
            ]
          }
        }
        break
        
      case 'delete':
        if (operation.layerId === concurrent.layerId) {
          // Layer was deleted, convert update to create
          const currentLayer = context.layerState.find(l => l.id === operation.layerId)
          if (currentLayer) {
            return {
              ...operation,
              type: 'create',
              data: {
                layer: {
                  ...currentLayer,
                  ...operation.data.updates
                }
              },
              transforms: [
                ...(operation.transforms || []),
                {
                  type: 'priority_override',
                  sourceOperationId: operation.id,
                  targetOperationId: concurrent.id,
                  transformation: { convertToCreate: true }
                }
              ]
            }
          }
        }
        break
        
      case 'reorder':
        // Update operation not affected by reorder
        return operation
    }

    return operation
  }

  /**
   * Transform delete operations
   */
  const transformDelete = (
    operation: OTOperation,
    concurrent: OTOperation,
    context: OperationContext
  ): OTOperation => {
    
    switch (concurrent.type) {
      case 'create':
        if (operation.layerId === concurrent.layerId) {
          // Attempting to delete a layer that's being created
          // Priority to create operation
          return {
            ...operation,
            type: 'update', // Convert to update
            data: {
              updates: { deleted: true } // Mark as deleted instead
            },
            transforms: [
              ...(operation.transforms || []),
              {
                type: 'priority_override',
                sourceOperationId: operation.id,
                targetOperationId: concurrent.id,
                transformation: { convertToUpdate: true, markDeleted: true }
              }
            ]
          }
        }
        break
        
      case 'update':
        if (operation.layerId === concurrent.layerId) {
          // Layer is being updated while being deleted
          if (operation.timestamp > concurrent.timestamp) {
            // Delete wins - proceed with deletion
            return operation
          } else {
            // Update wins - convert delete to update
            return {
              ...operation,
              type: 'update',
              data: {
                updates: { deleted: true }
              },
              transforms: [
                ...(operation.transforms || []),
                {
                  type: 'priority_override',
                  sourceOperationId: operation.id,
                  targetOperationId: concurrent.id,
                  transformation: { convertToUpdate: true, markDeleted: true }
                }
              ]
            }
          }
        }
        break
        
      case 'delete':
        if (operation.layerId === concurrent.layerId) {
          // Concurrent deletes - use timestamp to determine winner
          if (operation.timestamp <= concurrent.timestamp) {
            // This delete is redundant
            return {
              ...operation,
              type: 'update', // Convert to no-op update
              data: { updates: {} },
              transforms: [
                ...(operation.transforms || []),
                {
                  type: 'priority_override',
                  sourceOperationId: operation.id,
                  targetOperationId: concurrent.id,
                  transformation: { redundantOperation: true }
                }
              ]
            }
          }
        }
        break
        
      case 'reorder':
        // Remove layer from reorder indices
        const reorderData = concurrent.data
        return {
          ...operation,
          data: {
            ...operation.data,
            adjustedReorderIndices: adjustReorderForDeletion(
              reorderData,
              operation.layerId,
              context
            )
          },
          transforms: [
            ...(operation.transforms || []),
            {
              type: 'dependency_reorder',
              sourceOperationId: operation.id,
              targetOperationId: concurrent.id,
              transformation: { adjustedForDeletion: true }
            }
          ]
        }
    }

    return operation
  }

  /**
   * Transform reorder operations
   */
  const transformReorder = (
    operation: OTOperation,
    concurrent: OTOperation,
    context: OperationContext
  ): OTOperation => {
    
    switch (concurrent.type) {
      case 'create':
        // New layer created during reorder - adjust indices
        const newLayerIndex = calculateInsertPosition(
          concurrent.data.layer,
          operation.data,
          context
        )
        
        return {
          ...operation,
          data: {
            ...operation.data,
            adjustedForNewLayer: {
              layerId: concurrent.layerId,
              insertIndex: newLayerIndex
            }
          },
          transforms: [
            ...(operation.transforms || []),
            {
              type: 'position_adjust',
              sourceOperationId: operation.id,
              targetOperationId: concurrent.id,
              transformation: { adjustedForCreate: true }
            }
          ]
        }
        
      case 'delete':
        // Layer deleted during reorder - adjust indices
        return {
          ...operation,
          data: adjustReorderForDeletion(operation.data, concurrent.layerId, context),
          transforms: [
            ...(operation.transforms || []),
            {
              type: 'position_adjust',
              sourceOperationId: operation.id,
              targetOperationId: concurrent.id,
              transformation: { adjustedForDeletion: true }
            }
          ]
        }
        
      case 'reorder':
        // Concurrent reorders - merge them
        const mergedReorder = mergeReorderOperations(
          operation.data,
          concurrent.data,
          operation.timestamp > concurrent.timestamp
        )
        
        return {
          ...operation,
          data: mergedReorder,
          transforms: [
            ...(operation.transforms || []),
            {
              type: 'content_merge',
              sourceOperationId: operation.id,
              targetOperationId: concurrent.id,
              transformation: { mergedReorder: true }
            }
          ]
        }
    }

    return operation
  }

  /**
   * Merge two update operations on the same layer
   */
  const mergeUpdates = (
    updates1: Partial<EditorLayer>,
    updates2: Partial<EditorLayer>,
    prioritizeFirst: boolean
  ): Partial<EditorLayer> => {
    const merged: Partial<EditorLayer> = {}

    const allKeys = new Set([
      ...Object.keys(updates1),
      ...Object.keys(updates2)
    ])

    for (const key of allKeys) {
      const key1 = updates1[key as keyof EditorLayer]
      const key2 = updates2[key as keyof EditorLayer]

      if (key1 !== undefined && key2 !== undefined) {
        // Both operations update this property - resolve conflict
        merged[key as keyof EditorLayer] = prioritizeFirst ? key1 : key2
      } else {
        // Only one operation updates this property
        merged[key as keyof EditorLayer] = key1 !== undefined ? key1 : key2
      }
    }

    return merged
  }

  /**
   * Calculate insert position for new layer considering reorder
   */
  const calculateInsertPosition = (
    newLayer: EditorLayer,
    reorderData: any,
    context: OperationContext
  ): number => {
    // Simple strategy: insert at the end unless specified
    return context.layerState.length
  }

  /**
   * Adjust reorder operation for layer deletion
   */
  const adjustReorderForDeletion = (
    reorderData: any,
    deletedLayerId: string,
    context: OperationContext
  ): any => {
    const { fromIndex, toIndex, layerOrder } = reorderData

    // Remove deleted layer from order
    const adjustedOrder = layerOrder.filter((id: string) => id !== deletedLayerId)
    
    // Adjust indices
    let adjustedFromIndex = fromIndex
    let adjustedToIndex = toIndex

    const deletedIndex = layerOrder.indexOf(deletedLayerId)
    if (deletedIndex < fromIndex) adjustedFromIndex--
    if (deletedIndex < toIndex) adjustedToIndex--

    return {
      ...reorderData,
      fromIndex: adjustedFromIndex,
      toIndex: adjustedToIndex,
      layerOrder: adjustedOrder
    }
  }

  /**
   * Merge two reorder operations
   */
  const mergeReorderOperations = (
    reorder1: any,
    reorder2: any,
    prioritizeFirst: boolean
  ): any => {
    // For now, use the operation with higher priority
    // In a full implementation, you'd merge the operations more intelligently
    return prioritizeFirst ? reorder1 : reorder2
  }

  /**
   * Apply a sequence of operations with conflict resolution
   */
  const applyOperationsWithOT = (
    operations: OTOperation[],
    context: OperationContext
  ): OTOperation[] => {
    const transformedOperations: OTOperation[] = []
    
    // Sort operations by timestamp
    const sortedOps = [...operations].sort((a, b) => a.timestamp - b.timestamp)
    
    for (let i = 0; i < sortedOps.length; i++) {
      let currentOp = sortedOps[i]
      
      // Transform against all previous operations
      for (let j = 0; j < i; j++) {
        const prevOp = transformedOperations[j]
        if (operationsConflict(currentOp, prevOp)) {
          currentOp = transformOperation(currentOp, prevOp, context)
        }
      }
      
      transformedOperations.push(currentOp)
    }
    
    return transformedOperations
  }

  /**
   * Validate that an operation can be safely applied
   */
  const validateOperation = (
    operation: OTOperation,
    context: OperationContext
  ): boolean => {
    switch (operation.type) {
      case 'create':
        // Layer shouldn't already exist
        return !context.layerState.find(l => l.id === operation.layerId)
        
      case 'update':
      case 'delete':
        // Layer should exist
        return !!context.layerState.find(l => l.id === operation.layerId)
        
      case 'reorder':
        // All layers in reorder should exist
        const layerIds = new Set(context.layerState.map(l => l.id))
        return operation.data.layerOrder.every((id: string) => layerIds.has(id))
        
      default:
        return false
    }
  }

  return {
    transformOperation,
    operationsConflict,
    applyOperationsWithOT,
    validateOperation,
    mergeUpdates
  }
}