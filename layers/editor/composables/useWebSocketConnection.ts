import { ref, reactive, onMounted, onUnmounted, computed, readonly } from 'vue'
import { useRuntimeConfig } from '#app'
import { useFirebase } from '../../core/composables/firebase'

export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'reconnecting' | 'error'

export interface WebSocketConnectionOptions {
  workspaceId: string
  projectId?: string
  userId?: string
  autoConnect?: boolean
  reconnectInterval?: number
  maxReconnectAttempts?: number
}

export interface WebSocketError {
  code: string
  message: string
  timestamp: Date
  context?: Record<string, any>
}

export interface ConnectionInfo {
  status: ConnectionStatus
  url: string | null
  lastConnected: Date | null
  reconnectAttempts: number
  error: WebSocketError | null
}

/**
 * WebSocket connection management for real-time collaboration
 * Implements Story 1.1: WebSocket Connection Management
 */
export const useWebSocketConnection = (options: WebSocketConnectionOptions) => {
  const { auth } = useFirebase()
  const runtimeConfig = useRuntimeConfig()
  
  // Connection state
  const connection = ref<WebSocket | null>(null)
  const connectionInfo = reactive<ConnectionInfo>({
    status: 'disconnected',
    url: null,
    lastConnected: null,
    reconnectAttempts: 0,
    error: null
  })

  // Configuration
  const config = {
    autoConnect: options.autoConnect ?? true,
    reconnectInterval: options.reconnectInterval ?? 3000,
    maxReconnectAttempts: options.maxReconnectAttempts ?? 10
  }

  // Connection pool for workspace-level sessions
  const connectionPool = new Map<string, WebSocket>()
  
  // Reconnection timer
  let reconnectTimer: NodeJS.Timeout | null = null
  let heartbeatTimer: NodeJS.Timeout | null = null

  // Computed properties
  const isConnected = computed(() => connectionInfo.status === 'connected')
  const isConnecting = computed(() => connectionInfo.status === 'connecting' || connectionInfo.status === 'reconnecting')
  const canReconnect = computed(() => connectionInfo.reconnectAttempts < config.maxReconnectAttempts)

  /**
   * Validate WebSocket URL configuration
   */
  const validateWebSocketUrl = (url: string): boolean => {
    try {
      const parsedUrl = new URL(url)
      return parsedUrl.protocol === 'ws:' || parsedUrl.protocol === 'wss:'
    } catch {
      return false
    }
  }

  /**
   * Generate WebSocket URL with authentication and workspace context
   */
  const generateWebSocketUrl = async (): Promise<string> => {
    const currentUser = auth.currentUser
    if (!currentUser) {
      throw new Error('User not authenticated')
    }

    const token = await currentUser.getIdToken()
    const baseUrl = runtimeConfig.public.websocketUrl || 'ws://localhost:3001'
    
    // Validate WebSocket URL configuration
    if (!validateWebSocketUrl(baseUrl)) {
      console.warn('Invalid WebSocket URL configuration:', baseUrl)
      throw new Error('Invalid WebSocket URL configuration')
    }

    // Warn if using default development URL in production
    if (baseUrl === 'ws://localhost:3001' && process.env.NODE_ENV === 'production') {
      console.warn('Using default WebSocket URL in production. Please configure NUXT_PUBLIC_WEBSOCKET_URL')
    }
    
    const params = new URLSearchParams({
      token,
      workspaceId: options.workspaceId,
      ...(options.projectId && { projectId: options.projectId }),
      ...(options.userId && { userId: options.userId })
    })

    return `${baseUrl}/editor?${params.toString()}`
  }

  /**
   * Establish WebSocket connection with exponential backoff
   */
  const connect = async (): Promise<void> => {
    if (connection.value?.readyState === WebSocket.CONNECTING || 
        connection.value?.readyState === WebSocket.OPEN) {
      return
    }

    try {
      connectionInfo.status = connectionInfo.reconnectAttempts > 0 ? 'reconnecting' : 'connecting'
      connectionInfo.error = null

      const wsUrl = await generateWebSocketUrl()
      connectionInfo.url = wsUrl

      const ws = new WebSocket(wsUrl)
      
      // Connection opened
      ws.onopen = (event) => {
        console.log('WebSocket connected:', event)
        connectionInfo.status = 'connected'
        connectionInfo.lastConnected = new Date()
        connectionInfo.reconnectAttempts = 0
        connectionInfo.error = null
        
        // Add to connection pool
        connectionPool.set(options.workspaceId, ws)
        
        // Start heartbeat
        startHeartbeat()
        
        // Emit connection event
        emitConnectionEvent('connected', { event })
      }

      // Message received
      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          handleMessage(data)
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }

      // Connection closed
      ws.onclose = (event) => {
        console.log('WebSocket closed:', event)
        connectionInfo.status = 'disconnected'
        connectionPool.delete(options.workspaceId)
        stopHeartbeat()
        
        // Attempt reconnection if not intentional close
        if (!event.wasClean && canReconnect.value) {
          scheduleReconnect()
        }
        
        emitConnectionEvent('disconnected', { event })
      }

      // Connection error
      ws.onerror = (event) => {
        console.error('WebSocket error:', event)
        connectionInfo.status = 'error'
        connectionInfo.error = 'Connection error occurred'
        
        emitConnectionEvent('error', { event })
      }

      connection.value = ws

    } catch (error: any) {
      console.error('Failed to connect WebSocket:', error)
      connectionInfo.status = 'error'
      connectionInfo.error = error.message || 'Failed to establish connection'
      
      if (canReconnect.value) {
        scheduleReconnect()
      }
    }
  }

  /**
   * Disconnect WebSocket connection
   */
  const disconnect = (): void => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }

    stopHeartbeat()

    if (connection.value) {
      connection.value.close(1000, 'Intentional disconnect')
      connection.value = null
    }

    connectionInfo.status = 'disconnected'
    connectionPool.delete(options.workspaceId)
  }

  /**
   * Schedule reconnection with exponential backoff
   */
  const scheduleReconnect = (): void => {
    if (reconnectTimer || !canReconnect.value) return

    const delay = Math.min(
      config.reconnectInterval * Math.pow(2, connectionInfo.reconnectAttempts),
      30000 // Max 30 seconds
    )

    connectionInfo.reconnectAttempts++

    reconnectTimer = setTimeout(() => {
      reconnectTimer = null
      connect()
    }, delay)
  }

  /**
   * Start heartbeat to keep connection alive
   */
  const startHeartbeat = (): void => {
    heartbeatTimer = setInterval(() => {
      if (connection.value?.readyState === WebSocket.OPEN) {
        send({ type: 'ping', timestamp: Date.now() })
      }
    }, 60000) // 60 seconds - optimized for better performance
  }

  /**
   * Stop heartbeat timer
   */
  const stopHeartbeat = (): void => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
  }

  /**
   * Send message through WebSocket
   */
  const send = (data: any): boolean => {
    if (connection.value?.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket not connected, message not sent:', data)
      return false
    }

    try {
      connection.value.send(JSON.stringify(data))
      return true
    } catch (error) {
      console.error('Error sending WebSocket message:', error)
      return false
    }
  }

  /**
   * Handle incoming WebSocket messages
   */
  const handleMessage = (data: any): void => {
    switch (data.type) {
      case 'pong':
        // Heartbeat response - connection is alive
        break
      
      case 'user-joined':
        emitConnectionEvent('user-joined', data)
        break
        
      case 'user-left':
        emitConnectionEvent('user-left', data)
        break
        
      case 'layer-update':
        emitConnectionEvent('layer-update', data)
        break
        
      default:
        emitConnectionEvent('message', data)
    }
  }

  // Event emitter for connection events
  const connectionEvents = new EventTarget()

  const emitConnectionEvent = (type: string, detail: any): void => {
    connectionEvents.dispatchEvent(new CustomEvent(type, { detail }))
  }

  const on = (type: string, handler: EventListener): void => {
    connectionEvents.addEventListener(type, handler)
  }

  const off = (type: string, handler: EventListener): void => {
    connectionEvents.removeEventListener(type, handler)
  }

  /**
   * Get connection from pool for workspace
   */
  const getWorkspaceConnection = (workspaceId: string): WebSocket | null => {
    return connectionPool.get(workspaceId) || null
  }

  /**
   * Manual reconnect with reset attempt counter
   */
  const reconnect = (): void => {
    connectionInfo.reconnectAttempts = 0
    disconnect()
    setTimeout(() => connect(), 1000)
  }

  // Auto-connect on mount if enabled
  onMounted(() => {
    if (config.autoConnect) {
      connect()
    }
  })

  // Cleanup on unmount
  onUnmounted(() => {
    disconnect()
  })

  return {
    // State
    connectionInfo: readonly(connectionInfo),
    isConnected,
    isConnecting,
    canReconnect,
    
    // Methods
    connect,
    disconnect,
    reconnect,
    send,
    
    // Events
    on,
    off,
    
    // Connection pool
    getWorkspaceConnection,
    
    // Utilities
    generateWebSocketUrl
  }
}