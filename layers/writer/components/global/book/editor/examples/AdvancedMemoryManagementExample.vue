<template>
  <div class="advanced-memory-demo p-6 space-y-6">
    <div class="text-center">
      <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
        Advanced Memory Management Demo
      </h2>
      <p class="text-gray-600 dark:text-gray-400">
        Intelligent memory optimization with AI-driven cleanup and predictive analytics
      </p>
    </div>

    <!-- Memory Analytics Dashboard -->
    <MemoryAnalyticsDashboard 
      :project-type="selectedProjectType"
      :auto-refresh="true"
    />

    <!-- Demo Controls -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
      <h3 class="text-lg font-semibold mb-4">Demo Controls</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <!-- Project Type Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Project Type
          </label>
          <select 
            v-model="selectedProjectType"
            @change="handleProjectTypeChange"
            class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
          >
            <option value="small">Small Project (256MB)</option>
            <option value="medium">Medium Project (512MB)</option>
            <option value="large">Large Project (1GB)</option>
            <option value="enterprise">Enterprise (2GB)</option>
          </select>
        </div>

        <!-- Memory Pressure Simulation -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Memory Pressure
          </label>
          <select 
            v-model="memoryPressure"
            @change="simulateMemoryPressure"
            class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
          >
            <option value="low">Low (Normal)</option>
            <option value="medium">Medium (Warning)</option>
            <option value="high">High (Critical)</option>
            <option value="extreme">Extreme (Maximum)</option>
          </select>
        </div>

        <!-- Cleanup Strategy -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Cleanup Aggressiveness
          </label>
          <input
            v-model.number="cleanupAggressiveness"
            type="range"
            min="0.1"
            max="1.0"
            step="0.1"
            class="w-full"
          />
          <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {{ Math.round(cleanupAggressiveness * 100) }}% aggressive
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-wrap gap-3">
        <button
          @click="createTestLayers"
          :disabled="isLoading"
          class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-blue-400 transition-colors"
        >
          Create Test Layers ({{ layersToCreate }})
        </button>

        <button
          @click="simulateUsagePatterns"
          :disabled="isLoading"
          class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-green-400 transition-colors"
        >
          Simulate Usage Patterns
        </button>

        <button
          @click="triggerIntelligentCleanup"
          :disabled="isLoading"
          class="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 disabled:bg-orange-400 transition-colors"
        >
          Intelligent Cleanup
        </button>

        <button
          @click="createMemoryLeak"
          :disabled="isLoading"
          class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:bg-red-400 transition-colors"
        >
          Simulate Memory Leak
        </button>

        <button
          @click="resetDemo"
          :disabled="isLoading"
          class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 disabled:bg-gray-400 transition-colors"
        >
          Reset Demo
        </button>
      </div>
    </div>

    <!-- Demo Status -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
      <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Demo Status</h4>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div>
          <span class="text-gray-600 dark:text-gray-400">Active Layers:</span>
          <span class="font-medium ml-2">{{ currentLayerCount }}</span>
        </div>
        <div>
          <span class="text-gray-600 dark:text-gray-400">Memory Usage:</span>
          <span class="font-medium ml-2">{{ formatMemory(currentMemoryUsage) }}</span>
        </div>
        <div>
          <span class="text-gray-600 dark:text-gray-400">Efficiency:</span>
          <span class="font-medium ml-2">{{ Math.round(currentEfficiency) }}%</span>
        </div>
        <div>
          <span class="text-gray-600 dark:text-gray-400">Health Score:</span>
          <span class="font-medium ml-2">{{ Math.round(currentHealth) }}%</span>
        </div>
      </div>
    </div>

    <!-- Activity Log -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold">Activity Log</h3>
        <button
          @click="clearLog"
          class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          Clear
        </button>
      </div>
      
      <div class="max-h-64 overflow-y-auto space-y-2">
        <div
          v-for="(entry, index) in activityLog"
          :key="index"
          :class="[
            'p-3 rounded-lg text-sm',
            entry.type === 'success' ? 'bg-green-50 text-green-800 dark:bg-green-900/20 dark:text-green-200' :
            entry.type === 'warning' ? 'bg-yellow-50 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-200' :
            entry.type === 'error' ? 'bg-red-50 text-red-800 dark:bg-red-900/20 dark:text-red-200' :
            'bg-gray-50 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
          ]"
        >
          <div class="flex items-start justify-between">
            <span>{{ entry.message }}</span>
            <span class="text-xs opacity-75">{{ formatTime(entry.timestamp) }}</span>
          </div>
          <div v-if="entry.details" class="mt-1 text-xs opacity-75">
            {{ entry.details }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAdvancedMemoryManager, MEMORY_BUDGETS } from '../../../../../core/composables/useAdvancedMemoryManager'
import MemoryAnalyticsDashboard from '../../../../../core/components/memory/MemoryAnalyticsDashboard.vue'

interface ActivityLogEntry {
  timestamp: number
  type: 'info' | 'success' | 'warning' | 'error'
  message: string
  details?: string
}

// Demo state
const selectedProjectType = ref<keyof typeof MEMORY_BUDGETS>('medium')
const memoryPressure = ref<'low' | 'medium' | 'high' | 'extreme'>('low')
const cleanupAggressiveness = ref(0.7)
const layersToCreate = ref(50)
const isLoading = ref(false)
const activityLog = ref<ActivityLogEntry[]>([])

// Initialize advanced memory manager
const memoryManager = useAdvancedMemoryManager(
  {
    thresholds: MEMORY_BUDGETS[selectedProjectType.value],
    autoCleanup: false // Manual control for demo
  },
  {
    enablePredictiveCleanup: true,
    enableUsagePatternAnalysis: true,
    enableMemoryLeakDetection: true,
    enableSmartPrefetching: true,
    cleanupAggressiveness: cleanupAggressiveness.value
  }
)

// Computed properties
const currentLayerCount = computed(() => memoryManager.layerRegistry.value.size)
const currentMemoryUsage = computed(() => memoryManager.memoryStats.value.usedJSHeapSize)
const currentEfficiency = computed(() => memoryManager.memoryEfficiency.value)
const currentHealth = computed(() => memoryManager.systemHealth.value)

// Demo layer counter for unique IDs
let layerCounter = 0

// Methods
function addLogEntry(type: ActivityLogEntry['type'], message: string, details?: string) {
  activityLog.value.unshift({
    timestamp: Date.now(),
    type,
    message,
    details
  })
  
  // Keep log size manageable
  if (activityLog.value.length > 50) {
    activityLog.value = activityLog.value.slice(0, 50)
  }
}

function handleProjectTypeChange() {
  memoryManager.setMemoryBudget(selectedProjectType.value)
  addLogEntry('info', `Switched to ${selectedProjectType.value} project budget`, 
    `Max memory: ${formatMemory(MEMORY_BUDGETS[selectedProjectType.value].maxMemoryMB)}`)
}

function simulateMemoryPressure() {
  const pressureMap = {
    low: 0.3,
    medium: 0.6,
    high: 0.85,
    extreme: 0.95
  }
  
  const budget = MEMORY_BUDGETS[selectedProjectType.value]
  const targetUsage = budget.maxMemoryMB * pressureMap[memoryPressure.value]
  
  // Mock the memory usage (in a real scenario, this would be actual memory allocation)
  if ('memory' in performance) {
    const mockMemory = (performance as any).memory
    mockMemory.usedJSHeapSize = targetUsage * 1024 * 1024
  }
  
  addLogEntry('warning', `Simulated ${memoryPressure.value} memory pressure`, 
    `Target usage: ${formatMemory(targetUsage)}`)
}

async function createTestLayers() {
  isLoading.value = true
  
  try {
    const promises: Promise<void>[] = []
    
    for (let i = 0; i < layersToCreate.value; i++) {
      const layerId = `demo-layer-${++layerCounter}`
      const layerType = ['image', 'canvas', 'data', 'element'][Math.floor(Math.random() * 4)] as any
      const priority = ['high', 'medium', 'low'][Math.floor(Math.random() * 3)] as any
      const isVisible = Math.random() > 0.7 // 30% visible
      
      // Create mock data based on type
      let data: any
      switch (layerType) {
        case 'image':
          data = new ImageData(
            Math.floor(Math.random() * 500) + 100,
            Math.floor(Math.random() * 500) + 100
          )
          break
        case 'canvas':
          data = {
            width: Math.floor(Math.random() * 800) + 200,
            height: Math.floor(Math.random() * 600) + 200,
            context: 'mock-canvas-data'
          }
          break
        default:
          data = {
            content: `Mock layer content ${i}`,
            metadata: { size: Math.random() * 1000, created: Date.now() }
          }
      }
      
      promises.push(
        memoryManager.registerLayer(layerId, layerType, data, {
          priority,
          isVisible,
          enableCompression: !isVisible
        })
      )
    }
    
    await Promise.all(promises)
    
    addLogEntry('success', `Created ${layersToCreate.value} test layers`, 
      `Total layers: ${currentLayerCount.value}`)
  } catch (error) {
    addLogEntry('error', 'Failed to create test layers', (error as Error).message)
  } finally {
    isLoading.value = false
  }
}

async function simulateUsagePatterns() {
  isLoading.value = true
  
  try {
    const layerIds = Array.from(memoryManager.layerRegistry.value.keys())
    const accessPromises: Promise<any>[] = []
    
    // Simulate realistic usage patterns
    for (const layerId of layerIds) {
      const accessCount = Math.floor(Math.random() * 20) + 1
      
      for (let i = 0; i < accessCount; i++) {
        accessPromises.push(
          new Promise(resolve => {
            setTimeout(async () => {
              try {
                await memoryManager.accessLayer(layerId)
                resolve(null)
              } catch (error) {
                resolve(null) // Continue even if access fails
              }
            }, Math.random() * 1000) // Random delays up to 1 second
          })
        )
      }
    }
    
    await Promise.all(accessPromises)
    
    addLogEntry('success', 'Simulated realistic usage patterns', 
      `Accessed ${layerIds.length} layers with varying frequencies`)
  } catch (error) {
    addLogEntry('error', 'Failed to simulate usage patterns', (error as Error).message)
  } finally {
    isLoading.value = false
  }
}

async function triggerIntelligentCleanup() {
  isLoading.value = true
  
  try {
    const cleanupResult = await memoryManager.performIntelligentCleanup()
    
    addLogEntry('success', 'Intelligent cleanup completed', 
      `Removed ${cleanupResult.layersRemoved} layers, freed ${formatMemory(cleanupResult.memoryFreed)}`)
    
    if (cleanupResult.recommendations.length > 0) {
      addLogEntry('info', `Generated ${cleanupResult.recommendations.length} optimization recommendations`)
    }
  } catch (error) {
    addLogEntry('error', 'Cleanup failed', (error as Error).message)
  } finally {
    isLoading.value = false
  }
}

async function createMemoryLeak() {
  isLoading.value = true
  
  try {
    // Create layers that should be cleaned up but won't be
    const leakLayerIds: string[] = []
    
    for (let i = 0; i < 10; i++) {
      const layerId = `leak-layer-${++layerCounter}`
      
      await memoryManager.registerLayer(layerId, 'data', {
        content: `Leaked data ${i}`,
        size: Math.random() * 5000 + 1000 // Large data
      }, {
        priority: 'low',
        isVisible: false
      })
      
      // Make them appear old
      const layer = memoryManager.layerRegistry.value.get(layerId)
      if (layer) {
        layer.lastAccessed = Date.now() - 30 * 60 * 1000 // 30 minutes ago
      }
      
      leakLayerIds.push(layerId)
    }
    
    addLogEntry('warning', 'Simulated memory leak', 
      `Created ${leakLayerIds.length} old, unused layers that should be cleaned`)
    
    // Wait a bit, then check for leaks
    setTimeout(() => {
      const detectedLeaks = memoryManager.detectMemoryLeaks()
      if (detectedLeaks.length > 0) {
        addLogEntry('error', `Detected ${detectedLeaks.length} memory leaks`)
      }
    }, 1000)
  } catch (error) {
    addLogEntry('error', 'Failed to simulate memory leak', (error as Error).message)
  } finally {
    isLoading.value = false
  }
}

async function resetDemo() {
  isLoading.value = true
  
  try {
    // Clear all demo layers
    const allLayerIds = Array.from(memoryManager.layerRegistry.value.keys())
    allLayerIds.forEach(layerId => {
      memoryManager.unregisterLayer(layerId)
    })
    
    // Reset counters
    layerCounter = 0
    
    // Reset memory pressure
    memoryPressure.value = 'low'
    simulateMemoryPressure()
    
    addLogEntry('info', 'Demo reset completed', 
      `Removed all ${allLayerIds.length} demo layers`)
  } catch (error) {
    addLogEntry('error', 'Reset failed', (error as Error).message)
  } finally {
    isLoading.value = false
  }
}

function clearLog() {
  activityLog.value = []
}

function formatMemory(mb: number): string {
  if (mb < 1) {
    return `${Math.round(mb * 1024)} KB`
  } else if (mb < 1024) {
    return `${Math.round(mb)} MB`
  } else {
    return `${(mb / 1024).toFixed(1)} GB`
  }
}

function formatTime(timestamp: number): string {
  return new Date(timestamp).toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit',
    second: '2-digit'
  })
}

// Initialize demo
onMounted(() => {
  addLogEntry('info', 'Advanced Memory Management Demo initialized', 
    `Project type: ${selectedProjectType.value}`)
})

onUnmounted(() => {
  // Cleanup demo layers
  resetDemo()
})
</script>

<style scoped>
.advanced-memory-demo {
  font-family: system-ui, -apple-system, sans-serif;
}

/* Custom scrollbar for activity log */
.max-h-64::-webkit-scrollbar {
  width: 6px;
}

.max-h-64::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.max-h-64::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.max-h-64::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Dark mode scrollbar */
.dark .max-h-64::-webkit-scrollbar-track {
  background: #374151;
}

.dark .max-h-64::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark .max-h-64::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>