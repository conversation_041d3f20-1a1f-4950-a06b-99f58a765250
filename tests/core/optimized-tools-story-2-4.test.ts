/**
 * Test Suite for Story 2.4: Optimized Tool Response Times
 * 
 * Acceptance Criteria:
 * - [ ] Basic tools respond within 100ms
 * - [ ] Complex tools complete within 2 seconds
 * - [ ] Progress bars for operations > 500ms
 * - [ ] Ability to cancel long-running operations
 * - [ ] Tool operations don't block other interactions
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'

// Mock onUnmounted to avoid Vue warnings in tests
vi.mock('vue', async () => {
  const actual = await vi.importActual('vue')
  return {
    ...actual,
    onUnmounted: vi.fn()
  }
})

// Mock the canvas worker pool
vi.mock('../../layers/writer/composables/useCanvasWorkerPool', () => ({
  useCanvasWorkerPool: () => ({
    workerPool: {
      execute: vi.fn().mockResolvedValue({ success: true, data: 'mocked-result' }),
      getStats: vi.fn().mockReturnValue({ activeWorkers: 0, queuedTasks: 0 })
    }
  })
}))

import { useOptimizedTools } from '../../layers/core/composables/useOptimizedTools'

// Mock requestIdleCallback for testing
const mockRequestIdleCallback = vi.fn((callback, options) => {
  const deadline = {
    timeRemaining: () => 16, // Mock 16ms remaining
    didTimeout: false
  }
  // Execute immediately in tests
  try {
    callback(deadline)
  } catch (error) {
    console.warn('RequestIdleCallback mock error:', error)
  }
  return 1
})

const mockCancelIdleCallback = vi.fn()

// Set up global mocks properly for both browser and node environments
if (typeof global !== 'undefined') {
  // Node environment
  Object.defineProperty(global, 'requestIdleCallback', {
    value: mockRequestIdleCallback,
    writable: true,
    configurable: true
  })
  
  Object.defineProperty(global, 'cancelIdleCallback', {
    value: mockCancelIdleCallback,
    writable: true,
    configurable: true
  })
}

if (typeof window !== 'undefined') {
  // Browser environment
  Object.defineProperty(window, 'requestIdleCallback', {
    value: mockRequestIdleCallback,
    writable: true,
    configurable: true
  })
  
  Object.defineProperty(window, 'cancelIdleCallback', {
    value: mockCancelIdleCallback,
    writable: true,
    configurable: true
  })
}

describe('Story 2.4: Optimized Tool Response Times', () => {
  let optimizedTools: ReturnType<typeof useOptimizedTools>

  beforeEach(() => {
    optimizedTools = useOptimizedTools()
    vi.clearAllTimers()
    vi.useFakeTimers()
  })

  afterEach(() => {
    optimizedTools.cancelAllOperations()
    optimizedTools.clearAllCaches()
    vi.useRealTimers()
  })

  describe('Acceptance Criteria 1: Basic tools respond within 100ms', () => {
    it('should execute basic tools within 100ms', async () => {
      const startTime = Date.now()
      
      const promise = optimizedTools.executeTool('image-edit', 'resize', { width: 100, height: 100 })
      
      // Fast-forward time by 100ms
      vi.advanceTimersByTime(100)
      
      const result = await promise
      const responseTime = result.responseTime

      expect(result.success).toBe(true)
      expect(responseTime).toBeLessThanOrEqual(100)
      expect(result.data.optimized).toBe(true)
    })

    it('should use micro-cache for repeated basic tool operations', async () => {
      const parameters = { text: 'Hello World', format: 'uppercase' }
      
      // First execution
      const promise1 = optimizedTools.executeTool('text-transform', 'uppercase', parameters)
      vi.advanceTimersByTime(50)
      const result1 = await promise1

      // Second execution (should hit micro-cache)
      const startTime = Date.now()
      const promise2 = optimizedTools.executeTool('text-transform', 'uppercase', parameters)
      vi.advanceTimersByTime(5) // Minimal time for cache hit
      const result2 = await promise2

      expect(result1.success).toBe(true)
      expect(result2.success).toBe(true)
      expect(result2.fromCache).toBe(true)
      expect(result2.responseTime).toBeLessThan(10) // Cache hit should be very fast
    })

    it('should register basic tools with correct configuration', () => {
      const configs = optimizedTools.toolConfigs.value
      const basicTools = configs.filter(config => config.type === 'basic')
      
      basicTools.forEach(tool => {
        expect(tool.expectedResponseTime).toBeLessThanOrEqual(100)
        expect(tool.showProgressAfter).toBeGreaterThan(500) // No progress for basic ops
      })
    })
  })

  describe('Acceptance Criteria 2: Complex tools complete within 2 seconds', () => {
    it('should execute complex tools within 2 seconds', async () => {
      const promise = optimizedTools.executeTool('firecrawl', 'scrape', { url: 'https://example.com' })
      
      // Fast-forward time by 2 seconds
      vi.advanceTimersByTime(2000)
      
      const result = await promise

      expect(result.success).toBe(true)
      expect(result.responseTime).toBeLessThanOrEqual(2000)
    })

    it('should use worker pools for complex operations', async () => {
      const promise = optimizedTools.executeTool('image-generate', 'create', { 
        prompt: 'A beautiful landscape', 
        size: '1024x1024' 
      })
      
      vi.advanceTimersByTime(1500)
      const result = await promise

      expect(result.success).toBe(true)
      expect(result.responseTime).toBeLessThanOrEqual(2000)
    })

    it('should cache complex tool results', async () => {
      const parameters = { url: 'https://example.com', depth: 2 }
      
      // First execution
      const promise1 = optimizedTools.executeTool('firecrawl', 'scrape', parameters)
      vi.advanceTimersByTime(1500)
      const result1 = await promise1

      // Second execution (should hit cache)
      const promise2 = optimizedTools.executeTool('firecrawl', 'scrape', parameters)
      vi.advanceTimersByTime(10)
      const result2 = await promise2

      expect(result1.success).toBe(true)
      expect(result2.success).toBe(true)
      expect(result2.fromCache).toBe(true)
    })
  })

  describe('Acceptance Criteria 3: Progress bars for operations > 500ms', () => {
    it('should show progress for operations taking longer than 500ms', async () => {
      let progressUpdates: number[] = []
      
      const promise = optimizedTools.executeTool('firecrawl', 'scrape', { url: 'https://example.com' }, {
        onProgress: (progress) => {
          progressUpdates.push(progress)
        }
      })
      
      // Fast-forward past progress threshold
      vi.advanceTimersByTime(600)
      await promise

      expect(progressUpdates.length).toBeGreaterThan(0)
      expect(progressUpdates[progressUpdates.length - 1]).toBe(100)
    })

    it('should not show progress for basic operations', async () => {
      let progressUpdates: number[] = []
      
      const promise = optimizedTools.executeTool('image-edit', 'resize', { width: 100 }, {
        onProgress: (progress) => {
          progressUpdates.push(progress)
        }
      })
      
      vi.advanceTimersByTime(100)
      await promise

      expect(progressUpdates.length).toBe(0) // No progress for basic tools
    })

    it('should configure tools with appropriate progress thresholds', () => {
      const configs = optimizedTools.toolConfigs.value
      
      configs.forEach(config => {
        if (config.type === 'complex') {
          expect(config.showProgressAfter).toBeLessThanOrEqual(500)
        } else if (config.type === 'basic') {
          expect(config.showProgressAfter).toBeGreaterThan(500)
        }
      })
    })
  })

  describe('Acceptance Criteria 4: Ability to cancel long-running operations', () => {
    it('should cancel operations successfully', async () => {
      const promise = optimizedTools.executeTool('firecrawl', 'scrape', { url: 'https://example.com' })
      
      // Start operation
      vi.advanceTimersByTime(100)
      
      // Get the request ID and cancel
      const activeRequests = optimizedTools.activeRequests.value
      expect(activeRequests).toBeGreaterThan(0)
      
      // Cancel all operations
      const cancelledCount = optimizedTools.cancelAllOperations()
      expect(cancelledCount).toBeGreaterThan(0)
      
      try {
        await promise
        expect.fail('Promise should have been rejected due to cancellation')
      } catch (error) {
        expect(error.message).toContain('Operation cancelled')
      }
    })

    it('should mark cancellable tools correctly', () => {
      const configs = optimizedTools.toolConfigs.value
      
      configs.forEach(config => {
        if (config.type === 'complex' || config.expectedResponseTime > 100) {
          expect(config.cancellable).toBe(true)
        }
      })
    })

    it('should track cancelled operations in performance metrics', async () => {
      const promise = optimizedTools.executeTool('image-generate', 'create', { prompt: 'test' })
      
      vi.advanceTimersByTime(100)
      optimizedTools.cancelAllOperations()
      
      try {
        await promise
      } catch {
        // Expected cancellation
      }
      
      const performance = optimizedTools.getEnhancedSystemPerformance()
      expect(performance.cancelledCount).toBeGreaterThan(0)
    })
  })

  describe('Acceptance Criteria 5: Tool operations don\'t block other interactions', () => {
    it('should support requestIdleCallback for non-critical operations', () => {
      const performance = optimizedTools.getEnhancedSystemPerformance()
      expect(performance.idleManager.supportsIdleCallback).toBe(true)
    })

    it('should execute non-critical operations during idle time', async () => {
      const idlePromise = optimizedTools.scheduleIdleOperation(async () => {
        // Simulate some work
        await new Promise(resolve => setTimeout(resolve, 50))
      })
      
      // Advance timers to trigger idle callback
      vi.advanceTimersByTime(100)
      
      await expect(idlePromise).resolves.toBeUndefined()
      
      const idleStats = optimizedTools.getEnhancedSystemPerformance().idleManager
      expect(idleStats.isProcessing).toBe(false)
    })

    it('should use idle callbacks for low priority operations', async () => {
      // Register a low priority tool that should use idle callbacks
      optimizedTools.registerTool({
        id: 'background-sync',
        name: 'Background Sync',
        type: 'async',
        expectedResponseTime: 1000,
        maxRetries: 1,
        timeout: 5000,
        cacheable: false,
        poolable: true,
        cancellable: true,
        useIdleCallback: true,
        priority: 'low'
      })

      const promise = optimizedTools.executeTool('background-sync', 'sync', { data: 'test' })
      
      // Should be scheduled in idle callback
      vi.advanceTimersByTime(1100)
      const result = await promise

      expect(result.success).toBe(true)
    })

    it('should handle multiple concurrent operations without blocking', async () => {
      const promises = [
        optimizedTools.executeTool('image-edit', 'resize', { width: 100 }),
        optimizedTools.executeTool('text-transform', 'uppercase', { text: 'hello' }),
        optimizedTools.executeTool('image-edit', 'crop', { x: 0, y: 0, width: 50, height: 50 })
      ]
      
      vi.advanceTimersByTime(100)
      const results = await Promise.all(promises)

      results.forEach(result => {
        expect(result.success).toBe(true)
        expect(result.responseTime).toBeLessThanOrEqual(100)
      })
    })
  })

  describe('Story 2.4 Overall Performance Requirements', () => {
    it('should meet all Story 2.4 acceptance criteria', () => {
      const performance = optimizedTools.getEnhancedSystemPerformance()
      const requirements = performance.meetsStory24Requirements

      expect(requirements.hasProgressBars).toBe(true)
      expect(requirements.hasCancellation).toBe(true)
      expect(requirements.nonBlockingOperations).toBe(true)
    })

    it('should provide comprehensive performance metrics', () => {
      const performance = optimizedTools.getEnhancedSystemPerformance()
      
      expect(performance).toHaveProperty('microCache')
      expect(performance).toHaveProperty('idleManager')
      expect(performance).toHaveProperty('basicToolAverageTime')
      expect(performance).toHaveProperty('meetsStory24Requirements')
      
      expect(performance.microCache).toHaveProperty('size')
      expect(performance.microCache).toHaveProperty('maxSize')
      expect(performance.idleManager).toHaveProperty('supportsIdleCallback')
    })

    it('should optimize basic tools for consistent sub-100ms performance', async () => {
      const basicToolPromises = []
      
      // Execute multiple basic tool operations
      for (let i = 0; i < 5; i++) {
        basicToolPromises.push(
          optimizedTools.executeTool('text-transform', 'uppercase', { text: `test${i}` })
        )
      }
      
      vi.advanceTimersByTime(50)
      const results = await Promise.all(basicToolPromises)
      
      results.forEach(result => {
        expect(result.success).toBe(true)
        expect(result.responseTime).toBeLessThanOrEqual(100)
      })
      
      const performance = optimizedTools.getEnhancedSystemPerformance()
      expect(performance.basicToolAverageTime).toBeLessThanOrEqual(100)
    })

    it('should maintain performance under load', async () => {
      const operations = []
      
      // Simulate high load with mixed operation types
      for (let i = 0; i < 20; i++) {
        if (i % 3 === 0) {
          operations.push(optimizedTools.executeTool('image-edit', 'resize', { width: i * 10 }))
        } else if (i % 3 === 1) {
          operations.push(optimizedTools.executeTool('text-transform', 'uppercase', { text: `test${i}` }))
        } else {
          operations.push(optimizedTools.executeTool('firecrawl', 'scrape', { url: `https://example${i}.com` }))
        }
      }
      
      vi.advanceTimersByTime(2000)
      const results = await Promise.all(operations)
      
      const basicResults = results.filter((_, i) => i % 3 !== 2)
      const complexResults = results.filter((_, i) => i % 3 === 2)
      
      basicResults.forEach(result => {
        expect(result.responseTime).toBeLessThanOrEqual(100)
      })
      
      complexResults.forEach(result => {
        expect(result.responseTime).toBeLessThanOrEqual(2000)
      })
      
      const performance = optimizedTools.getEnhancedSystemPerformance()
      expect(performance.isOptimal).toBe(true)
    })
  })
})