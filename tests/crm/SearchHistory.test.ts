import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { ref } from 'vue'
import { useSearchHistory } from '~/layers/crm/composables/useSearchHistory'
import type { SearchFilter, SearchHistoryEntry, SavedSearch } from '~/layers/crm/types/crm'

// Mock the useDataApi composable
const mockDataApi = {
  create: vi.fn(),
  update: vi.fn(),
  getAll: vi.fn(),
  getById: vi.fn(),
  count: vi.fn()
}

vi.mock('~/layers/core/composables/useDataApi', () => ({
  useDataApi: () => mockDataApi
}))

describe('useSearchHistory', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('Search History', () => {
    it('should add search to history', async () => {
      const { addToHistory } = useSearchHistory()
      
      mockDataApi.create.mockResolvedValue({ id: 'test-history-id' })
      
      const userId = 'user-123'
      const query = 'test search'
      const filters: SearchFilter = { entityTypes: ['account'] }
      const resultCount = 5
      const searchTime = 150
      const workspaceId = 'workspace-123'

      await addToHistory(userId, query, filters, resultCount, searchTime, workspaceId)

      expect(mockDataApi.create).toHaveBeenCalledWith(
        'search_history',
        expect.objectContaining({
          user_id: userId,
          workspace_id: workspaceId,
          query: query.trim(),
          filters,
          result_count: resultCount,
          search_time: searchTime,
          active: true,
          timestamp: expect.any(Date)
        }),
        expect.objectContaining({
          workspaceId,
          profileId: userId
        })
      )
    })

    it('should not add empty queries to history', async () => {
      const { addToHistory } = useSearchHistory()
      
      const userId = 'user-123'
      const query = '   ' // Empty/whitespace query
      const filters: SearchFilter = {}
      
      await addToHistory(userId, query, filters, 0, 100)

      expect(mockDataApi.create).not.toHaveBeenCalled()
    })

    it('should not add duplicate consecutive searches', async () => {
      const searchHistoryInstance = useSearchHistory()
      
      // First, add an entry to create initial state
      mockDataApi.create.mockResolvedValueOnce({ id: 'existing-id' })
      mockDataApi.getAll.mockResolvedValue([]) // No cleanup needed
      
      await searchHistoryInstance.addToHistory('user-123', 'test search', { entityTypes: ['account'] }, 5, 150)
      
      // Clear the mock to test the duplicate detection
      mockDataApi.create.mockClear()
      
      // Try to add the same search again
      await searchHistoryInstance.addToHistory('user-123', 'test search', { entityTypes: ['account'] }, 5, 150)

      expect(mockDataApi.create).not.toHaveBeenCalled()
    })

    it('should get search history for user', async () => {
      const { getHistory } = useSearchHistory()
      
      const mockEntries = [
        {
          id: 'entry-1',
          user_id: 'user-123',
          query: 'search 1',
          filters: {},
          result_count: 3,
          search_time: 100,
          timestamp: { toDate: () => new Date('2023-01-01') },
          active: true
        }
      ]
      
      mockDataApi.getAll.mockResolvedValue(mockEntries)
      mockDataApi.count.mockResolvedValue(1)

      const result = await getHistory({
        userId: 'user-123',
        workspaceId: 'workspace-123',
        limit: 10,
        offset: 0
      })

      expect(result.entries).toHaveLength(1)
      expect(result.total).toBe(1)
      expect(result.hasMore).toBe(false)
      expect(mockDataApi.getAll).toHaveBeenCalledWith(
        'search_history',
        expect.objectContaining({
          workspaceId: 'workspace-123',
          profileId: 'user-123',
          limit: 10,
          orderBy: 'timestamp',
          orderDirection: 'desc',
          offset: 0,
          filters: expect.arrayContaining([
            { field: 'user_id', operator: 'eq', value: 'user-123' },
            { field: 'active', operator: 'eq', value: true },
            { field: 'workspace_id', operator: 'eq', value: 'workspace-123' }
          ])
        })
      )
    })

    it('should clear search history for user', async () => {
      const { clearHistory } = useSearchHistory()
      
      const mockEntries = [
        { id: 'entry-1', user_id: 'user-123' },
        { id: 'entry-2', user_id: 'user-123' }
      ]
      
      mockDataApi.getAll.mockResolvedValue(mockEntries)
      mockDataApi.update.mockResolvedValue({})

      await clearHistory('user-123', 'workspace-123')

      expect(mockDataApi.update).toHaveBeenCalledTimes(2)
      expect(mockDataApi.update).toHaveBeenCalledWith(
        'search_history',
        'entry-1',
        { active: false },
        expect.objectContaining({
          workspaceId: 'workspace-123',
          profileId: 'user-123'
        })
      )
    })

    it('should remove specific entry from history', async () => {
      const { removeFromHistory } = useSearchHistory()
      
      mockDataApi.update.mockResolvedValue({})

      await removeFromHistory('entry-123', 'user-123', 'workspace-123')

      expect(mockDataApi.update).toHaveBeenCalledWith(
        'search_history',
        'entry-123',
        { active: false },
        expect.objectContaining({
          workspaceId: 'workspace-123',
          profileId: 'user-123'
        })
      )
    })

    it('should get frequent searches', async () => {
      const { getFrequentSearches } = useSearchHistory()
      
      const mockEntries = [
        { query: 'popular search' },
        { query: 'popular search' },
        { query: 'another search' },
        { query: 'popular search' }
      ]
      
      mockDataApi.getAll.mockResolvedValue(mockEntries)

      const result = await getFrequentSearches('user-123', 'workspace-123', 5)

      expect(result).toEqual(['popular search', 'another search'])
      expect(mockDataApi.getAll).toHaveBeenCalledWith(
        'search_history',
        expect.objectContaining({
          limit: 100,
          orderBy: 'timestamp',
          orderDirection: 'desc'
        })
      )
    })
  })

  describe('Saved Searches', () => {
    it('should create saved search', async () => {
      const { createSavedSearch } = useSearchHistory()
      
      mockDataApi.create.mockResolvedValue({ id: 'saved-search-id' })
      
      const userId = 'user-123'
      const name = 'My Saved Search'
      const query = 'important query'
      const filters: SearchFilter = { entityTypes: ['contact'], status: 'active' }
      const workspaceId = 'workspace-123'
      const description = 'This is a saved search'
      const tags = ['important', 'contacts']

      const result = await createSavedSearch(userId, name, query, filters, workspaceId, description, tags)

      expect(result.id).toBe('saved-search-id')
      expect(result.name).toBe(name)
      expect(result.query).toBe(query)
      expect(result.filters).toEqual(filters)
      expect(result.tags).toEqual(tags)
      expect(mockDataApi.create).toHaveBeenCalledWith(
        'saved_searches',
        expect.objectContaining({
          user_id: userId,
          workspace_id: workspaceId,
          name,
          description,
          query,
          filters,
          tags,
          is_favorite: false,
          notification_enabled: false,
          execution_count: 0,
          active: true,
          created_at: expect.any(Date),
          updated_at: expect.any(Date)
        }),
        expect.objectContaining({
          workspaceId,
          profileId: userId
        })
      )
    })

    it('should get saved searches for user', async () => {
      const { getSavedSearches } = useSearchHistory()
      
      const mockSearches = [
        {
          id: 'search-1',
          user_id: 'user-123',
          name: 'Search 1',
          query: 'test',
          filters: {},
          is_favorite: true,
          created_at: { toDate: () => new Date('2023-01-01') },
          updated_at: { toDate: () => new Date('2023-01-02') },
          execution_count: 5,
          active: true,
          tags: []
        }
      ]
      
      mockDataApi.getAll.mockResolvedValue(mockSearches)
      mockDataApi.count.mockResolvedValue(1)

      const result = await getSavedSearches({
        userId: 'user-123',
        workspaceId: 'workspace-123',
        limit: 50,
        offset: 0,
        includeInactive: false
      })

      expect(result.searches).toHaveLength(1)
      expect(result.total).toBe(1)
      expect(result.hasMore).toBe(false)
      expect(mockDataApi.getAll).toHaveBeenCalledWith(
        'saved_searches',
        expect.objectContaining({
          workspaceId: 'workspace-123',
          profileId: 'user-123',
          limit: 50,
          orderBy: 'updated_at',
          orderDirection: 'desc',
          offset: 0,
          filters: expect.arrayContaining([
            { field: 'user_id', operator: 'eq', value: 'user-123' },
            { field: 'active', operator: 'eq', value: true },
            { field: 'workspace_id', operator: 'eq', value: 'workspace-123' }
          ])
        })
      )
    })

    it('should update saved search', async () => {
      const searchHistoryInstance = useSearchHistory()
      
      // First create a saved search to have something to update
      mockDataApi.create.mockResolvedValueOnce({ id: 'search-123' })
      
      const createdSearch = await searchHistoryInstance.createSavedSearch(
        'user-123',
        'Original Name',
        'original query',
        {},
        'workspace-123'
      )
      
      // Mock the update operation
      mockDataApi.update.mockResolvedValue({})
      
      const updates = {
        name: 'Updated Name',
        is_favorite: true
      }

      const result = await searchHistoryInstance.updateSavedSearch('search-123', 'user-123', updates, 'workspace-123')

      expect(result.name).toBe('Updated Name')
      expect(result.is_favorite).toBe(true)
      expect(mockDataApi.update).toHaveBeenCalledWith(
        'saved_searches',
        'search-123',
        expect.objectContaining({
          ...updates,
          updated_at: expect.any(Date)
        }),
        expect.objectContaining({
          workspaceId: 'workspace-123',
          profileId: 'user-123'
        })
      )
    })

    it('should delete saved search (soft delete)', async () => {
      const { deleteSavedSearch } = useSearchHistory()
      
      mockDataApi.update.mockResolvedValue({})

      await deleteSavedSearch('search-123', 'user-123', 'workspace-123')

      expect(mockDataApi.update).toHaveBeenCalledWith(
        'saved_searches',
        'search-123',
        expect.objectContaining({
          active: false,
          updated_at: expect.any(Date)
        }),
        expect.objectContaining({
          workspaceId: 'workspace-123',
          profileId: 'user-123'
        })
      )
    })

    it('should execute saved search and update tracking', async () => {
      const searchHistoryInstance = useSearchHistory()
      
      // First create a saved search
      mockDataApi.create.mockResolvedValueOnce({ id: 'search-123' })
      
      const createdSearch = await searchHistoryInstance.createSavedSearch(
        'user-123',
        'Test Search',
        'test query',
        { entityTypes: ['account'] },
        'workspace-123'
      )
      
      // Mock the update operation for execution tracking
      mockDataApi.update.mockResolvedValue({})

      const result = await searchHistoryInstance.executeSavedSearch('search-123', 'user-123', 'workspace-123')

      expect(result).toBeTruthy()
      expect(result?.execution_count).toBe(1) // From 0 to 1
      expect(mockDataApi.update).toHaveBeenCalledWith(
        'saved_searches',
        'search-123',
        expect.objectContaining({
          last_executed: expect.any(Date),
          execution_count: 1,
          updated_at: expect.any(Date)
        }),
        expect.objectContaining({
          workspaceId: 'workspace-123',
          profileId: 'user-123'
        })
      )
    })

    it('should toggle saved search favorite status', async () => {
      const searchHistoryInstance = useSearchHistory()
      
      // First create a saved search
      mockDataApi.create.mockResolvedValueOnce({ id: 'search-123' })
      
      const createdSearch = await searchHistoryInstance.createSavedSearch(
        'user-123',
        'Test Search',
        'test query',
        {},
        'workspace-123'
      )
      
      // Mock the update operation
      mockDataApi.update.mockResolvedValue({})

      await searchHistoryInstance.toggleSavedSearchFavorite('search-123', 'user-123', 'workspace-123')

      expect(mockDataApi.update).toHaveBeenCalledWith(
        'saved_searches',
        'search-123',
        expect.objectContaining({
          is_favorite: true,
          updated_at: expect.any(Date)
        }),
        expect.objectContaining({
          workspaceId: 'workspace-123',
          profileId: 'user-123'
        })
      )
    })

    it('should check if search is already saved', async () => {
      const searchHistoryInstance = useSearchHistory()
      
      // First create a saved search
      mockDataApi.create.mockResolvedValueOnce({ id: 'search-123' })
      
      const createdSearch = await searchHistoryInstance.createSavedSearch(
        'user-123',
        'Test Search',
        'test query',
        { entityTypes: ['account'] },
        'workspace-123'
      )

      expect(searchHistoryInstance.isSearchSaved('test query', { entityTypes: ['account'] })).toBe(true)
      expect(searchHistoryInstance.isSearchSaved('different query', { entityTypes: ['account'] })).toBe(false)
      expect(searchHistoryInstance.isSearchSaved('test query', { entityTypes: ['contact'] })).toBe(false)
    })
  })

  describe('Utility Functions', () => {
    it('should format entity types correctly', () => {
      const { formatEntityTypes } = useSearchHistory()

      expect(formatEntityTypes([])).toBe('All types')
      expect(formatEntityTypes(['account'])).toBe('Accounts')
      expect(formatEntityTypes(['account', 'contact'])).toBe('2 types')
      expect(formatEntityTypes(['account', 'contact', 'company', 'product', 'order'])).toBe('All types')
    })

    it('should format filters display correctly', () => {
      const { formatFiltersDisplay } = useSearchHistory()

      expect(formatFiltersDisplay({})).toBe('No filters')
      expect(formatFiltersDisplay({ status: 'active' })).toBe('Status: active')
      expect(formatFiltersDisplay({ 
        entityTypes: ['account'], 
        status: 'active',
        tags: ['important', 'urgent', 'critical']
      })).toBe('Accounts • Status: active • Tags: important, urgent...')
    })
  })

  describe('Error Handling', () => {
    it('should handle errors when adding to history', async () => {
      const { addToHistory } = useSearchHistory()
      
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockDataApi.create.mockRejectedValue(new Error('Database error'))

      // Should not throw error
      await expect(addToHistory('user-123', 'test', {}, 0, 100)).resolves.toBeUndefined()
      
      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to add search to history:', expect.any(Error))
      consoleErrorSpy.mockRestore()
    })

    it('should handle errors when getting history', async () => {
      const { getHistory } = useSearchHistory()
      
      mockDataApi.getAll.mockRejectedValue(new Error('Database error'))

      const result = await getHistory({ userId: 'user-123' })

      expect(result.entries).toEqual([])
      expect(result.total).toBe(0)
      expect(result.hasMore).toBe(false)
    })

    it('should handle errors when creating saved search', async () => {
      const { createSavedSearch } = useSearchHistory()
      
      mockDataApi.create.mockRejectedValue(new Error('Database error'))

      await expect(createSavedSearch('user-123', 'Test', 'query', {})).rejects.toThrow()
    })
  })
})