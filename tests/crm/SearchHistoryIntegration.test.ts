import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { useGlobalSearch } from '~/layers/crm/composables/useGlobalSearch'
import type { SearchResponse } from '~/layers/crm/types/crm'

// Mock the dependencies
const mockDataApi = {
  create: vi.fn(),
  update: vi.fn(),
  getAll: vi.fn(),
  getById: vi.fn(),
  count: vi.fn()
}

const mockSearchHistory = {
  addToHistory: vi.fn(),
  getHistory: vi.fn(),
  clearHistory: vi.fn(),
  removeFromHistory: vi.fn(),
  getSavedSearches: vi.fn(),
  createSavedSearch: vi.fn(),
  updateSavedSearch: vi.fn(),
  deleteSavedSearch: vi.fn(),
  executeSavedSearch: vi.fn(),
  toggleSavedSearchFavorite: vi.fn(),
  getFrequentSearches: vi.fn(),
  searchInHistory: vi.fn(),
  isSearchSaved: vi.fn(),
  formatEntityTypes: vi.fn(),
  formatFiltersDisplay: vi.fn(),
  searchHistory: { value: [] },
  savedSearches: { value: [] },
  recentSearches: { value: [] },
  hasHistory: { value: false },
  hasSavedSearches: { value: false },
  favoriteSavedSearches: { value: [] }
}

vi.mock('~/layers/core/composables/useDataApi', () => ({
  useDataApi: () => mockDataApi
}))

vi.mock('~/layers/crm/composables/useSearchHistory', () => ({
  useSearchHistory: () => mockSearchHistory
}))

// Mock the $fetch function for API calls
const mockFetch = vi.fn()
global.$fetch = mockFetch

describe('useGlobalSearch with Search History Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('User Context Initialization', () => {
    it('should initialize user context and load search history', async () => {
      const { initializeUserContext } = useGlobalSearch()
      
      mockSearchHistory.getHistory.mockResolvedValue({
        entries: [],
        total: 0,
        hasMore: false
      })

      await initializeUserContext('user-123', 'workspace-123')

      expect(mockSearchHistory.getHistory).toHaveBeenCalledWith({
        userId: 'user-123',
        workspaceId: 'workspace-123'
      })
    })

    it('should handle initialization without workspace', async () => {
      const { initializeUserContext } = useGlobalSearch()
      
      mockSearchHistory.getHistory.mockResolvedValue({
        entries: [],
        total: 0,
        hasMore: false
      })

      await initializeUserContext('user-123')

      expect(mockSearchHistory.getHistory).toHaveBeenCalledWith({
        userId: 'user-123',
        workspaceId: undefined
      })
    })
  })

  describe('Saved Search Execution', () => {
    it('should execute saved search and update search state', async () => {
      const { initializeUserContext, executeSavedSearch, searchQuery, searchFilters } = useGlobalSearch()
      
      // Initialize user context first
      await initializeUserContext('user-123', 'workspace-123')
      
      const mockSavedSearch = {
        id: 'saved-search-123',
        query: 'important contacts',
        filters: { entityTypes: ['contact'], status: 'active' },
        execution_count: 5
      }
      
      mockSearchHistory.executeSavedSearch.mockResolvedValue({
        ...mockSavedSearch,
        execution_count: 6,
        last_executed: new Date()
      })
      
      // Mock the search API response
      const mockSearchResponse: SearchResponse = {
        results: [],
        total: 0,
        hasMore: false
      }
      mockFetch.mockResolvedValue(mockSearchResponse)

      await executeSavedSearch('saved-search-123')

      expect(mockSearchHistory.executeSavedSearch).toHaveBeenCalledWith(
        'saved-search-123',
        'user-123',
        'workspace-123'
      )
      
      // Check that search state was updated
      expect(searchQuery.value).toBe('important contacts')
      expect(searchFilters.value).toEqual({ entityTypes: ['contact'], status: 'active' })
    })

    it('should handle execution when user context not set', async () => {
      const { executeSavedSearch } = useGlobalSearch()
      
      const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      await executeSavedSearch('saved-search-123')

      expect(consoleWarnSpy).toHaveBeenCalledWith('Cannot execute saved search: User ID not set')
      expect(mockSearchHistory.executeSavedSearch).not.toHaveBeenCalled()
      
      consoleWarnSpy.mockRestore()
    })
  })

  describe('Save Current Search', () => {
    it('should save current search query and filters', async () => {
      const { initializeUserContext, saveCurrentSearch, searchQuery, searchFilters } = useGlobalSearch()
      
      await initializeUserContext('user-123', 'workspace-123')
      
      // Set current search state
      searchQuery.value = 'active accounts'
      searchFilters.value = { entityTypes: ['account'], status: 'active' }
      
      const mockSavedSearch = {
        id: 'new-saved-search',
        name: 'My Active Accounts',
        query: 'active accounts',
        filters: { entityTypes: ['account'], status: 'active' }
      }
      
      mockSearchHistory.createSavedSearch.mockResolvedValue(mockSavedSearch)

      const result = await saveCurrentSearch(
        'My Active Accounts',
        'Search for all active accounts',
        ['important', 'accounts']
      )

      expect(mockSearchHistory.createSavedSearch).toHaveBeenCalledWith(
        'user-123',
        'My Active Accounts',
        'active accounts',
        { entityTypes: ['account'], status: 'active' },
        'workspace-123',
        'Search for all active accounts',
        ['important', 'accounts']
      )
      
      expect(result).toEqual(mockSavedSearch)
    })

    it('should handle save when no query is set', async () => {
      const { initializeUserContext, saveCurrentSearch, searchQuery } = useGlobalSearch()
      
      await initializeUserContext('user-123', 'workspace-123')
      searchQuery.value = '' // No query set
      
      const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      const result = await saveCurrentSearch('Empty Search')

      expect(consoleWarnSpy).toHaveBeenCalledWith('Cannot save search: User ID not set or no query')
      expect(mockSearchHistory.createSavedSearch).not.toHaveBeenCalled()
      expect(result).toBeNull()
      
      consoleWarnSpy.mockRestore()
    })
  })

  describe('Apply History Search', () => {
    it('should apply search from history', async () => {
      const { applyHistorySearch, searchQuery, searchFilters } = useGlobalSearch()
      
      const historyEntry = {
        id: 'history-123',
        query: 'old search',
        filters: { entityTypes: ['contact'] },
        timestamp: new Date(),
        result_count: 3
      }
      
      // Mock the search API response
      const mockSearchResponse: SearchResponse = {
        results: [],
        total: 0,
        hasMore: false
      }
      mockFetch.mockResolvedValue(mockSearchResponse)

      applyHistorySearch(historyEntry)

      expect(searchQuery.value).toBe('old search')
      expect(searchFilters.value).toEqual({ entityTypes: ['contact'] })
    })
  })

  describe('Search Suggestions', () => {
    it('should get search suggestions based on history', async () => {
      const { initializeUserContext, getSearchSuggestions } = useGlobalSearch()
      
      await initializeUserContext('user-123', 'workspace-123')
      
      const mockSuggestions = ['frequent search 1', 'frequent search 2', 'frequent search 3']
      mockSearchHistory.getFrequentSearches.mockResolvedValue(mockSuggestions)

      const result = await getSearchSuggestions(5)

      expect(mockSearchHistory.getFrequentSearches).toHaveBeenCalledWith(
        'user-123',
        'workspace-123',
        5
      )
      expect(result).toEqual(mockSuggestions)
    })

    it('should return empty array when user context not set', async () => {
      const { getSearchSuggestions } = useGlobalSearch()

      const result = await getSearchSuggestions()

      expect(result).toEqual([])
      expect(mockSearchHistory.getFrequentSearches).not.toHaveBeenCalled()
    })
  })

  describe('Search History State Access', () => {
    it('should expose search history state from useSearchHistory', () => {
      const globalSearch = useGlobalSearch()
      
      expect(globalSearch.searchHistory).toBe(mockSearchHistory.searchHistory)
      expect(globalSearch.savedSearches).toBe(mockSearchHistory.savedSearches)
      expect(globalSearch.recentSearches).toBe(mockSearchHistory.recentSearches)
      expect(globalSearch.hasHistory).toBe(mockSearchHistory.hasHistory)
      expect(globalSearch.hasSavedSearches).toBe(mockSearchHistory.hasSavedSearches)
      expect(globalSearch.favoriteSavedSearches).toBe(mockSearchHistory.favoriteSavedSearches)
    })

    it('should expose search history methods', () => {
      const globalSearch = useGlobalSearch()
      
      expect(globalSearch.getHistory).toBe(mockSearchHistory.getHistory)
      expect(globalSearch.clearHistory).toBe(mockSearchHistory.clearHistory)
      expect(globalSearch.removeFromHistory).toBe(mockSearchHistory.removeFromHistory)
      expect(globalSearch.getSavedSearches).toBe(mockSearchHistory.getSavedSearches)
      expect(globalSearch.createSavedSearch).toBe(mockSearchHistory.createSavedSearch)
      expect(globalSearch.updateSavedSearch).toBe(mockSearchHistory.updateSavedSearch)
      expect(globalSearch.deleteSavedSearch).toBe(mockSearchHistory.deleteSavedSearch)
      expect(globalSearch.toggleSavedSearchFavorite).toBe(mockSearchHistory.toggleSavedSearchFavorite)
      expect(globalSearch.isSearchSaved).toBe(mockSearchHistory.isSearchSaved)
      expect(globalSearch.formatEntityTypes).toBe(mockSearchHistory.formatEntityTypes)
      expect(globalSearch.formatFiltersDisplay).toBe(mockSearchHistory.formatFiltersDisplay)
    })
  })

  describe('Error Handling', () => {
    it('should handle errors when executing saved search', async () => {
      const { initializeUserContext, executeSavedSearch, error } = useGlobalSearch()
      
      await initializeUserContext('user-123', 'workspace-123')
      
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockSearchHistory.executeSavedSearch.mockRejectedValue(new Error('Execution failed'))

      await executeSavedSearch('saved-search-123')

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to execute saved search:', expect.any(Error))
      expect(error.value).toEqual({
        message: 'Failed to execute saved search',
        code: 'SAVED_SEARCH_EXECUTION_ERROR'
      })
      
      consoleErrorSpy.mockRestore()
    })

    it('should handle errors when saving current search', async () => {
      const { initializeUserContext, saveCurrentSearch, searchQuery, error } = useGlobalSearch()
      
      await initializeUserContext('user-123', 'workspace-123')
      searchQuery.value = 'test query'
      
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockSearchHistory.createSavedSearch.mockRejectedValue(new Error('Save failed'))

      const result = await saveCurrentSearch('Test Search')

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to save search:', expect.any(Error))
      expect(error.value).toEqual({
        message: 'Failed to save search',
        code: 'SAVE_SEARCH_ERROR'
      })
      expect(result).toBeNull()
      
      consoleErrorSpy.mockRestore()
    })

    it('should handle errors when getting search suggestions', async () => {
      const { initializeUserContext, getSearchSuggestions } = useGlobalSearch()
      
      await initializeUserContext('user-123', 'workspace-123')
      
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockSearchHistory.getFrequentSearches.mockRejectedValue(new Error('Suggestions failed'))

      const result = await getSearchSuggestions()

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to get search suggestions:', expect.any(Error))
      expect(result).toEqual([])
      
      consoleErrorSpy.mockRestore()
    })
  })
})