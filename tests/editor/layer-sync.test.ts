import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { ref, nextTick } from 'vue'
import { useLayerSync } from '../../layers/editor/composables/useLayerSync'
import { useOperationalTransform } from '../../layers/editor/composables/useOperationalTransform'
import type { LayerOperation } from '../../layers/editor/composables/useLayerSync'
import type { EditorLayer } from '../../layers/editor/composables/useEditorState'

// Mock WebSocket
class MockWebSocket {
  static CONNECTING = 0
  static OPEN = 1
  static CLOSING = 2
  static CLOSED = 3

  readyState = MockWebSocket.OPEN
  url: string
  onopen: ((event: Event) => void) | null = null
  onclose: ((event: CloseEvent) => void) | null = null
  onmessage: ((event: MessageEvent) => void) | null = null
  onerror: ((event: Event) => void) | null = null

  constructor(url: string) {
    this.url = url
  }

  send(data: string) {
    // Simulate successful send
    return true
  }

  close(code?: number, reason?: string) {
    this.readyState = MockWebSocket.CLOSED
  }
}

// Mock Firebase
const mockAuth = {
  currentUser: {
    getIdToken: vi.fn().mockResolvedValue('mock-token'),
    uid: 'test-user-123'
  }
}

// Mock editor state
const mockLayers = ref<EditorLayer[]>([])
const mockActiveLayerId = ref<string | null>(null)
const mockProjectState = {
  id: 'test-project-123',
  name: 'Test Project',
  width: 800,
  height: 600
}

const mockEditorState = {
  layers: mockLayers,
  activeLayerId: mockActiveLayerId,
  projectState: mockProjectState,
  addLayer: vi.fn((layer: Partial<EditorLayer>) => {
    const newLayer: EditorLayer = {
      id: layer.id || `layer-${Date.now()}`,
      name: layer.name || 'New Layer',
      type: layer.type || 'image',
      visible: layer.visible !== false,
      locked: layer.locked || false,
      opacity: layer.opacity || 1,
      data: layer.data || null,
      transform: layer.transform || {
        x: 0, y: 0, width: 100, height: 100,
        rotation: 0, flipX: false, flipY: false
      }
    }
    mockLayers.value.push(newLayer)
    if (!mockActiveLayerId.value) {
      mockActiveLayerId.value = newLayer.id
    }
    return newLayer.id
  }),
  updateLayer: vi.fn((layerId: string, updates: Partial<EditorLayer>) => {
    const index = mockLayers.value.findIndex(l => l.id === layerId)
    if (index !== -1) {
      mockLayers.value[index] = { ...mockLayers.value[index], ...updates }
    }
  }),
  removeLayer: vi.fn((layerId: string) => {
    const index = mockLayers.value.findIndex(l => l.id === layerId)
    if (index !== -1) {
      mockLayers.value.splice(index, 1)
      if (mockActiveLayerId.value === layerId) {
        mockActiveLayerId.value = mockLayers.value.length > 0 ? mockLayers.value[0].id : null
      }
    }
  }),
  reorderLayers: vi.fn((fromIndex: number, toIndex: number) => {
    const layer = mockLayers.value.splice(fromIndex, 1)[0]
    mockLayers.value.splice(toIndex, 0, layer)
  })
}

// Mock WebSocket connection
const mockWebSocketConnection = {
  connectionInfo: {
    status: 'connected' as const,
    url: 'ws://localhost:3001',
    lastConnected: new Date(),
    reconnectAttempts: 0,
    error: null
  },
  isConnected: ref(true),
  send: vi.fn(() => true),
  on: vi.fn(),
  off: vi.fn()
}

vi.mock('../../layers/editor/composables/useEditorState', () => ({
  useEditorState: () => mockEditorState
}))

vi.mock('../../layers/editor/composables/useWebSocketConnection', () => ({
  useWebSocketConnection: () => mockWebSocketConnection
}))

vi.mock('../../layers/core/composables/firebase', () => ({
  useFirebase: () => ({ auth: mockAuth })
}))

vi.mock('#app', () => ({
  useRuntimeConfig: () => ({
    public: { websocketUrl: 'ws://localhost:3001' }
  })
}))

// Mock Nuxt auto-imports
globalThis.readonly = vi.fn((obj) => obj)
globalThis.useRuntimeConfig = vi.fn(() => ({
  public: { websocketUrl: 'ws://localhost:3001' }
}))

describe('Layer Synchronization (Story 1.3)', () => {
  let layerSync: ReturnType<typeof useLayerSync>
  const testOptions = {
    workspaceId: 'test-workspace',
    projectId: 'test-project',
    userId: 'test-user-123',
    syncThreshold: 50,
    maxBatchSize: 10
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockLayers.value = []
    mockActiveLayerId.value = null
    layerSync = useLayerSync(testOptions)
  })

  afterEach(() => {
    vi.clearAllTimers()
    layerSync.cleanup()
  })

  describe('Layer Creation Sync', () => {
    it('should sync layer creation within 100ms', async () => {
      vi.useFakeTimers()
      
      const startTime = Date.now()
      const layerId = layerSync.syncLayerCreate({
        name: 'Test Layer',
        type: 'image',
        data: 'test-data'
      })

      expect(layerId).toBeDefined()
      expect(mockLayers.value).toHaveLength(1)
      expect(mockLayers.value[0].name).toBe('Test Layer')

      // Advance time by sync threshold (50ms)
      vi.advanceTimersByTime(50)
      
      // Should trigger batched send
      expect(mockWebSocketConnection.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'layer-operations-batch',
          operations: expect.arrayContaining([
            expect.objectContaining({
              type: 'create',
              layerId: layerId
            })
          ])
        })
      )

      vi.useRealTimers()
    })

    it('should handle multiple rapid layer creations with batching', async () => {
      vi.useFakeTimers()

      // Create multiple layers rapidly
      for (let i = 0; i < 5; i++) {
        layerSync.syncLayerCreate({
          name: `Layer ${i}`,
          type: 'image'
        })
      }

      expect(mockLayers.value).toHaveLength(5)
      expect(layerSync.syncState.operationQueue).toHaveLength(5)

      // Advance time to trigger batch
      vi.advanceTimersByTime(50)

      expect(mockWebSocketConnection.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'layer-operations-batch',
          operations: expect.arrayContaining([
            expect.objectContaining({ type: 'create' })
          ])
        })
      )

      vi.useRealTimers()
    })

    it('should send immediately when batch reaches max size', async () => {
      // Set small batch size for testing
      layerSync = useLayerSync({ ...testOptions, maxBatchSize: 3 })

      // Create layers to reach batch size
      for (let i = 0; i < 3; i++) {
        layerSync.syncLayerCreate({
          name: `Layer ${i}`,
          type: 'image'
        })
      }

      // Should send immediately without waiting for timer
      expect(mockWebSocketConnection.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'layer-operations-batch',
          operations: expect.arrayContaining([
            expect.objectContaining({ type: 'create' })
          ])
        })
      )
    })
  })

  describe('Layer Update Sync', () => {
    let testLayerId: string

    beforeEach(() => {
      testLayerId = layerSync.syncLayerCreate({
        name: 'Test Layer',
        type: 'image'
      })
      vi.clearAllMocks()
    })

    it('should sync layer updates for all users', async () => {
      vi.useFakeTimers()

      layerSync.syncLayerUpdate(testLayerId, {
        name: 'Updated Layer',
        opacity: 0.5
      })

      expect(mockLayers.value[0].name).toBe('Updated Layer')
      expect(mockLayers.value[0].opacity).toBe(0.5)

      vi.advanceTimersByTime(50)

      expect(mockWebSocketConnection.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'layer-operations-batch',
          operations: expect.arrayContaining([
            expect.objectContaining({
              type: 'update',
              layerId: testLayerId,
              data: expect.objectContaining({
                updates: expect.objectContaining({
                  name: 'Updated Layer',
                  opacity: 0.5
                })
              })
            })
          ])
        })
      )

      vi.useRealTimers()
    })

    it('should handle concurrent updates with conflict resolution', async () => {
      const operation1: LayerOperation = {
        id: 'op1',
        type: 'update',
        layerId: testLayerId,
        data: { updates: { name: 'Name 1', opacity: 0.7 } },
        userId: 'user1',
        timestamp: Date.now(),
        version: 1
      }

      const operation2: LayerOperation = {
        id: 'op2',
        type: 'update',
        layerId: testLayerId,
        data: { updates: { name: 'Name 2', visible: false } },
        userId: 'user2',
        timestamp: Date.now() + 10, // Slightly later
        version: 1
      }

      // Simulate receiving concurrent operations
      layerSync.applyRemoteOperation(operation1)
      layerSync.applyRemoteOperation(operation2)

      // Should handle conflicts and apply both non-conflicting updates
      await nextTick()
      
      expect(layerSync.syncState.conflicts.length).toBeGreaterThanOrEqual(0)
    })
  })

  describe('Layer Deletion Sync', () => {
    let testLayerId: string

    beforeEach(() => {
      testLayerId = layerSync.syncLayerCreate({
        name: 'Test Layer',
        type: 'image'
      })
      vi.clearAllMocks()
    })

    it('should sync layer deletion for all users', async () => {
      vi.useFakeTimers()

      expect(mockLayers.value).toHaveLength(1)
      
      layerSync.syncLayerDelete(testLayerId)

      expect(mockLayers.value).toHaveLength(0)

      vi.advanceTimersByTime(50)

      expect(mockWebSocketConnection.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'layer-operations-batch',
          operations: expect.arrayContaining([
            expect.objectContaining({
              type: 'delete',
              layerId: testLayerId
            })
          ])
        })
      )

      vi.useRealTimers()
    })

    it('should clean up layer versions when layer is deleted', async () => {
      expect(layerSync.syncState.layerVersions.has(testLayerId)).toBe(true)
      
      layerSync.syncLayerDelete(testLayerId)
      
      // Version should still exist until confirmed
      expect(layerSync.syncState.layerVersions.has(testLayerId)).toBe(true)
    })
  })

  describe('Layer Reorder Sync', () => {
    let layerIds: string[]

    beforeEach(() => {
      layerIds = []
      for (let i = 0; i < 3; i++) {
        const id = layerSync.syncLayerCreate({
          name: `Layer ${i}`,
          type: 'image'
        })
        layerIds.push(id)
      }
      vi.clearAllMocks()
    })

    it('should sync layer reordering immediately', async () => {
      vi.useFakeTimers()

      expect(mockLayers.value.map(l => l.name)).toEqual(['Layer 0', 'Layer 1', 'Layer 2'])

      layerSync.syncLayerReorder(0, 2)

      expect(mockLayers.value.map(l => l.name)).toEqual(['Layer 1', 'Layer 2', 'Layer 0'])

      vi.advanceTimersByTime(50)

      expect(mockWebSocketConnection.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'layer-operations-batch',
          operations: expect.arrayContaining([
            expect.objectContaining({
              type: 'reorder',
              data: expect.objectContaining({
                fromIndex: 0,
                toIndex: 2
              })
            })
          ])
        })
      )

      vi.useRealTimers()
    })
  })

  describe('Conflict Resolution', () => {
    let testLayerId: string

    beforeEach(() => {
      testLayerId = layerSync.syncLayerCreate({
        name: 'Test Layer',
        type: 'image'
      })
    })

    it('should detect concurrent edit conflicts', async () => {
      const now = Date.now()
      
      const operation1: LayerOperation = {
        id: 'op1',
        type: 'update',
        layerId: testLayerId,
        data: { updates: { name: 'Name 1' } },
        userId: 'user1',
        timestamp: now,
        version: 2
      }

      const operation2: LayerOperation = {
        id: 'op2',
        type: 'update',
        layerId: testLayerId,
        data: { updates: { name: 'Name 2' } },
        userId: 'user2',
        timestamp: now + 100, // Within conflict window
        version: 2
      }

      layerSync.applyRemoteOperation(operation1)
      layerSync.applyRemoteOperation(operation2)

      // Should detect conflict and add to conflicts list
      expect(layerSync.hasConflicts.value).toBe(true)
    })

    it('should resolve conflicts automatically when possible', async () => {
      const operation: LayerOperation = {
        id: 'op1',
        type: 'update',
        layerId: testLayerId,
        data: { updates: { opacity: 0.5, visible: false } },
        userId: 'user1',
        timestamp: Date.now(),
        version: 2
      }

      layerSync.applyRemoteOperation(operation)

      // Should apply safe updates
      expect(mockLayers.value[0].opacity).toBe(0.5)
      expect(mockLayers.value[0].visible).toBe(false)
    })

    it('should clear conflicts when requested', () => {
      // Simulate conflicts
      layerSync.syncState.conflicts.push({
        operation: {
          id: 'op1',
          type: 'update',
          layerId: testLayerId,
          data: {},
          userId: 'user1',
          timestamp: Date.now(),
          version: 1
        },
        conflictType: 'concurrent_edit',
        strategy: 'manual',
        resolved: false
      })

      expect(layerSync.hasConflicts.value).toBe(true)
      
      layerSync.clearConflicts()
      
      expect(layerSync.hasConflicts.value).toBe(false)
    })
  })

  describe('Performance Requirements', () => {
    it('should maintain sub-100ms synchronization latency', async () => {
      vi.useFakeTimers()
      
      const startTime = Date.now()
      
      layerSync.syncLayerCreate({
        name: 'Performance Test Layer',
        type: 'image'
      })

      // Should batch and send within sync threshold (50ms)
      vi.advanceTimersByTime(50)
      
      expect(mockWebSocketConnection.send).toHaveBeenCalled()
      
      // Update sync state timestamp
      layerSync.syncState.lastSyncTime = Date.now()
      
      // Latency should be minimal
      expect(layerSync.syncLatency.value).toBeLessThan(100)
      
      vi.useRealTimers()
    })

    it('should handle rapid changes efficiently with batching', async () => {
      vi.useFakeTimers()
      
      // Perform 20 rapid operations
      for (let i = 0; i < 20; i++) {
        layerSync.syncLayerCreate({
          name: `Rapid Layer ${i}`,
          type: 'image'
        })
      }

      // Should batch operations
      expect(layerSync.syncState.operationQueue.length).toBe(20)
      
      // Advance time to trigger sends
      vi.advanceTimersByTime(50)
      
      // Should have sent operations in batches
      expect(mockWebSocketConnection.send).toHaveBeenCalled()
      
      vi.useRealTimers()
    })
  })

  describe('Operational Transformation', () => {
    it('should transform conflicting operations correctly', () => {
      const { transformOperation, operationsConflict } = useOperationalTransform()
      
      const op1: LayerOperation = {
        id: 'op1',
        type: 'update',
        layerId: 'layer1',
        data: { updates: { name: 'Name 1' } },
        userId: 'user1',
        timestamp: Date.now(),
        version: 1
      }

      const op2: LayerOperation = {
        id: 'op2',
        type: 'update',
        layerId: 'layer1',
        data: { updates: { name: 'Name 2' } },
        userId: 'user2',
        timestamp: Date.now() + 100,
        version: 1
      }

      expect(operationsConflict(op1, op2)).toBe(true)

      const context = {
        layerState: mockLayers.value,
        operationHistory: new Map(),
        conflictingOperations: []
      }

      const transformed = transformOperation(op1, op2, context)
      expect(transformed).toBeDefined()
      expect(transformed.transforms).toBeDefined()
    })

    it('should merge non-conflicting updates', () => {
      const { mergeUpdates } = useOperationalTransform()
      
      const updates1 = { name: 'New Name', opacity: 0.5 }
      const updates2 = { visible: false, locked: true }

      const merged = mergeUpdates(updates1, updates2, true)
      
      expect(merged).toEqual({
        name: 'New Name',
        opacity: 0.5,
        visible: false,
        locked: true
      })
    })
  })

  describe('Integration Tests', () => {
    it('should handle complete collaborative editing workflow', async () => {
      vi.useFakeTimers()
      
      // User 1 creates a layer
      const layerId = layerSync.syncLayerCreate({
        name: 'Collaborative Layer',
        type: 'image'
      })

      // Simulate User 2 updating the same layer
      const remoteUpdate: LayerOperation = {
        id: 'remote-op',
        type: 'update',
        layerId,
        data: { updates: { opacity: 0.7 } },
        userId: 'user2',
        timestamp: Date.now(),
        version: 2
      }

      layerSync.applyRemoteOperation(remoteUpdate)

      // User 1 makes another update
      layerSync.syncLayerUpdate(layerId, { visible: false })

      vi.advanceTimersByTime(50)

      // Both updates should be applied
      expect(mockLayers.value[0].opacity).toBe(0.7)
      expect(mockLayers.value[0].visible).toBe(false)

      vi.useRealTimers()
    })

    it('should maintain consistency across multiple clients', async () => {
      const layerId1 = layerSync.syncLayerCreate({ name: 'Layer 1', type: 'image' })
      const layerId2 = layerSync.syncLayerCreate({ name: 'Layer 2', type: 'image' })

      // Simulate operations from multiple clients
      const operations: LayerOperation[] = [
        {
          id: 'op1',
          type: 'update',
          layerId: layerId1,
          data: { updates: { name: 'Updated Layer 1' } },
          userId: 'user1',
          timestamp: Date.now(),
          version: 2
        },
        {
          id: 'op2',
          type: 'reorder',
          layerId: 'reorder-op',
          data: { fromIndex: 0, toIndex: 1, layerOrder: [layerId2, layerId1] },
          userId: 'user2',
          timestamp: Date.now() + 50,
          version: 1
        }
      ]

      for (const op of operations) {
        layerSync.applyRemoteOperation(op)
      }

      // Should maintain consistent state
      expect(mockLayers.value).toHaveLength(2)
      expect(mockLayers.value[0].name).toBe('Updated Layer 1')
    })
  })
})