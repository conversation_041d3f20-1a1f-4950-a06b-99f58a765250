import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick, ref, readonly, computed, reactive } from 'vue'
import { useUserPresence } from '../../layers/editor/composables/useUserPresence'
import { useCursorTracking } from '../../layers/editor/composables/useCursorTracking'
import UserPresenceIndicator from '../../layers/editor/components/UserPresenceIndicator.vue'

// Mock Vue lifecycle hooks
globalThis.onMounted = vi.fn((fn) => fn())
globalThis.onUnmounted = vi.fn()
globalThis.watch = vi.fn()
globalThis.readonly = readonly
globalThis.ref = ref
globalThis.computed = computed
globalThis.reactive = reactive

// Mock WebSocket Connection
const mockWebSocketConnection = {
  connectionInfo: {
    status: 'connected',
    url: 'ws://localhost:3001',
    lastConnected: new Date(),
    reconnectAttempts: 0,
    error: null
  },
  isConnected: ref(true),
  send: vi.fn(),
  on: vi.fn(),
  off: vi.fn()
}

vi.mock('../../layers/editor/composables/useWebSocketConnection', () => ({
  useWebSocketConnection: () => mockWebSocketConnection
}))

// Mock DOM methods for cursor tracking
Object.defineProperty(Element.prototype, 'getBoundingClientRect', {
  value: vi.fn(() => ({
    left: 100,
    top: 100,
    width: 800,
    height: 600,
    right: 900,
    bottom: 700
  }))
})

describe('User Presence System', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.clearAllTimers()
  })

  afterEach(() => {
    vi.clearAllTimers()
  })

  describe('useUserPresence', () => {
    it('should initialize with correct default state', () => {
      const { presenceState, activeUsers, currentUser, totalUsers, isAtCapacity } = useUserPresence({
        workspaceId: 'test-workspace',
        projectId: 'test-project',
        maxUsers: 10
      })

      expect(presenceState.value.activeUsers).toEqual([])
      expect(presenceState.value.currentUser).toBeNull()
      expect(presenceState.value.totalUsers).toBe(0)
      expect(presenceState.value.isAtCapacity).toBe(false)
      expect(activeUsers.value).toEqual([])
      expect(currentUser.value).toBeNull()
      expect(totalUsers.value).toBe(0)
      expect(isAtCapacity.value).toBe(false)
    })

    it('should initialize current user correctly', () => {
      const { initializeCurrentUser, currentUser, users } = useUserPresence({
        workspaceId: 'test-workspace',
        projectId: 'test-project'
      })

      const userData = {
        id: 'user-123',
        name: 'John Doe',
        email: '<EMAIL>',
        avatar: 'https://example.com/avatar.jpg'
      }

      initializeCurrentUser(userData)

      expect(currentUser.value).toMatchObject({
        id: 'user-123',
        name: 'John Doe',
        email: '<EMAIL>',
        avatar: 'https://example.com/avatar.jpg',
        isActive: true,
        isTyping: false
      })
      expect(users.value.has('user-123')).toBe(true)
      expect(mockWebSocketConnection.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'presence-update',
          subType: 'user-joined'
        })
      )
    })

    it('should enforce maximum user capacity limit', () => {
      const { initializeCurrentUser, users, isAtCapacity } = useUserPresence({
        workspaceId: 'test-workspace',
        projectId: 'test-project',
        maxUsers: 2
      })

      // Initialize current user
      initializeCurrentUser({ id: 'user-1', name: 'User 1' })
      
      // Add one more user to reach capacity
      users.value.set('user-2', {
        id: 'user-2',
        name: 'User 2',
        email: '<EMAIL>',
        color: '#FF6B6B',
        position: { x: 0, y: 0 },
        cursor: { x: 0, y: 0 },
        lastSeen: new Date(),
        isActive: true
      })

      expect(isAtCapacity.value).toBe(true)
      expect(users.value.size).toBe(2)
    })

    it('should update cursor position with throttling', () => {
      vi.useFakeTimers()
      
      const { initializeCurrentUser, updateCursorPosition, currentUser } = useUserPresence({
        workspaceId: 'test-workspace',
        projectId: 'test-project',
        cursorUpdateThrottle: 100
      })

      initializeCurrentUser({ id: 'user-1', name: 'User 1' })

      // First update should go through
      updateCursorPosition(50, 75)
      expect(currentUser.value?.cursor).toEqual({ x: 50, y: 75 })
      expect(mockWebSocketConnection.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'presence-update',
          subType: 'cursor-update'
        })
      )

      mockWebSocketConnection.send.mockClear()

      // Second update within throttle period should be ignored
      vi.advanceTimersByTime(50)
      updateCursorPosition(100, 150)
      expect(mockWebSocketConnection.send).not.toHaveBeenCalled()

      // Update after throttle period should go through
      vi.advanceTimersByTime(60)
      updateCursorPosition(100, 150)
      expect(mockWebSocketConnection.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'presence-update',
          subType: 'cursor-update'
        })
      )

      vi.useRealTimers()
    })

    it('should update typing status correctly', () => {
      const { initializeCurrentUser, updateTypingStatus, currentUser } = useUserPresence({
        workspaceId: 'test-workspace',
        projectId: 'test-project'
      })

      initializeCurrentUser({ id: 'user-1', name: 'User 1' })
      
      updateTypingStatus(true)
      expect(currentUser.value?.isTyping).toBe(true)
      expect(mockWebSocketConnection.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'presence-update',
          subType: 'typing-update',
          data: expect.objectContaining({
            isTyping: true
          })
        })
      )

      updateTypingStatus(false)
      expect(currentUser.value?.isTyping).toBe(false)
    })

    it('should update current tool correctly', () => {
      const { initializeCurrentUser, updateCurrentTool, currentUser } = useUserPresence({
        workspaceId: 'test-workspace',
        projectId: 'test-project'
      })

      initializeCurrentUser({ id: 'user-1', name: 'User 1' })
      
      updateCurrentTool('crop')
      expect(currentUser.value?.currentTool).toBe('crop')
      expect(mockWebSocketConnection.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'presence-update',
          subType: 'tool-update',
          data: expect.objectContaining({
            currentTool: 'crop'
          })
        })
      )

      updateCurrentTool(null)
      expect(currentUser.value?.currentTool).toBeUndefined()
    })

    it('should generate consistent user colors', () => {
      const { getUserColor } = useUserPresence({
        workspaceId: 'test-workspace',
        projectId: 'test-project'
      })

      const color1 = getUserColor('user-123')
      const color2 = getUserColor('user-123')
      const color3 = getUserColor('user-456')

      expect(color1).toBe(color2) // Same user should get same color
      expect(color1).not.toBe(color3) // Different users should get different colors
      expect(color1).toMatch(/^#[0-9A-F]{6}$/i) // Should be valid hex color
    })

    it('should send presence heartbeat periodically', () => {
      vi.useFakeTimers()

      const { initializeCurrentUser } = useUserPresence({
        workspaceId: 'test-workspace',
        projectId: 'test-project',
        presenceUpdateInterval: 5000
      })

      initializeCurrentUser({ id: 'user-1', name: 'User 1' })
      mockWebSocketConnection.send.mockClear()

      // Fast-forward to trigger heartbeat
      vi.advanceTimersByTime(5000)

      expect(mockWebSocketConnection.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'presence-update',
          subType: 'heartbeat'
        })
      )

      vi.useRealTimers()
    })
  })

  describe('useCursorTracking', () => {
    let mockElement: HTMLElement

    beforeEach(() => {
      mockElement = document.createElement('div')
      document.body.appendChild(mockElement)
    })

    afterEach(() => {
      document.body.removeChild(mockElement)
    })

    it('should initialize cursor tracking correctly', () => {
      const mockPresence = useUserPresence({
        workspaceId: 'test-workspace',
        projectId: 'test-project'
      })

      const { initializeCursorTracking, isTracking, trackingElement } = useCursorTracking(
        mockPresence,
        { trackingElement: mockElement }
      )

      const result = initializeCursorTracking()

      expect(result).toBe(true)
      expect(isTracking.value).toBe(true)
      expect(trackingElement.value).toBe(mockElement)
    })

    it('should handle mouse movements with throttling', () => {
      vi.useFakeTimers()

      const mockPresence = useUserPresence({
        workspaceId: 'test-workspace',
        projectId: 'test-project'
      })
      mockPresence.updateCursorPosition = vi.fn()

      const { initializeCursorTracking } = useCursorTracking(
        mockPresence,
        { trackingElement: mockElement, throttleMs: 50 }
      )

      initializeCursorTracking()

      // Simulate mouse move events
      const mouseEvent1 = new MouseEvent('mousemove', { clientX: 200, clientY: 250 })
      const mouseEvent2 = new MouseEvent('mousemove', { clientX: 220, clientY: 270 })

      mockElement.dispatchEvent(mouseEvent1)
      expect(mockPresence.updateCursorPosition).toHaveBeenCalledWith(100, 150) // relative to element

      vi.clearAllMocks()

      // Second event within throttle period should be ignored
      vi.advanceTimersByTime(25)
      mockElement.dispatchEvent(mouseEvent2)
      expect(mockPresence.updateCursorPosition).not.toHaveBeenCalled()

      // Event after throttle period should go through
      vi.advanceTimersByTime(30)
      mockElement.dispatchEvent(mouseEvent2)
      expect(mockPresence.updateCursorPosition).toHaveBeenCalled()

      vi.useRealTimers()
    })

    it('should create and position remote user cursors', () => {
      const mockPresence = useUserPresence({
        workspaceId: 'test-workspace',
        projectId: 'test-project'
      })

      const { initializeCursorTracking, addUserCursor } = useCursorTracking(
        mockPresence,
        { trackingElement: mockElement }
      )

      initializeCursorTracking()

      const testUser = {
        id: 'user-123',
        name: 'Test User',
        email: '<EMAIL>',
        color: '#FF6B6B',
        position: { x: 0, y: 0 },
        cursor: { x: 150, y: 200 },
        lastSeen: new Date(),
        isActive: true
      }

      addUserCursor(testUser)

      const cursorElement = mockElement.querySelector('[data-user-id="user-123"]')
      expect(cursorElement).toBeTruthy()
      expect(cursorElement?.getAttribute('data-user-id')).toBe('user-123')
    })

    it('should remove user cursors when users leave', () => {
      const mockPresence = useUserPresence({
        workspaceId: 'test-workspace',
        projectId: 'test-project'
      })

      const { initializeCursorTracking, addUserCursor, removeUserCursor } = useCursorTracking(
        mockPresence,
        { trackingElement: mockElement }
      )

      initializeCursorTracking()

      const testUser = {
        id: 'user-123',
        name: 'Test User',
        email: '<EMAIL>',
        color: '#FF6B6B',
        position: { x: 0, y: 0 },
        cursor: { x: 150, y: 200 },
        lastSeen: new Date(),
        isActive: true
      }

      addUserCursor(testUser)
      expect(mockElement.querySelector('[data-user-id="user-123"]')).toBeTruthy()

      removeUserCursor('user-123')
      expect(mockElement.querySelector('[data-user-id="user-123"]')).toBeFalsy()
    })
  })

  describe('UserPresenceIndicator Component', () => {
    const mockPresenceState = {
      users: new Map(),
      activeUsers: [
        {
          id: 'user-1',
          name: 'John Doe',
          email: '<EMAIL>',
          color: '#FF6B6B',
          position: { x: 0, y: 0 },
          cursor: { x: 0, y: 0 },
          lastSeen: new Date(),
          isActive: true,
          isTyping: false
        },
        {
          id: 'user-2',
          name: 'Jane Smith',
          email: '<EMAIL>',
          color: '#4ECDC4',
          position: { x: 0, y: 0 },
          cursor: { x: 0, y: 0 },
          lastSeen: new Date(),
          isActive: true,
          isTyping: true
        }
      ],
      currentUser: {
        id: 'user-1',
        name: 'John Doe',
        email: '<EMAIL>',
        color: '#FF6B6B',
        position: { x: 0, y: 0 },
        cursor: { x: 0, y: 0 },
        lastSeen: new Date(),
        isActive: true,
        isTyping: false
      },
      totalUsers: 2,
      isAtCapacity: false
    }

    it('should display correct user count', () => {
      const wrapper = mount(UserPresenceIndicator, {
        props: {
          presenceState: mockPresenceState,
          maxUsers: 10
        }
      })

      expect(wrapper.text()).toContain('2 users')
      expect(wrapper.find('.users-count .count').text()).toBe('2')
    })

    it('should display user avatars correctly', () => {
      const wrapper = mount(UserPresenceIndicator, {
        props: {
          presenceState: mockPresenceState,
          showAvatars: true
        }
      })

      const avatars = wrapper.findAll('.user-avatar')
      expect(avatars).toHaveLength(2)

      // Check for typing indicator on second user
      const typingUser = avatars[1]
      expect(typingUser.classes()).toContain('user-avatar--typing')
      expect(typingUser.find('.typing-indicator').exists()).toBe(true)
    })

    it('should show capacity warning when at maximum', () => {
      const capacityState = {
        ...mockPresenceState,
        isAtCapacity: true
      }

      const wrapper = mount(UserPresenceIndicator, {
        props: {
          presenceState: capacityState,
          maxUsers: 2
        }
      })

      expect(wrapper.find('.capacity-warning').exists()).toBe(true)
      expect(wrapper.text()).toContain('(2 max)')
    })

    it('should show overflow indicator for many users', () => {
      const manyUsersState = {
        ...mockPresenceState,
        activeUsers: Array.from({ length: 8 }, (_, i) => ({
          id: `user-${i}`,
          name: `User ${i}`,
          email: `user${i}@example.com`,
          color: '#FF6B6B',
          position: { x: 0, y: 0 },
          cursor: { x: 0, y: 0 },
          lastSeen: new Date(),
          isActive: true
        }))
      }

      const wrapper = mount(UserPresenceIndicator, {
        props: {
          presenceState: manyUsersState,
          maxDisplayUsers: 5
        }
      })

      const avatars = wrapper.findAll('.user-avatar')
      expect(avatars).toHaveLength(6) // 5 users + 1 overflow indicator

      const overflowIndicator = wrapper.find('.overflow-indicator')
      expect(overflowIndicator.exists()).toBe(true)
      expect(overflowIndicator.text()).toContain('+3')
    })

    it('should display user tooltip on hover', async () => {
      const wrapper = mount(UserPresenceIndicator, {
        props: {
          presenceState: mockPresenceState,
          showTooltips: true
        }
      })

      const firstAvatar = wrapper.find('.user-avatar')
      await firstAvatar.trigger('mouseenter')
      await nextTick()

      const tooltip = wrapper.find('.user-tooltip')
      expect(tooltip.exists()).toBe(true)
      expect(tooltip.text()).toContain('John Doe')
      expect(tooltip.text()).toContain('<EMAIL>')
    })

    it('should generate correct user initials', () => {
      const wrapper = mount(UserPresenceIndicator, {
        props: {
          presenceState: mockPresenceState
        }
      })

      const component = wrapper.vm as any
      expect(component.getUserInitials('John Doe')).toBe('JD')
      expect(component.getUserInitials('Jane')).toBe('J')
      expect(component.getUserInitials('A B C D')).toBe('AB')
    })

    it('should format last seen time correctly', () => {
      const wrapper = mount(UserPresenceIndicator, {
        props: {
          presenceState: mockPresenceState
        }
      })

      const component = wrapper.vm as any
      const now = new Date()
      
      expect(component.formatLastSeen(new Date(now.getTime() - 30000))).toBe('just now')
      expect(component.formatLastSeen(new Date(now.getTime() - 120000))).toBe('2m ago')
      expect(component.formatLastSeen(new Date(now.getTime() - 7200000))).toBe('2h ago')
    })
  })

  describe('Integration Tests', () => {
    it('should integrate presence and cursor tracking correctly', () => {
      const mockPresence = useUserPresence({
        workspaceId: 'test-workspace',
        projectId: 'test-project'
      })

      mockPresence.initializeCurrentUser({
        id: 'user-1',
        name: 'Test User',
        email: '<EMAIL>'
      })

      const trackingElement = document.createElement('div')
      document.body.appendChild(trackingElement)

      const { initializeCursorTracking } = useCursorTracking(
        mockPresence,
        { trackingElement }
      )

      expect(initializeCursorTracking()).toBe(true)
      expect(mockPresence.currentUser.value).toBeTruthy()
      expect(mockPresence.activeUsers.value).toHaveLength(1)

      document.body.removeChild(trackingElement)
    })

    it('should handle complete user presence lifecycle', () => {
      const { 
        initializeCurrentUser, 
        updateCursorPosition, 
        updateTypingStatus, 
        leavePresence,
        currentUser,
        activeUsers
      } = useUserPresence({
        workspaceId: 'test-workspace',
        projectId: 'test-project'
      })

      // Initialize user
      initializeCurrentUser({
        id: 'user-1',
        name: 'Test User',
        email: '<EMAIL>'
      })

      expect(currentUser.value).toBeTruthy()
      expect(activeUsers.value).toHaveLength(1)

      // Update presence
      updateCursorPosition(100, 200)
      updateTypingStatus(true)

      expect(currentUser.value?.cursor).toEqual({ x: 100, y: 200 })
      expect(currentUser.value?.isTyping).toBe(true)

      // Leave presence
      leavePresence()

      expect(currentUser.value).toBeNull()
      expect(activeUsers.value).toHaveLength(0)
    })
  })
})