import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ref, computed } from 'vue'
import BookDashboard from '../../layers/writer/pages/book/dashboard.vue'

// Mock the dependencies (now handled by global mocks below)

vi.mock('nuxt/app', () => ({
  useRouter: () => ({
    push: vi.fn()
  }),
  onMounted: vi.fn((callback) => callback())
}))

// Mock the toaster composable
vi.mock('../../layers/core/composables/toaster', () => ({
  useToaster: vi.fn(() => ({
    show: vi.fn(),
    showError: vi.fn(),
    showSuccess: vi.fn(),
    showWarning: vi.fn(),
    showInfo: vi.fn(),
    clear: vi.fn(),
    clearAll: vi.fn()
  }))
}))

// Make useToaster available globally (for Nuxt auto-import)
globalThis.useToaster = vi.fn(() => ({
  show: vi.fn(),
  showError: vi.fn(),
  showSuccess: vi.fn(),
  showWarning: vi.fn(),
  showInfo: vi.fn(),
  clear: vi.fn(),
  clearAll: vi.fn()
}))

// Create a persistent mock object to track calls correctly using actual Vue reactive APIs
const mockBooks = ref([
  {
    id: 'book-1',
    title: 'Book 1',
    description: 'Description 1',
    authors: ['Author 1'],
    category: ['Fiction'],
    format: 'EBOOK',
    status: 'WRITING',
    created_at: new Date('2023-01-01'),
    updated_at: new Date('2023-01-15')
  },
  {
    id: 'book-2',
    title: 'Book 2',
    description: 'Description 2',
    authors: ['Author 2'],
    category: ['Non-Fiction'],
    format: 'PAPERBACK',
    status: 'PLANNING',
    created_at: new Date('2023-02-01'),
    updated_at: new Date('2023-02-15')
  }
])

const mockUseBook = {
  books: mockBooks,
  getBooks: vi.fn().mockResolvedValue([]),
  handleEditBook: vi.fn(),
  handleDeleteBook: vi.fn(),
  navigateToCreateBook: vi.fn()
}

globalThis.useBook = vi.fn(() => mockUseBook)

// Mock Vue composables with actual Vue APIs
// Make Vue reactivity functions available globally for the component
globalThis.ref = ref
globalThis.computed = computed
globalThis.onMounted = vi.fn((callback) => callback())

// Dashboard components are now handled through global component registration in beforeEach

describe('BookDashboard component', () => {
  let wrapper: any
  
  beforeEach(() => {
    // Clear all mocks and reset reactive state
    vi.clearAllMocks()
    // Reset books to initial state for each test
    mockBooks.value = [
      {
        id: 'book-1',
        title: 'Book 1',
        description: 'Description 1',
        authors: ['Author 1'],
        category: ['Fiction'],
        format: 'EBOOK',
        status: 'WRITING',
        created_at: new Date('2023-01-01'),
        updated_at: new Date('2023-01-15')
      },
      {
        id: 'book-2',
        title: 'Book 2',
        description: 'Description 2',
        authors: ['Author 2'],
        category: ['Non-Fiction'],
        format: 'PAPERBACK',
        status: 'PLANNING',
        created_at: new Date('2023-02-01'),
        updated_at: new Date('2023-02-15')
      }
    ]
    // Mount with global components to resolve the dashboard components
    wrapper = mount(BookDashboard, {
      global: {
        components: {
          BookDashboardHeader: {
            name: 'BookDashboardHeader',
            emits: ['create-book'],
            template: '<div data-testid="dashboard-header" @click="$emit(\'create-book\')" role="button">Dashboard Header</div>'
          },
          BookDashboardStats: {
            name: 'BookDashboardStats',
            props: ['books'],
            template: '<div data-testid="dashboard-stats">Dashboard Stats ({{ books?.length || 0 }} books)</div>'
          },
          BookDashboardFilters: {
            name: 'BookDashboardFilters',
            props: ['activeFilter'],
            emits: ['filter-changed'],
            template: `<div data-testid="dashboard-filters">
              <button data-testid="filter-recent" :class="{ active: activeFilter === 'recent' }" @click="$emit('filter-changed', 'recent')">Recent</button>
              <button data-testid="filter-planning" :class="{ active: activeFilter === 'planning' }" @click="$emit('filter-changed', 'planning')">Planning</button>
              <button data-testid="filter-writing" :class="{ active: activeFilter === 'writing' }" @click="$emit('filter-changed', 'writing')">Writing</button>
            </div>`
          },
          BookDashboardGrid: {
            name: 'BookDashboardGrid',
            props: ['books', 'loading'],
            emits: ['create-book', 'edit-book', 'delete-book'],
            template: `<div data-testid="dashboard-grid">
              <div v-if="loading" data-testid="loading-state">Loading...</div>
              <div v-else-if="!books || books.length === 0" data-testid="empty-state">
                <button data-testid="create-book-btn" @click="$emit('create-book')">Create Book</button>
              </div>
              <div v-else data-testid="books-list">
                <div v-for="book in books" :key="book.id" data-testid="book-item">
                  <span>{{ book.title }}</span>
                  <button :data-testid="'edit-' + book.id" @click="$emit('edit-book', book.id)">Edit</button>
                  <button :data-testid="'delete-' + book.id" @click="$emit('delete-book', book.id)">Delete</button>
                </div>
              </div>
            </div>`
          }
        }
      }
    })
  })
  
  afterEach(() => {
    // Clean up wrapper and any side effects
    if (wrapper) {
      wrapper.unmount()
      wrapper = null
    }
    // Additional cleanup can be added here if needed
  })
  
  it('should render the component', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('[data-testid="dashboard-header"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="dashboard-stats"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="dashboard-filters"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="dashboard-grid"]').exists()).toBe(true)
  })
  
  it('should load books on mount', () => {
    // Test that the component initializes properly
    expect(wrapper.exists()).toBe(true)
  })
  
  it('should display book cards for each book', () => {
    // The decomposed grid component handles displaying books
    expect(wrapper.find('[data-testid="dashboard-grid"]').exists()).toBe(true)
  })
  
  it('should call navigateToCreateBook when create button is clicked', async () => {
    const headerComponent = wrapper.findComponent({ name: 'BookDashboardHeader' })
    expect(headerComponent.exists()).toBe(true)
    
    // Simulate the event emission from the child component
    await headerComponent.vm.$emit('create-book')
    expect(mockUseBook.navigateToCreateBook).toHaveBeenCalled()
  })
  
  it('should handle filter change events', async () => {
    const filterButton = wrapper.find('[data-testid="filter-planning"]')
    expect(filterButton.exists()).toBe(true)
    
    await filterButton.trigger('click')
    // Check that the activeFilter reactive value changes
    expect(wrapper.vm.activeBookFilter).toBe('planning')
  })
  
  it('should handle edit book events from grid', async () => {
    const editButton = wrapper.find('[data-testid="edit-book-1"]')
    if (editButton.exists()) {
      await editButton.trigger('click')
      expect(mockUseBook.handleEditBook).toHaveBeenCalledWith('book-1')
    }
  })
  
  it('should handle delete book events from grid', async () => {
    const deleteButton = wrapper.find('[data-testid="delete-book-1"]')
    if (deleteButton.exists()) {
      await deleteButton.trigger('click')
      expect(mockUseBook.handleDeleteBook).toHaveBeenCalledWith('book-1')
    }
  })
  
  it('should filter books based on active filter', async () => {
    // Test that filteredBooks computed property works correctly
    expect(wrapper.vm.filteredBooks).toBeDefined()
    expect(Array.isArray(wrapper.vm.filteredBooks)).toBe(true)
  })
  
  it('should pass correct props to components', () => {
    const statsComponent = wrapper.findComponent({ name: 'BookDashboardStats' })
    const filtersComponent = wrapper.findComponent({ name: 'BookDashboardFilters' })
    const gridComponent = wrapper.findComponent({ name: 'BookDashboardGrid' })
    
    expect(statsComponent.exists()).toBe(true)
    expect(statsComponent.props('books')).toBeDefined()
    
    expect(filtersComponent.exists()).toBe(true)
    // The activeFilter prop is passed as a reactive value
    expect(filtersComponent.props('activeFilter')).toBe('recent')
    
    expect(gridComponent.exists()).toBe(true)
    expect(gridComponent.props('books')).toBeDefined()
    expect(gridComponent.props('loading')).toBeDefined()
  })
})
