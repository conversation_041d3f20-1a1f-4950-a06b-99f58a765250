import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

// Mock Nuxt utilities
globalThis.defineEventHandler = vi.fn((handler) => handler)
globalThis.getMethod = vi.fn()
globalThis.readBody = vi.fn()
globalThis.createError = vi.fn((config) => new Error(config.statusMessage))

// Mock useTools
const mockUseGenerateText = vi.fn()
const mockUseTools = vi.fn(() => ({
  useGenerateText: mockUseGenerateText
}))

vi.mock('../../../layers/core/composables/useTools', () => ({
  useTools: mockUseTools
}))

// Import the handler after mocking
import textEditorHandler from '../../../layers/core/server/api/agency/text-editor'

describe('Text Editor Agency API', () => {
  let mockEvent: any

  beforeEach(() => {
    vi.clearAllMocks()
    mockEvent = {
      node: {
        req: { method: 'POST' },
        res: {}
      }
    }
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Connection Handling', () => {
    it('should handle connect request successfully', async () => {
      vi.mocked(getMethod).mockReturnValue('POST')
      vi.mocked(readBody).mockResolvedValue({
        type: 'connect',
        sessionId: 'test-session-123',
        userId: 'user-456',
        apiKey: 'test-api-key'
      })

      const result = await textEditorHandler(mockEvent)

      expect(result).toEqual({
        success: true,
        sessionId: 'test-session-123',
        message: 'Connected to Text Editor Agency. I\'m here to help you with writing, editing, and content creation tasks.'
      })
    })

    it('should generate new session ID if not provided', async () => {
      vi.mocked(getMethod).mockReturnValue('POST')
      vi.mocked(readBody).mockResolvedValue({
        type: 'connect'
      })

      const result = await textEditorHandler(mockEvent)

      expect(result.success).toBe(true)
      expect(result.sessionId).toBeDefined()
      expect(typeof result.sessionId).toBe('string')
      expect(result.sessionId.length).toBeGreaterThan(0)
    })

    it('should handle connect with existing session', async () => {
      const sessionId = 'existing-session-789'
      
      // First connection
      vi.mocked(getMethod).mockReturnValue('POST')
      vi.mocked(readBody).mockResolvedValue({
        type: 'connect',
        sessionId
      })

      const firstResult = await textEditorHandler(mockEvent)
      expect(firstResult.sessionId).toBe(sessionId)

      // Second connection with same session ID
      const secondResult = await textEditorHandler(mockEvent)
      expect(secondResult.sessionId).toBe(sessionId)
    })
  })

  describe('Message Processing', () => {
    const sessionId = 'test-session-123'

    beforeEach(async () => {
      // Establish a session first
      vi.mocked(getMethod).mockReturnValue('POST')
      vi.mocked(readBody).mockResolvedValue({
        type: 'connect',
        sessionId
      })
      await textEditorHandler(mockEvent)
    })

    it('should process message successfully', async () => {
      const userMessage = 'Help me write a compelling introduction for my novel'
      const assistantResponse = 'I\'d be happy to help you craft a compelling introduction for your novel!'

      mockUseGenerateText.mockResolvedValue({
        content: assistantResponse
      })

      vi.mocked(readBody).mockResolvedValue({
        type: 'message',
        message: userMessage,
        sessionId
      })

      const result = await textEditorHandler(mockEvent)

      expect(result).toEqual({
        success: true,
        sessionId,
        message: assistantResponse,
        timestamp: expect.any(String)
      })

      expect(mockUseGenerateText).toHaveBeenCalledWith(
        expect.stringContaining(userMessage),
        {
          maxTokens: 1024,
          temperature: 0.7,
          model: 'claude-3-sonnet-20240229'
        }
      )
    })

    it('should handle message with conversation context', async () => {
      const firstMessage = 'I\'m writing a mystery novel'
      const firstResponse = 'Great! Mystery novels are exciting to write.'
      const secondMessage = 'What are some good opening techniques?'
      const secondResponse = 'Here are some effective opening techniques for mysteries...'

      // First message
      mockUseGenerateText.mockResolvedValueOnce({
        content: firstResponse
      })

      vi.mocked(readBody).mockResolvedValue({
        type: 'message',
        message: firstMessage,
        sessionId
      })

      await textEditorHandler(mockEvent)

      // Second message with context
      mockUseGenerateText.mockResolvedValueOnce({
        content: secondResponse
      })

      vi.mocked(readBody).mockResolvedValue({
        type: 'message',
        message: secondMessage,
        sessionId
      })

      const result = await textEditorHandler(mockEvent)

      expect(result.success).toBe(true)
      expect(result.message).toBe(secondResponse)

      // Verify context was included in the prompt
      const lastCall = mockUseGenerateText.mock.calls[1]
      expect(lastCall[0]).toContain('Previous conversation:')
      expect(lastCall[0]).toContain(firstMessage)
      expect(lastCall[0]).toContain(firstResponse)
      expect(lastCall[0]).toContain(secondMessage)
    })

    it('should handle empty or invalid messages', async () => {
      vi.mocked(readBody).mockResolvedValue({
        type: 'message',
        message: '',
        sessionId
      })

      const result = await textEditorHandler(mockEvent)

      expect(result).toEqual({
        success: false,
        error: 'Invalid message',
        timestamp: expect.any(String)
      })
    })

    it('should handle missing message field', async () => {
      vi.mocked(readBody).mockResolvedValue({
        type: 'message',
        sessionId
      })

      const result = await textEditorHandler(mockEvent)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Invalid message')
    })

    it('should handle AI generation errors', async () => {
      const userMessage = 'Help me write something'
      const aiError = new Error('AI service unavailable')

      mockUseGenerateText.mockRejectedValue(aiError)

      vi.mocked(readBody).mockResolvedValue({
        type: 'message',
        message: userMessage,
        sessionId
      })

      const result = await textEditorHandler(mockEvent)

      expect(result).toEqual({
        success: false,
        sessionId,
        error: 'Error processing your request: AI service unavailable',
        timestamp: expect.any(String)
      })
    })

    it('should handle fallback when AI response is empty', async () => {
      const userMessage = 'Help me write something'

      mockUseGenerateText.mockResolvedValue({
        content: null
      })

      vi.mocked(readBody).mockResolvedValue({
        type: 'message',
        message: userMessage,
        sessionId
      })

      const result = await textEditorHandler(mockEvent)

      expect(result.success).toBe(true)
      expect(result.message).toBe('I apologize, but I encountered an issue generating a response.')
    })

    it('should limit conversation context to last 10 messages', async () => {
      // Add 15 messages to history to test the limit
      for (let i = 0; i < 15; i++) {
        mockUseGenerateText.mockResolvedValueOnce({
          content: `Response ${i}`
        })

        vi.mocked(readBody).mockResolvedValue({
          type: 'message',
          message: `Message ${i}`,
          sessionId
        })

        await textEditorHandler(mockEvent)
      }

      // Add one more message and check context
      mockUseGenerateText.mockResolvedValueOnce({
        content: 'Final response'
      })

      vi.mocked(readBody).mockResolvedValue({
        type: 'message',
        message: 'Final message',
        sessionId
      })

      await textEditorHandler(mockEvent)

      // Check that only last 10 conversation pairs were included
      const lastCall = mockUseGenerateText.mock.calls[15]
      const prompt = lastCall[0]
      
      // Should contain messages 6-15 (10 messages) but not 0-5
      expect(prompt).toContain('Message 6')
      expect(prompt).toContain('Message 14')
      expect(prompt).not.toContain('Message 0')
      expect(prompt).not.toContain('Message 5')
    })
  })

  describe('History Management', () => {
    const sessionId = 'test-session-123'

    beforeEach(async () => {
      // Establish a session with some history
      vi.mocked(getMethod).mockReturnValue('POST')
      vi.mocked(readBody).mockResolvedValue({
        type: 'connect',
        sessionId
      })
      await textEditorHandler(mockEvent)

      // Add some messages
      mockUseGenerateText.mockResolvedValue({
        content: 'Test response'
      })

      vi.mocked(readBody).mockResolvedValue({
        type: 'message',
        message: 'Test message',
        sessionId
      })
      await textEditorHandler(mockEvent)
    })

    it('should clear history successfully', async () => {
      vi.mocked(readBody).mockResolvedValue({
        type: 'clear_history',
        sessionId
      })

      const result = await textEditorHandler(mockEvent)

      expect(result).toEqual({
        success: true,
        sessionId,
        message: 'Conversation history cleared.'
      })
    })

    it('should get history successfully', async () => {
      vi.mocked(readBody).mockResolvedValue({
        type: 'get_history',
        sessionId
      })

      const result = await textEditorHandler(mockEvent)

      expect(result.success).toBe(true)
      expect(result.sessionId).toBe(sessionId)
      expect(Array.isArray(result.history)).toBe(true)
      expect(result.history.length).toBeGreaterThan(0)
      expect(result.history[0]).toEqual({
        role: 'user',
        content: 'Test message',
        timestamp: expect.any(String)
      })
      expect(result.history[1]).toEqual({
        role: 'assistant',
        content: 'Test response',
        timestamp: expect.any(String)
      })
    })

    it('should verify history is actually cleared', async () => {
      // Clear history
      vi.mocked(readBody).mockResolvedValue({
        type: 'clear_history',
        sessionId
      })
      await textEditorHandler(mockEvent)

      // Get history to verify it's empty
      vi.mocked(readBody).mockResolvedValue({
        type: 'get_history',
        sessionId
      })

      const result = await textEditorHandler(mockEvent)

      expect(result.history).toEqual([])
    })
  })

  describe('Session Management', () => {
    it('should handle multiple concurrent sessions', async () => {
      const session1 = 'session-1'
      const session2 = 'session-2'

      vi.mocked(getMethod).mockReturnValue('POST')

      // Connect session 1
      vi.mocked(readBody).mockResolvedValue({
        type: 'connect',
        sessionId: session1,
        userId: 'user-1'
      })
      const result1 = await textEditorHandler(mockEvent)

      // Connect session 2
      vi.mocked(readBody).mockResolvedValue({
        type: 'connect',
        sessionId: session2,
        userId: 'user-2'
      })
      const result2 = await textEditorHandler(mockEvent)

      expect(result1.sessionId).toBe(session1)
      expect(result2.sessionId).toBe(session2)

      // Send message to session 1
      mockUseGenerateText.mockResolvedValue({
        content: 'Response for session 1'
      })

      vi.mocked(readBody).mockResolvedValue({
        type: 'message',
        message: 'Message for session 1',
        sessionId: session1
      })
      const msg1Result = await textEditorHandler(mockEvent)

      // Send message to session 2
      mockUseGenerateText.mockResolvedValue({
        content: 'Response for session 2'
      })

      vi.mocked(readBody).mockResolvedValue({
        type: 'message',
        message: 'Message for session 2',
        sessionId: session2
      })
      const msg2Result = await textEditorHandler(mockEvent)

      expect(msg1Result.message).toBe('Response for session 1')
      expect(msg2Result.message).toBe('Response for session 2')

      // Verify sessions are independent
      vi.mocked(readBody).mockResolvedValue({
        type: 'get_history',
        sessionId: session1
      })
      const history1 = await textEditorHandler(mockEvent)

      vi.mocked(readBody).mockResolvedValue({
        type: 'get_history',
        sessionId: session2
      })
      const history2 = await textEditorHandler(mockEvent)

      expect(history1.history[0].content).toBe('Message for session 1')
      expect(history2.history[0].content).toBe('Message for session 2')
    })

    it('should handle requests with non-existent session', async () => {
      vi.mocked(getMethod).mockReturnValue('POST')
      vi.mocked(readBody).mockResolvedValue({
        type: 'message',
        message: 'Test message',
        sessionId: 'non-existent-session'
      })

      // Should create new session and process message
      mockUseGenerateText.mockResolvedValue({
        content: 'Response for new session'
      })

      const result = await textEditorHandler(mockEvent)

      expect(result.success).toBe(true)
      expect(result.sessionId).toBe('non-existent-session')
      expect(result.message).toBe('Response for new session')
    })
  })

  describe('HTTP Method Handling', () => {
    it('should handle OPTIONS requests for CORS', async () => {
      vi.mocked(getMethod).mockReturnValue('OPTIONS')

      const result = await textEditorHandler(mockEvent)

      expect(result).toEqual({})
    })

    it('should reject GET requests', async () => {
      vi.mocked(getMethod).mockReturnValue('GET')

      await expect(textEditorHandler(mockEvent)).rejects.toThrow('Method not allowed')
      expect(createError).toHaveBeenCalledWith({
        statusCode: 405,
        statusMessage: 'Method not allowed'
      })
    })

    it('should reject PUT requests', async () => {
      vi.mocked(getMethod).mockReturnValue('PUT')

      await expect(textEditorHandler(mockEvent)).rejects.toThrow('Method not allowed')
    })

    it('should reject DELETE requests', async () => {
      vi.mocked(getMethod).mockReturnValue('DELETE')

      await expect(textEditorHandler(mockEvent)).rejects.toThrow('Method not allowed')
    })
  })

  describe('Error Handling', () => {
    it('should handle unknown request types', async () => {
      vi.mocked(getMethod).mockReturnValue('POST')
      vi.mocked(readBody).mockResolvedValue({
        type: 'unknown_type',
        sessionId: 'test-session'
      })

      const result = await textEditorHandler(mockEvent)

      expect(result).toEqual({
        success: false,
        error: 'Unknown request type',
        timestamp: expect.any(String)
      })
    })

    it('should handle malformed request body', async () => {
      vi.mocked(getMethod).mockReturnValue('POST')
      vi.mocked(readBody).mockRejectedValue(new Error('Invalid JSON'))

      const result = await textEditorHandler(mockEvent)

      expect(result).toEqual({
        success: false,
        error: 'Invalid JSON',
        timestamp: expect.any(String)
      })
    })

    it('should handle missing request body', async () => {
      vi.mocked(getMethod).mockReturnValue('POST')
      vi.mocked(readBody).mockResolvedValue(null)

      const result = await textEditorHandler(mockEvent)

      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
    })

    it('should handle useTools import errors', async () => {
      vi.mocked(getMethod).mockReturnValue('POST')
      vi.mocked(readBody).mockResolvedValue({
        type: 'connect',
        sessionId: 'test-session'
      })

      // First establish session
      await textEditorHandler(mockEvent)

      // Mock useTools to throw error
      mockUseTools.mockImplementation(() => {
        throw new Error('useTools not available')
      })

      vi.mocked(readBody).mockResolvedValue({
        type: 'message',
        message: 'Test message',
        sessionId: 'test-session'
      })

      const result = await textEditorHandler(mockEvent)

      expect(result.success).toBe(false)
      expect(result.error).toContain('useTools not available')
    })
  })

  describe('Performance and Memory', () => {
    it('should maintain session data in memory', async () => {
      const sessionId = 'persistent-session'
      
      vi.mocked(getMethod).mockReturnValue('POST')

      // Connect
      vi.mocked(readBody).mockResolvedValue({
        type: 'connect',
        sessionId,
        userId: 'test-user'
      })
      await textEditorHandler(mockEvent)

      // Add multiple messages
      for (let i = 0; i < 5; i++) {
        mockUseGenerateText.mockResolvedValueOnce({
          content: `Response ${i}`
        })

        vi.mocked(readBody).mockResolvedValue({
          type: 'message',
          message: `Message ${i}`,
          sessionId
        })
        await textEditorHandler(mockEvent)
      }

      // Verify all messages are in history
      vi.mocked(readBody).mockResolvedValue({
        type: 'get_history',
        sessionId
      })

      const result = await textEditorHandler(mockEvent)

      expect(result.history).toHaveLength(10) // 5 user + 5 assistant
      expect(result.history[0].content).toBe('Message 0')
      expect(result.history[9].content).toBe('Response 4')
    })

    it('should handle large conversation history efficiently', async () => {
      const sessionId = 'large-session'
      
      vi.mocked(getMethod).mockReturnValue('POST')

      // Connect
      vi.mocked(readBody).mockResolvedValue({
        type: 'connect',
        sessionId
      })
      await textEditorHandler(mockEvent)

      // Add 50 message pairs (100 total messages)
      for (let i = 0; i < 50; i++) {
        mockUseGenerateText.mockResolvedValueOnce({
          content: `Response ${i}`
        })

        vi.mocked(readBody).mockResolvedValue({
          type: 'message',
          message: `Message ${i}`,
          sessionId
        })
        await textEditorHandler(mockEvent)
      }

      // Verify history contains all messages
      vi.mocked(readBody).mockResolvedValue({
        type: 'get_history',
        sessionId
      })

      const result = await textEditorHandler(mockEvent)

      expect(result.history).toHaveLength(100)
      
      // Verify context is still limited to last 10 when sending new message
      mockUseGenerateText.mockResolvedValueOnce({
        content: 'Final response'
      })

      vi.mocked(readBody).mockResolvedValue({
        type: 'message',
        message: 'Final message',
        sessionId
      })

      await textEditorHandler(mockEvent)

      const lastPrompt = mockUseGenerateText.mock.calls[50][0]
      // Should not contain early messages due to 10-message context limit
      expect(lastPrompt).not.toContain('Message 0')
      expect(lastPrompt).toContain('Message 40') // Should contain recent messages
    })
  })
})