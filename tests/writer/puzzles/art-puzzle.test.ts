import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'

// Mock the generator function
vi.mock('../../../layers/writer/components/global/book/puzzles/art-puzzle/generator', () => ({
  generateArtPuzzle: vi.fn()
}))

// Mock global composables
globalThis.ref = vi.fn((value) => ({ value }))
globalThis.computed = vi.fn((fn) => ({ value: fn() }))
globalThis.watch = vi.fn()
globalThis.defineEmits = vi.fn(() => vi.fn())
globalThis.defineProps = vi.fn()

describe('Art Puzzle Components', () => {
  describe('Art Puzzle Generator', () => {
    it('should generate valid art puzzle', async () => {
      const { generateArtPuzzle } = await import('../../../layers/writer/components/global/book/puzzles/art-puzzle/generator')
      
      const settings = {
        artStyle: 'renaissance' as const,
        difficulty: 'medium' as const,
        pieceCount: 50,
        theme: 'classical',
        showReference: true,
        enableRotation: false,
        puzzleShape: 'rectangle' as const,
        imageUrl: 'https://example.com/art.jpg',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-art-puzzle-1',
        type: 'art-puzzle',
        artStyle: 'renaissance',
        difficulty: 'medium' as const,
        pieces: Array.from({ length: 50 }, (_, i) => ({
          id: i + 1,
          shape: 'rectangle',
          position: { x: i % 10, y: Math.floor(i / 10) },
          imageSegment: `data:image/png;base64,segment${i}`,
          rotation: 0,
          isPlaced: false
        })),
        solution: Array.from({ length: 50 }, (_, i) => ({
          id: i + 1,
          correctPosition: { x: i % 10, y: Math.floor(i / 10) },
          correctRotation: 0
        })),
        metadata: {
          artist: 'Leonardo da Vinci',
          title: 'The Mona Lisa',
          year: '1503-1519',
          description: 'Famous Renaissance portrait',
          artMovement: 'Renaissance'
        },
        instructions: [
          'Drag pieces to their correct positions',
          'Look for matching colors and patterns',
          'Use the reference image if needed'
        ],
        hints: [
          'Start with corner pieces',
          'Group pieces by color',
          'Look for distinctive features'
        ],
        stats: {
          pieceCount: 50,
          completionTime: 1800,
          complexity: 7.5,
          educationalValue: 8,
          estimatedSolveTime: 1800
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateArtPuzzle).mockResolvedValue(mockResult)
      
      const result = await generateArtPuzzle(settings)
      
      expect(result).toBeDefined()
      expect(result.type).toBe('art-puzzle')
      expect(result.pieces).toHaveLength(50)
      expect(result.solution).toHaveLength(50)
      expect(result.metadata.artist).toBe('Leonardo da Vinci')
      expect(result.stats.pieceCount).toBe(50)
    })

    it('should handle different art styles', async () => {
      const { generateArtPuzzle } = await import('../../../layers/writer/components/global/book/puzzles/art-puzzle/generator')
      
      const artStyles = ['renaissance', 'impressionist', 'modern', 'abstract', 'classical'] as const
      
      for (const artStyle of artStyles) {
        const settings = {
          artStyle,
          difficulty: 'medium' as const,
          pieceCount: 25,
          theme: artStyle,
          showReference: true,
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00',
          fontSize: 'medium' as const,
          printLayout: 'compact' as const
        }

        const mockResult = {
          id: `test-art-puzzle-${artStyle}`,
          type: 'art-puzzle',
          artStyle,
          difficulty: 'medium' as const,
          pieces: Array.from({ length: 25 }, (_, i) => ({
            id: i + 1,
            shape: 'rectangle',
            position: { x: i % 5, y: Math.floor(i / 5) },
            imageSegment: `data:image/png;base64,segment${i}`,
            rotation: 0,
            isPlaced: false
          })),
          solution: Array.from({ length: 25 }, (_, i) => ({
            id: i + 1,
            correctPosition: { x: i % 5, y: Math.floor(i / 5) },
            correctRotation: 0
          })),
          metadata: {
            artist: artStyle === 'renaissance' ? 'Michelangelo' : 
                   artStyle === 'impressionist' ? 'Monet' :
                   artStyle === 'modern' ? 'Picasso' :
                   artStyle === 'abstract' ? 'Kandinsky' : 'Unknown',
            title: `${artStyle} Masterpiece`,
            artMovement: artStyle
          },
          stats: {
            pieceCount: 25,
            complexity: artStyle === 'abstract' ? 8.5 : artStyle === 'modern' ? 7.0 : 6.0,
            educationalValue: 7,
            estimatedSolveTime: 900
          },
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00'
        }

        vi.mocked(generateArtPuzzle).mockResolvedValue(mockResult)
        
        const result = await generateArtPuzzle(settings)
        expect(result.artStyle).toBe(artStyle)
        expect(result.metadata.artMovement).toBe(artStyle)
      }
    })

    it('should handle different difficulty levels', async () => {
      const { generateArtPuzzle } = await import('../../../layers/writer/components/global/book/puzzles/art-puzzle/generator')
      
      const difficulties = ['easy', 'medium', 'hard'] as const
      const pieceCounts = { easy: 16, medium: 36, hard: 64 }
      
      for (const difficulty of difficulties) {
        const settings = {
          artStyle: 'classical' as const,
          difficulty,
          pieceCount: pieceCounts[difficulty],
          theme: 'classical',
          showReference: difficulty !== 'hard',
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00',
          fontSize: 'medium' as const,
          printLayout: 'compact' as const
        }

        const expectedPieceCount = pieceCounts[difficulty]
        
        const mockResult = {
          id: `test-art-puzzle-${difficulty}`,
          type: 'art-puzzle',
          artStyle: 'classical',
          difficulty,
          pieces: Array.from({ length: expectedPieceCount }, (_, i) => ({
            id: i + 1,
            shape: 'rectangle',
            position: { x: i % Math.sqrt(expectedPieceCount), y: Math.floor(i / Math.sqrt(expectedPieceCount)) },
            imageSegment: `data:image/png;base64,segment${i}`,
            rotation: 0,
            isPlaced: false
          })),
          solution: Array.from({ length: expectedPieceCount }, (_, i) => ({
            id: i + 1,
            correctPosition: { x: i % Math.sqrt(expectedPieceCount), y: Math.floor(i / Math.sqrt(expectedPieceCount)) },
            correctRotation: 0
          })),
          metadata: {
            artist: 'Classical Master',
            title: 'Classical Artwork'
          },
          stats: {
            pieceCount: expectedPieceCount,
            complexity: difficulty === 'easy' ? 4.0 : difficulty === 'medium' ? 6.5 : 9.0,
            educationalValue: 7,
            estimatedSolveTime: difficulty === 'easy' ? 600 : difficulty === 'medium' ? 1200 : 2400
          },
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00'
        }

        vi.mocked(generateArtPuzzle).mockResolvedValue(mockResult)
        
        const result = await generateArtPuzzle(settings)
        expect(result.difficulty).toBe(difficulty)
        expect(result.pieces).toHaveLength(expectedPieceCount)
        expect(result.stats.complexity).toBeGreaterThan(0)
      }
    })

    it('should handle rotation enabled puzzles', async () => {
      const { generateArtPuzzle } = await import('../../../layers/writer/components/global/book/puzzles/art-puzzle/generator')
      
      const settings = {
        artStyle: 'modern' as const,
        difficulty: 'hard' as const,
        pieceCount: 36,
        theme: 'modern',
        showReference: false,
        enableRotation: true,
        puzzleShape: 'irregular' as const,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-art-puzzle-rotation',
        type: 'art-puzzle',
        artStyle: 'modern',
        difficulty: 'hard' as const,
        pieces: Array.from({ length: 36 }, (_, i) => ({
          id: i + 1,
          shape: 'irregular',
          position: { x: i % 6, y: Math.floor(i / 6) },
          imageSegment: `data:image/png;base64,segment${i}`,
          rotation: Math.floor(Math.random() * 4) * 90, // Random initial rotation
          isPlaced: false
        })),
        solution: Array.from({ length: 36 }, (_, i) => ({
          id: i + 1,
          correctPosition: { x: i % 6, y: Math.floor(i / 6) },
          correctRotation: 0
        })),
        metadata: {
          artist: 'Pablo Picasso',
          title: 'Les Demoiselles d\'Avignon',
          year: '1907',
          artMovement: 'Cubism'
        },
        instructions: [
          'Drag pieces to their correct positions',
          'Right-click to rotate pieces',
          'Each piece may need to be rotated to fit correctly'
        ],
        stats: {
          pieceCount: 36,
          complexity: 9.5, // Higher complexity due to rotation
          educationalValue: 8,
          estimatedSolveTime: 2700
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateArtPuzzle).mockResolvedValue(mockResult)
      
      const result = await generateArtPuzzle(settings)
      expect(result.pieces.some(piece => piece.rotation !== 0)).toBe(true)
      expect(result.instructions).toContain('Right-click to rotate pieces')
      expect(result.stats.complexity).toBeGreaterThan(9)
    })
  })

  describe('Art Puzzle Components Mock Tests', () => {
    let mockCustomizeComponent: any
    let mockPreviewComponent: any
    let mockSolutionComponent: any

    beforeEach(() => {
      mockCustomizeComponent = {
        template: `
          <div data-testid="art-puzzle-customize">
            <select v-model="artStyle">
              <option value="renaissance">Renaissance</option>
              <option value="impressionist">Impressionist</option>
              <option value="modern">Modern</option>
              <option value="abstract">Abstract</option>
              <option value="classical">Classical</option>
            </select>
            <select v-model="difficulty">
              <option value="easy">Easy (16 pieces)</option>
              <option value="medium">Medium (36 pieces)</option>
              <option value="hard">Hard (64 pieces)</option>
            </select>
            <input v-model="pieceCount" type="number" min="16" max="100" />
            <label>
              <input type="checkbox" v-model="showReference" />
              Show Reference Image
            </label>
            <label>
              <input type="checkbox" v-model="enableRotation" />
              Enable Piece Rotation
            </label>
            <select v-model="puzzleShape">
              <option value="rectangle">Rectangle</option>
              <option value="square">Square</option>
              <option value="irregular">Irregular</option>
            </select>
            <input v-model="imageUrl" type="url" placeholder="Custom image URL" />
          </div>
        `,
        props: ['modelValue'],
        emits: ['update:modelValue'],
        data() {
          return {
            artStyle: 'renaissance',
            difficulty: 'medium',
            pieceCount: 36,
            showReference: true,
            enableRotation: false,
            puzzleShape: 'rectangle',
            imageUrl: ''
          }
        }
      }

      mockPreviewComponent = {
        template: `
          <div data-testid="art-puzzle-preview">
            <div class="puzzle-header">
              <h3>{{ metadata?.title || 'Art Puzzle' }}</h3>
              <p v-if="metadata?.artist">by {{ metadata.artist }}</p>
              <p v-if="metadata?.year">{{ metadata.year }}</p>
            </div>
            <div class="puzzle-workspace">
              <div v-if="showReference" class="reference-image">
                <img :src="referenceImage" alt="Reference" />
              </div>
              <div class="puzzle-area">
                <div class="piece-container">
                  <div v-for="piece in pieces" :key="piece.id" 
                       class="puzzle-piece"
                       :style="{ 
                         transform: 'rotate(' + piece.rotation + 'deg)',
                         left: piece.position.x * 50 + 'px',
                         top: piece.position.y * 50 + 'px'
                       }">
                    <img :src="piece.imageSegment" :alt="'Piece ' + piece.id" />
                  </div>
                </div>
              </div>
            </div>
            <div class="puzzle-info">
              <p>Pieces: {{ stats?.pieceCount || 0 }}</p>
              <p>Difficulty: {{ difficulty }}</p>
              <p>Art Style: {{ artStyle }}</p>
              <p v-if="metadata?.artMovement">Movement: {{ metadata.artMovement }}</p>
            </div>
            <div v-if="instructions" class="instructions">
              <h4>Instructions</h4>
              <ul>
                <li v-for="instruction in instructions" :key="instruction">{{ instruction }}</li>
              </ul>
            </div>
          </div>
        `,
        props: ['pieces', 'metadata', 'instructions', 'stats', 'difficulty', 'artStyle', 'showReference', 'referenceImage']
      }

      mockSolutionComponent = {
        template: `
          <div data-testid="art-puzzle-solution">
            <div class="solution-header">
              <h3>Completed Art Puzzle</h3>
              <div v-if="metadata" class="artwork-info">
                <h4>{{ metadata.title }}</h4>
                <p v-if="metadata.artist">Artist: {{ metadata.artist }}</p>
                <p v-if="metadata.year">Year: {{ metadata.year }}</p>
                <p v-if="metadata.description">{{ metadata.description }}</p>
                <p v-if="metadata.artMovement">Art Movement: {{ metadata.artMovement }}</p>
              </div>
            </div>
            <div class="completed-puzzle">
              <div class="solution-grid">
                <div v-for="piece in solution" :key="piece.id"
                     class="solved-piece"
                     :style="{
                       gridColumn: piece.correctPosition.x + 1,
                       gridRow: piece.correctPosition.y + 1,
                       transform: 'rotate(' + piece.correctRotation + 'deg)'
                     }">
                  {{ piece.id }}
                </div>
              </div>
            </div>
            <div class="educational-content">
              <h4>Learn More</h4>
              <p>This puzzle features artwork from the {{ metadata?.artMovement || 'classical' }} period.</p>
              <p v-if="hints">
                <strong>Solving Tips:</strong>
                <span v-for="hint in hints" :key="hint">{{ hint }}. </span>
              </p>
            </div>
          </div>
        `,
        props: ['solution', 'metadata', 'hints']
      }
    })

    it('should render customize component', () => {
      const wrapper = mount(mockCustomizeComponent, {
        props: { modelValue: { artStyle: 'renaissance', difficulty: 'medium' } }
      })
      
      expect(wrapper.find('[data-testid="art-puzzle-customize"]').exists()).toBe(true)
      expect(wrapper.findAll('select')).toHaveLength(3)
      expect(wrapper.find('input[type="number"]').exists()).toBe(true)
      expect(wrapper.findAll('input[type="checkbox"]')).toHaveLength(2)
      expect(wrapper.find('input[type="url"]').exists()).toBe(true)
    })

    it('should render preview component with puzzle workspace', () => {
      const props = {
        pieces: [
          {
            id: 1,
            shape: 'rectangle',
            position: { x: 0, y: 0 },
            imageSegment: 'data:image/png;base64,test1',
            rotation: 0,
            isPlaced: false
          },
          {
            id: 2,
            shape: 'rectangle',
            position: { x: 1, y: 0 },
            imageSegment: 'data:image/png;base64,test2',
            rotation: 90,
            isPlaced: false
          }
        ],
        metadata: {
          title: 'The Starry Night',
          artist: 'Vincent van Gogh',
          year: '1889',
          artMovement: 'Post-Impressionism'
        },
        instructions: ['Drag pieces to correct positions', 'Look for matching edges'],
        stats: { pieceCount: 2 },
        difficulty: 'medium',
        artStyle: 'impressionist',
        showReference: true,
        referenceImage: 'data:image/png;base64,reference'
      }
      
      const wrapper = mount(mockPreviewComponent, { props })
      
      expect(wrapper.find('[data-testid="art-puzzle-preview"]').exists()).toBe(true)
      expect(wrapper.find('.puzzle-workspace').exists()).toBe(true)
      expect(wrapper.find('.reference-image').exists()).toBe(true)
      expect(wrapper.findAll('.puzzle-piece')).toHaveLength(2)
      expect(wrapper.text()).toContain('The Starry Night')
      expect(wrapper.text()).toContain('Vincent van Gogh')
      expect(wrapper.text()).toContain('Post-Impressionism')
    })

    it('should render solution component with artwork information', () => {
      const props = {
        solution: [
          {
            id: 1,
            correctPosition: { x: 0, y: 0 },
            correctRotation: 0
          },
          {
            id: 2,
            correctPosition: { x: 1, y: 0 },
            correctRotation: 0
          }
        ],
        metadata: {
          title: 'Guernica',
          artist: 'Pablo Picasso',
          year: '1937',
          description: 'A powerful anti-war painting',
          artMovement: 'Cubism'
        },
        hints: ['Start with corner pieces', 'Look for distinctive shapes', 'Group by color schemes']
      }
      
      const wrapper = mount(mockSolutionComponent, { props })
      
      expect(wrapper.find('[data-testid="art-puzzle-solution"]').exists()).toBe(true)
      expect(wrapper.find('.completed-puzzle').exists()).toBe(true)
      expect(wrapper.find('.educational-content').exists()).toBe(true)
      expect(wrapper.text()).toContain('Guernica')
      expect(wrapper.text()).toContain('Pablo Picasso')
      expect(wrapper.text()).toContain('Cubism')
      expect(wrapper.text()).toContain('Start with corner pieces')
    })
  })

  describe('Art Puzzle Settings Validation', () => {
    it('should validate piece count constraints', () => {
      const validCounts = [16, 25, 36, 49, 64, 100]
      const invalidCounts = [10, 15, 200, 500]
      
      validCounts.forEach(count => {
        expect(count).toBeGreaterThanOrEqual(16)
        expect(count).toBeLessThanOrEqual(100)
      })
      
      invalidCounts.forEach(count => {
        expect(count < 16 || count > 100).toBe(true)
      })
    })

    it('should validate art style options', () => {
      const validStyles = ['renaissance', 'impressionist', 'modern', 'abstract', 'classical']
      const invalidStyles = ['invalid', '', 'not-a-style']
      
      validStyles.forEach(style => {
        expect(['renaissance', 'impressionist', 'modern', 'abstract', 'classical']).toContain(style)
      })
      
      invalidStyles.forEach(style => {
        expect(['renaissance', 'impressionist', 'modern', 'abstract', 'classical']).not.toContain(style)
      })
    })

    it('should validate puzzle shape options', () => {
      const validShapes = ['rectangle', 'square', 'irregular']
      const invalidShapes = ['circle', 'triangle', 'invalid']
      
      validShapes.forEach(shape => {
        expect(['rectangle', 'square', 'irregular']).toContain(shape)
      })
      
      invalidShapes.forEach(shape => {
        expect(['rectangle', 'square', 'irregular']).not.toContain(shape)
      })
    })
  })

  describe('Art Puzzle Edge Cases', () => {
    it('should handle minimum piece count', async () => {
      const { generateArtPuzzle } = await import('../../../layers/writer/components/global/book/puzzles/art-puzzle/generator')
      
      const settings = {
        artStyle: 'classical' as const,
        difficulty: 'easy' as const,
        pieceCount: 16, // Minimum
        theme: 'classical',
        showReference: true,
        enableRotation: false,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-art-puzzle-min',
        type: 'art-puzzle',
        artStyle: 'classical',
        difficulty: 'easy' as const,
        pieces: Array.from({ length: 16 }, (_, i) => ({
          id: i + 1,
          shape: 'rectangle',
          position: { x: i % 4, y: Math.floor(i / 4) },
          imageSegment: `data:image/png;base64,segment${i}`,
          rotation: 0,
          isPlaced: false
        })),
        solution: Array.from({ length: 16 }, (_, i) => ({
          id: i + 1,
          correctPosition: { x: i % 4, y: Math.floor(i / 4) },
          correctRotation: 0
        })),
        stats: {
          pieceCount: 16,
          complexity: 3.0,
          educationalValue: 6,
          estimatedSolveTime: 300
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateArtPuzzle).mockResolvedValue(mockResult)
      
      const result = await generateArtPuzzle(settings)
      expect(result.pieces).toHaveLength(16)
      expect(result.stats.pieceCount).toBe(16)
    })

    it('should handle custom image URLs', async () => {
      const { generateArtPuzzle } = await import('../../../layers/writer/components/global/book/puzzles/art-puzzle/generator')
      
      const settings = {
        artStyle: 'modern' as const,
        difficulty: 'medium' as const,
        pieceCount: 25,
        theme: 'custom',
        showReference: true,
        enableRotation: false,
        imageUrl: 'https://example.com/custom-artwork.jpg',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-art-puzzle-custom',
        type: 'art-puzzle',
        artStyle: 'modern',
        difficulty: 'medium' as const,
        pieces: Array.from({ length: 25 }, (_, i) => ({
          id: i + 1,
          shape: 'rectangle',
          position: { x: i % 5, y: Math.floor(i / 5) },
          imageSegment: `data:image/png;base64,custom${i}`,
          rotation: 0,
          isPlaced: false
        })),
        solution: Array.from({ length: 25 }, (_, i) => ({
          id: i + 1,
          correctPosition: { x: i % 5, y: Math.floor(i / 5) },
          correctRotation: 0
        })),
        metadata: {
          title: 'Custom Artwork',
          artist: 'User Provided',
          source: 'https://example.com/custom-artwork.jpg'
        },
        stats: {
          pieceCount: 25,
          complexity: 5.5,
          educationalValue: 5,
          estimatedSolveTime: 900
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateArtPuzzle).mockResolvedValue(mockResult)
      
      const result = await generateArtPuzzle(settings)
      expect(result.metadata.source).toBe('https://example.com/custom-artwork.jpg')
      expect(result.metadata.title).toBe('Custom Artwork')
    })

    it('should handle irregular shaped pieces', async () => {
      const { generateArtPuzzle } = await import('../../../layers/writer/components/global/book/puzzles/art-puzzle/generator')
      
      const settings = {
        artStyle: 'abstract' as const,
        difficulty: 'hard' as const,
        pieceCount: 49,
        theme: 'abstract',
        showReference: false,
        enableRotation: true,
        puzzleShape: 'irregular' as const,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-art-puzzle-irregular',
        type: 'art-puzzle',
        artStyle: 'abstract',
        difficulty: 'hard' as const,
        pieces: Array.from({ length: 49 }, (_, i) => ({
          id: i + 1,
          shape: 'irregular',
          position: { x: i % 7, y: Math.floor(i / 7) },
          imageSegment: `data:image/png;base64,irregular${i}`,
          rotation: Math.floor(Math.random() * 4) * 90,
          isPlaced: false,
          edges: ['concave', 'convex', 'straight', 'wavy'] // Irregular piece characteristics
        })),
        solution: Array.from({ length: 49 }, (_, i) => ({
          id: i + 1,
          correctPosition: { x: i % 7, y: Math.floor(i / 7) },
          correctRotation: 0
        })),
        metadata: {
          title: 'Abstract Composition',
          artist: 'Wassily Kandinsky',
          artMovement: 'Abstract Expressionism'
        },
        stats: {
          pieceCount: 49,
          complexity: 9.8, // Very high due to irregular shapes and rotation
          educationalValue: 9,
          estimatedSolveTime: 3600
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateArtPuzzle).mockResolvedValue(mockResult)
      
      const result = await generateArtPuzzle(settings)
      expect(result.pieces.every(piece => piece.shape === 'irregular')).toBe(true)
      expect(result.stats.complexity).toBeGreaterThan(9)
    })
  })
})