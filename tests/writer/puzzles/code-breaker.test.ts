import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'

// Mock the generator function
vi.mock('../../../layers/writer/components/global/book/puzzles/code-breaker/generator', () => ({
  generateCodeBreaker: vi.fn()
}))

// Mock global composables
globalThis.ref = vi.fn((value) => ({ value }))
globalThis.computed = vi.fn((fn) => ({ value: fn() }))
globalThis.watch = vi.fn()
globalThis.defineEmits = vi.fn(() => vi.fn())
globalThis.defineProps = vi.fn()

describe('Code Breaker Puzzle Components', () => {
  describe('Code Breaker Generator', () => {
    it('should generate valid code breaker puzzle', async () => {
      const { generateCodeBreaker } = await import('../../../layers/writer/components/global/book/puzzles/code-breaker/generator')
      
      const settings = {
        codeType: 'substitution' as const,
        difficulty: 'medium' as const,
        messageLength: 50,
        theme: 'spy',
        includeNumbers: true,
        includePunctuation: false,
        showPartialKey: true,
        keyLength: 4,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-code-breaker-1',
        type: 'code-breaker',
        codeType: 'substitution',
        difficulty: 'medium' as const,
        originalMessage: 'THE SECRET MISSION IS TO DECODE THIS MESSAGE',
        encodedMessage: 'WKH VHFUHW PLVVLRQ LV WR GHFRGH WKLV PHVVDJH',
        codeKey: {
          type: 'caesar',
          shift: 3,
          mapping: {
            'A': 'D', 'B': 'E', 'C': 'F', 'D': 'G', 'E': 'H',
            'F': 'I', 'G': 'J', 'H': 'K', 'I': 'L', 'J': 'M',
            'K': 'N', 'L': 'O', 'M': 'P', 'N': 'Q', 'O': 'R',
            'P': 'S', 'Q': 'T', 'R': 'U', 'S': 'V', 'T': 'W',
            'U': 'X', 'V': 'Y', 'W': 'Z', 'X': 'A', 'Y': 'B', 'Z': 'C'
          }
        },
        partialKey: {
          'T': 'W',
          'H': 'K',
          'E': 'H'
        },
        hints: [
          'This is a Caesar cipher',
          'Each letter is shifted by the same amount',
          'T becomes W - what is the shift?'
        ],
        instructions: [
          'Use the partial key to start decoding',
          'Look for patterns in common words',
          'Apply the same shift to all letters'
        ],
        stats: {
          messageLength: 42,
          uniqueCharacters: 16,
          complexity: 6.5,
          educationalValue: 8,
          estimatedSolveTime: 900
        },
        theme: 'spy',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateCodeBreaker).mockResolvedValue(mockResult)
      
      const result = await generateCodeBreaker(settings)
      
      expect(result).toBeDefined()
      expect(result.type).toBe('code-breaker')
      expect(result.codeType).toBe('substitution')
      expect(result.originalMessage).toBe('THE SECRET MISSION IS TO DECODE THIS MESSAGE')
      expect(result.encodedMessage).toBe('WKH VHFUHW PLVVLRQ LV WR GHFRGH WKLV PHVVDJH')
      expect(result.codeKey.shift).toBe(3)
      expect(result.hints).toHaveLength(3)
    })

    it('should handle different code types', async () => {
      const { generateCodeBreaker } = await import('../../../layers/writer/components/global/book/puzzles/code-breaker/generator')
      
      const codeTypes = ['substitution', 'morse', 'binary', 'atbash', 'vigenere'] as const
      
      for (const codeType of codeTypes) {
        const settings = {
          codeType,
          difficulty: 'medium' as const,
          messageLength: 30,
          theme: 'general',
          includeNumbers: false,
          includePunctuation: false,
          showPartialKey: true,
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00',
          fontSize: 'medium' as const,
          printLayout: 'compact' as const
        }

        let mockResult: any = {
          id: `test-code-breaker-${codeType}`,
          type: 'code-breaker',
          codeType,
          difficulty: 'medium' as const,
          originalMessage: 'HELLO WORLD',
          theme: 'general',
          stats: {
            messageLength: 11,
            complexity: 5.0,
            educationalValue: 7,
            estimatedSolveTime: 600
          },
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00'
        }

        switch (codeType) {
          case 'morse':
            mockResult.encodedMessage = '.... . .-.. .-.. --- / .-- --- .-. .-.. -..'
            mockResult.codeKey = {
              type: 'morse',
              mapping: {
                'A': '.-', 'B': '-...', 'C': '-.-.', 'D': '-..', 'E': '.',
                'F': '..-.', 'G': '--.', 'H': '....', 'I': '..', 'J': '.---',
                'K': '-.-', 'L': '.-..', 'M': '--', 'N': '-.', 'O': '---',
                'P': '.--.', 'Q': '--.-', 'R': '.-.', 'S': '...', 'T': '-',
                'U': '..-', 'V': '...-', 'W': '.--', 'X': '-..-', 'Y': '-.--', 'Z': '--..'
              }
            }
            break
          case 'binary':
            mockResult.encodedMessage = '01001000 01000101 01001100 01001100 01001111'
            mockResult.codeKey = { type: 'binary', encoding: 'ascii' }
            break
          case 'atbash':
            mockResult.encodedMessage = 'SVOOL DLIOW'
            mockResult.codeKey = { type: 'atbash', mapping: {} }
            break
          case 'vigenere':
            mockResult.encodedMessage = 'RIJVS UYVJN'
            mockResult.codeKey = { type: 'vigenere', keyword: 'KEY' }
            break
          default:
            mockResult.encodedMessage = 'KHOOR ZRUOG'
            mockResult.codeKey = { type: 'caesar', shift: 3 }
        }

        vi.mocked(generateCodeBreaker).mockResolvedValue(mockResult)
        
        const result = await generateCodeBreaker(settings)
        expect(result.codeType).toBe(codeType)
        expect(result.codeKey.type).toBe(codeType === 'substitution' ? 'caesar' : codeType)
      }
    })

    it('should handle different difficulty levels', async () => {
      const { generateCodeBreaker } = await import('../../../layers/writer/components/global/book/puzzles/code-breaker/generator')
      
      const difficulties = ['easy', 'medium', 'hard'] as const
      
      for (const difficulty of difficulties) {
        const settings = {
          codeType: 'substitution' as const,
          difficulty,
          messageLength: difficulty === 'easy' ? 20 : difficulty === 'medium' ? 40 : 60,
          theme: 'general',
          includeNumbers: difficulty !== 'easy',
          includePunctuation: difficulty === 'hard',
          showPartialKey: difficulty === 'easy',
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00',
          fontSize: 'medium' as const,
          printLayout: 'compact' as const
        }

        const messageLength = difficulty === 'easy' ? 20 : difficulty === 'medium' ? 40 : 60
        
        const mockResult = {
          id: `test-code-breaker-${difficulty}`,
          type: 'code-breaker',
          codeType: 'substitution',
          difficulty,
          originalMessage: 'A'.repeat(messageLength),
          encodedMessage: 'D'.repeat(messageLength),
          codeKey: {
            type: 'caesar',
            shift: difficulty === 'easy' ? 1 : difficulty === 'medium' ? 7 : 13
          },
          partialKey: difficulty === 'easy' ? { 'A': 'D' } : {},
          hints: difficulty === 'easy' ? ['Start with the given letter', 'Apply the same shift'] : 
                 difficulty === 'medium' ? ['Look for common words'] : [],
          stats: {
            messageLength,
            complexity: difficulty === 'easy' ? 3.0 : difficulty === 'medium' ? 6.0 : 9.0,
            educationalValue: difficulty === 'easy' ? 6 : difficulty === 'medium' ? 8 : 9,
            estimatedSolveTime: difficulty === 'easy' ? 300 : difficulty === 'medium' ? 900 : 1800
          },
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00'
        }

        vi.mocked(generateCodeBreaker).mockResolvedValue(mockResult)
        
        const result = await generateCodeBreaker(settings)
        expect(result.difficulty).toBe(difficulty)
        expect(result.stats.messageLength).toBe(messageLength)
        expect(result.stats.complexity).toBeGreaterThan(0)
      }
    })

    it('should handle Vigenere cipher with keyword', async () => {
      const { generateCodeBreaker } = await import('../../../layers/writer/components/global/book/puzzles/code-breaker/generator')
      
      const settings = {
        codeType: 'vigenere' as const,
        difficulty: 'hard' as const,
        messageLength: 40,
        theme: 'cryptography',
        includeNumbers: false,
        includePunctuation: false,
        showPartialKey: false,
        keyLength: 5,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-code-breaker-vigenere',
        type: 'code-breaker',
        codeType: 'vigenere',
        difficulty: 'hard' as const,
        originalMessage: 'CRYPTOGRAPHY IS THE ART OF SECRET WRITING',
        encodedMessage: 'MZSXDQKRKEXI WG DLI ETD QJ GIKVED ATMDWRK',
        codeKey: {
          type: 'vigenere',
          keyword: 'CIPHER',
          keywordLength: 6
        },
        hints: [
          'This uses a repeating keyword',
          'The keyword length is 6 characters',
          'Look for repeating patterns in the encoded text'
        ],
        instructions: [
          'Find the repeating keyword used for encoding',
          'Each letter of the keyword shifts the corresponding plaintext letter',
          'The keyword repeats throughout the message'
        ],
        stats: {
          messageLength: 39,
          uniqueCharacters: 15,
          complexity: 9.5,
          educationalValue: 9,
          estimatedSolveTime: 2400
        },
        theme: 'cryptography',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateCodeBreaker).mockResolvedValue(mockResult)
      
      const result = await generateCodeBreaker(settings)
      expect(result.codeType).toBe('vigenere')
      expect(result.codeKey.keyword).toBe('CIPHER')
      expect(result.codeKey.keywordLength).toBe(6)
      expect(result.stats.complexity).toBeGreaterThan(9)
    })
  })

  describe('Code Breaker Components Mock Tests', () => {
    let mockCustomizeComponent: any
    let mockPreviewComponent: any
    let mockSolutionComponent: any

    beforeEach(() => {
      mockCustomizeComponent = {
        template: `
          <div data-testid="code-breaker-customize">
            <select v-model="codeType">
              <option value="substitution">Substitution (Caesar)</option>
              <option value="morse">Morse Code</option>
              <option value="binary">Binary</option>
              <option value="atbash">Atbash</option>
              <option value="vigenere">Vigenère</option>
            </select>
            <select v-model="difficulty">
              <option value="easy">Easy</option>
              <option value="medium">Medium</option>
              <option value="hard">Hard</option>
            </select>
            <input v-model="messageLength" type="number" min="10" max="100" />
            <select v-model="theme">
              <option value="spy">Spy Mission</option>
              <option value="pirate">Pirate Treasure</option>
              <option value="mystery">Mystery</option>
              <option value="general">General</option>
            </select>
            <label>
              <input type="checkbox" v-model="includeNumbers" />
              Include Numbers
            </label>
            <label>
              <input type="checkbox" v-model="includePunctuation" />
              Include Punctuation
            </label>
            <label>
              <input type="checkbox" v-model="showPartialKey" />
              Show Partial Key
            </label>
            <input v-if="codeType === 'vigenere'" v-model="keyLength" type="number" min="3" max="10" placeholder="Keyword length" />
          </div>
        `,
        props: ['modelValue'],
        emits: ['update:modelValue'],
        data() {
          return {
            codeType: 'substitution',
            difficulty: 'medium',
            messageLength: 40,
            theme: 'spy',
            includeNumbers: false,
            includePunctuation: false,
            showPartialKey: true,
            keyLength: 4
          }
        }
      }

      mockPreviewComponent = {
        template: `
          <div data-testid="code-breaker-preview">
            <div class="puzzle-header">
              <h3>Code Breaker Challenge</h3>
              <p>{{ theme === 'spy' ? 'Secret Agent Mission' : theme === 'pirate' ? 'Pirate Treasure Map' : 'Mystery Message' }}</p>
              <div class="difficulty-badge">{{ difficulty.toUpperCase() }}</div>
            </div>
            <div class="encoded-message">
              <h4>Encoded Message:</h4>
              <div class="code-text">{{ encodedMessage }}</div>
            </div>
            <div v-if="partialKey && Object.keys(partialKey).length > 0" class="partial-key">
              <h4>Partial Key:</h4>
              <div class="key-mapping">
                <span v-for="(encoded, original) in partialKey" :key="original">
                  {{ original }} → {{ encoded }}
                </span>
              </div>
            </div>
            <div class="decoding-area">
              <h4>Your Solution:</h4>
              <textarea placeholder="Enter your decoded message here..." rows="3"></textarea>
            </div>
            <div v-if="hints && hints.length > 0" class="hints">
              <h4>Hints:</h4>
              <ul>
                <li v-for="hint in hints" :key="hint">{{ hint }}</li>
              </ul>
            </div>
            <div v-if="instructions" class="instructions">
              <h4>Instructions:</h4>
              <ul>
                <li v-for="instruction in instructions" :key="instruction">{{ instruction }}</li>
              </ul>
            </div>
            <div class="puzzle-stats">
              <p>Code Type: {{ codeType }}</p>
              <p>Message Length: {{ stats?.messageLength || 0 }} characters</p>
              <p>Complexity: {{ stats?.complexity?.toFixed(1) || 0 }}/10</p>
              <p>Estimated Time: {{ Math.floor((stats?.estimatedSolveTime || 0) / 60) }} minutes</p>
            </div>
          </div>
        `,
        props: ['encodedMessage', 'partialKey', 'hints', 'instructions', 'stats', 'codeType', 'difficulty', 'theme']
      }

      mockSolutionComponent = {
        template: `
          <div data-testid="code-breaker-solution">
            <div class="solution-header">
              <h3>Code Breaking Solution</h3>
            </div>
            <div class="solution-content">
              <div class="original-message">
                <h4>Original Message:</h4>
                <div class="decoded-text">{{ originalMessage }}</div>
              </div>
              <div class="code-explanation">
                <h4>Code Key:</h4>
                <div v-if="codeKey.type === 'caesar'" class="caesar-info">
                  <p>Caesar Cipher with shift of {{ codeKey.shift }}</p>
                  <div class="alphabet-mapping">
                    <div class="alphabet-row">
                      <span>Original:</span>
                      <span v-for="letter in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'" :key="letter">{{ letter }}</span>
                    </div>
                    <div class="alphabet-row">
                      <span>Encoded:</span>
                      <span v-for="letter in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'" :key="letter">
                        {{ String.fromCharCode(((letter.charCodeAt(0) - 65 + codeKey.shift) % 26) + 65) }}
                      </span>
                    </div>
                  </div>
                </div>
                <div v-else-if="codeKey.type === 'vigenere'" class="vigenere-info">
                  <p>Vigenère Cipher with keyword: {{ codeKey.keyword }}</p>
                  <p>Keyword repeats: {{ codeKey.keyword.repeat(Math.ceil(originalMessage.length / codeKey.keyword.length)).substring(0, originalMessage.length) }}</p>
                </div>
                <div v-else-if="codeKey.type === 'morse'" class="morse-info">
                  <p>Morse Code</p>
                  <div class="morse-table">
                    <div v-for="(morse, letter) in codeKey.mapping" :key="letter" class="morse-mapping">
                      {{ letter }}: {{ morse }}
                    </div>
                  </div>
                </div>
                <div v-else-if="codeKey.type === 'binary'" class="binary-info">
                  <p>Binary Code ({{ codeKey.encoding }} encoding)</p>
                </div>
                <div v-else-if="codeKey.type === 'atbash'" class="atbash-info">
                  <p>Atbash Cipher (A↔Z, B↔Y, C↔X, etc.)</p>
                </div>
              </div>
              <div class="educational-content">
                <h4>About This Cipher:</h4>
                <p v-if="codeKey.type === 'caesar'">
                  The Caesar cipher is one of the simplest encryption techniques. It shifts each letter by a fixed number of positions in the alphabet.
                </p>
                <p v-else-if="codeKey.type === 'vigenere'">
                  The Vigenère cipher uses a repeating keyword to shift letters by different amounts, making it much stronger than Caesar ciphers.
                </p>
                <p v-else-if="codeKey.type === 'morse'">
                  Morse code represents letters and numbers using dots and dashes, originally designed for telegraph communication.
                </p>
                <p v-else-if="codeKey.type === 'binary'">
                  Binary code represents text using only 0s and 1s, the fundamental language of computers.
                </p>
                <p v-else-if="codeKey.type === 'atbash'">
                  Atbash is a simple substitution cipher where A becomes Z, B becomes Y, and so on.
                </p>
              </div>
            </div>
          </div>
        `,
        props: ['originalMessage', 'codeKey']
      }
    })

    it('should render customize component', () => {
      const wrapper = mount(mockCustomizeComponent, {
        props: { modelValue: { codeType: 'substitution', difficulty: 'medium' } }
      })
      
      expect(wrapper.find('[data-testid="code-breaker-customize"]').exists()).toBe(true)
      expect(wrapper.findAll('select')).toHaveLength(3)
      expect(wrapper.find('input[type="number"]').exists()).toBe(true)
      expect(wrapper.findAll('input[type="checkbox"]')).toHaveLength(3)
    })

    it('should render preview component with encoded message', () => {
      const props = {
        encodedMessage: 'WKH TXLFN EURZQ IRA',
        partialKey: { 'T': 'W', 'H': 'K', 'E': 'H' },
        hints: ['This is a Caesar cipher', 'Each letter is shifted by 3'],
        instructions: ['Use the partial key to decode', 'Apply the same shift to all letters'],
        stats: {
          messageLength: 19,
          complexity: 5.5,
          estimatedSolveTime: 600
        },
        codeType: 'substitution',
        difficulty: 'medium',
        theme: 'spy'
      }
      
      const wrapper = mount(mockPreviewComponent, { props })
      
      expect(wrapper.find('[data-testid="code-breaker-preview"]').exists()).toBe(true)
      expect(wrapper.find('.encoded-message').exists()).toBe(true)
      expect(wrapper.find('.partial-key').exists()).toBe(true)
      expect(wrapper.find('.hints').exists()).toBe(true)
      expect(wrapper.text()).toContain('WKH TXLFN EURZQ IRA')
      expect(wrapper.text()).toContain('T → W')
      expect(wrapper.text()).toContain('Secret Agent Mission')
    })

    it('should render solution component with decryption details', () => {
      const props = {
        originalMessage: 'THE QUICK BROWN FOX',
        codeKey: {
          type: 'caesar',
          shift: 3,
          mapping: {
            'T': 'W', 'H': 'K', 'E': 'H'
          }
        }
      }
      
      const wrapper = mount(mockSolutionComponent, { props })
      
      expect(wrapper.find('[data-testid="code-breaker-solution"]').exists()).toBe(true)
      expect(wrapper.find('.original-message').exists()).toBe(true)
      expect(wrapper.find('.code-explanation').exists()).toBe(true)
      expect(wrapper.find('.educational-content').exists()).toBe(true)
      expect(wrapper.text()).toContain('THE QUICK BROWN FOX')
      expect(wrapper.text()).toContain('Caesar Cipher with shift of 3')
    })
  })

  describe('Code Breaker Settings Validation', () => {
    it('should validate message length constraints', () => {
      const validLengths = [10, 25, 50, 75, 100]
      const invalidLengths = [5, 150, 200]
      
      validLengths.forEach(length => {
        expect(length).toBeGreaterThanOrEqual(10)
        expect(length).toBeLessThanOrEqual(100)
      })
      
      invalidLengths.forEach(length => {
        expect(length < 10 || length > 100).toBe(true)
      })
    })

    it('should validate code type options', () => {
      const validTypes = ['substitution', 'morse', 'binary', 'atbash', 'vigenere']
      const invalidTypes = ['invalid', '', 'rot13']
      
      validTypes.forEach(type => {
        expect(['substitution', 'morse', 'binary', 'atbash', 'vigenere']).toContain(type)
      })
      
      invalidTypes.forEach(type => {
        expect(['substitution', 'morse', 'binary', 'atbash', 'vigenere']).not.toContain(type)
      })
    })

    it('should validate keyword length for Vigenere', () => {
      const validLengths = [3, 4, 5, 6, 7, 8, 9, 10]
      const invalidLengths = [1, 2, 15, 20]
      
      validLengths.forEach(length => {
        expect(length).toBeGreaterThanOrEqual(3)
        expect(length).toBeLessThanOrEqual(10)
      })
      
      invalidLengths.forEach(length => {
        expect(length < 3 || length > 10).toBe(true)
      })
    })
  })

  describe('Code Breaker Edge Cases', () => {
    it('should handle minimum message length', async () => {
      const { generateCodeBreaker } = await import('../../../layers/writer/components/global/book/puzzles/code-breaker/generator')
      
      const settings = {
        codeType: 'substitution' as const,
        difficulty: 'easy' as const,
        messageLength: 10, // Minimum
        theme: 'general',
        includeNumbers: false,
        includePunctuation: false,
        showPartialKey: true,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-code-breaker-min',
        type: 'code-breaker',
        codeType: 'substitution',
        difficulty: 'easy' as const,
        originalMessage: 'HELLO',
        encodedMessage: 'KHOOR',
        codeKey: { type: 'caesar', shift: 3 },
        partialKey: { 'H': 'K' },
        stats: {
          messageLength: 5,
          complexity: 2.0,
          educationalValue: 5,
          estimatedSolveTime: 180
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateCodeBreaker).mockResolvedValue(mockResult)
      
      const result = await generateCodeBreaker(settings)
      expect(result.stats.messageLength).toBeGreaterThanOrEqual(5)
      expect(result.partialKey).toBeDefined()
    })

    it('should handle morse code with numbers and punctuation', async () => {
      const { generateCodeBreaker } = await import('../../../layers/writer/components/global/book/puzzles/code-breaker/generator')
      
      const settings = {
        codeType: 'morse' as const,
        difficulty: 'hard' as const,
        messageLength: 30,
        theme: 'emergency',
        includeNumbers: true,
        includePunctuation: true,
        showPartialKey: false,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-code-breaker-morse-complex',
        type: 'code-breaker',
        codeType: 'morse',
        difficulty: 'hard' as const,
        originalMessage: 'SOS! HELP AT 123 MAIN ST.',
        encodedMessage: '... --- ... -.-.-- / .... . .-.. .--. / .- - / .---- ..--- ...-- / -- .- .. -. / ... - .-.-.-',
        codeKey: {
          type: 'morse',
          mapping: {
            'S': '...', 'O': '---', '!': '-.-.--', 'H': '....', 'E': '.', 'L': '.-..',
            'P': '.--.', 'A': '.-', 'T': '-', '1': '.----', '2': '..---', '3': '...--',
            'M': '--', 'I': '..', 'N': '-.', '.': '.-.-.-'
          }
        },
        hints: [],
        stats: {
          messageLength: 24,
          uniqueCharacters: 12,
          complexity: 8.5,
          educationalValue: 9,
          estimatedSolveTime: 1500
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateCodeBreaker).mockResolvedValue(mockResult)
      
      const result = await generateCodeBreaker(settings)
      expect(result.codeType).toBe('morse')
      expect(result.originalMessage).toContain('!')
      expect(result.originalMessage).toContain('123')
      expect(result.encodedMessage).toContain('-.-.--') // Exclamation mark in morse
    })

    it('should handle binary encoding edge cases', async () => {
      const { generateCodeBreaker } = await import('../../../layers/writer/components/global/book/puzzles/code-breaker/generator')
      
      const settings = {
        codeType: 'binary' as const,
        difficulty: 'hard' as const,
        messageLength: 15,
        theme: 'computer',
        includeNumbers: false,
        includePunctuation: false,
        showPartialKey: false,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-code-breaker-binary',
        type: 'code-breaker',
        codeType: 'binary',
        difficulty: 'hard' as const,
        originalMessage: 'BINARY CODE',
        encodedMessage: '01000010 01001001 01001110 01000001 01010010 01011001 00100000 01000011 01001111 01000100 01000101',
        codeKey: {
          type: 'binary',
          encoding: 'ascii'
        },
        hints: [
          'Each group of 8 bits represents one character',
          'Use ASCII encoding table',
          'Space is represented as 00100000'
        ],
        stats: {
          messageLength: 11,
          uniqueCharacters: 9,
          complexity: 9.0,
          educationalValue: 9,
          estimatedSolveTime: 1800
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateCodeBreaker).mockResolvedValue(mockResult)
      
      const result = await generateCodeBreaker(settings)
      expect(result.codeType).toBe('binary')
      expect(result.encodedMessage).toMatch(/^[01\s]+$/) // Only contains 0s, 1s, and spaces
      expect(result.codeKey.encoding).toBe('ascii')
    })
  })
})