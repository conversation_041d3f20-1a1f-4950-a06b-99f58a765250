import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'

// Mock all puzzle generators
vi.mock('../../../layers/writer/components/global/book/puzzles/logic-grid/generator', () => ({
  generateLogicGrid: vi.fn()
}))

vi.mock('../../../layers/writer/components/global/book/puzzles/math-story/generator', () => ({
  generateMathStory: vi.fn()
}))

vi.mock('../../../layers/writer/components/global/book/puzzles/music-notes/generator', () => ({
  generateMusicNotes: vi.fn()
}))

vi.mock('../../../layers/writer/components/global/book/puzzles/spelling-bee/generator', () => ({
  generateSpellingBee: vi.fn()
}))

vi.mock('../../../layers/writer/components/global/book/puzzles/vocabulary-match/generator', () => ({
  generateVocabularyMatch: vi.fn()
}))

vi.mock('../../../layers/writer/components/global/book/puzzles/word-ladder/generator', () => ({
  generateWordLadder: vi.fn()
}))

vi.mock('../../../layers/writer/components/global/book/puzzles/word-scramble/generator', () => ({
  generateWordScramble: vi.fn()
}))

vi.mock('../../../layers/writer/components/global/book/puzzles/matching-pairs/generator', () => ({
  generateMatchingPairs: vi.fn()
}))

vi.mock('../../../layers/writer/components/global/book/puzzles/number-sequence/generator', () => ({
  generateNumberSequence: vi.fn()
}))

vi.mock('../../../layers/writer/components/global/book/puzzles/spot-difference/generator', () => ({
  generateSpotDifference: vi.fn()
}))

// Mock global composables
globalThis.ref = vi.fn((value) => ({ value }))
globalThis.computed = vi.fn((fn) => ({ value: fn() }))
globalThis.watch = vi.fn()
globalThis.defineEmits = vi.fn(() => vi.fn())
globalThis.defineProps = vi.fn()

describe('Comprehensive Puzzle Suite Tests', () => {
  describe('Logic Grid Puzzles', () => {
    it('should generate valid logic grid puzzle', async () => {
      const { generateLogicGrid } = await import('../../../layers/writer/components/global/book/puzzles/logic-grid/generator')
      
      const settings = {
        gridSize: 4,
        difficulty: 'medium' as const,
        categories: ['Names', 'Colors', 'Animals', 'Numbers'],
        clueCount: 8,
        allowContradictions: false,
        showWorkspace: true,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-logic-grid-1',
        type: 'logic-grid',
        gridSize: 4,
        difficulty: 'medium' as const,
        categories: ['Names', 'Colors', 'Animals', 'Numbers'],
        items: {
          names: ['Alice', 'Bob', 'Carol', 'David'],
          colors: ['Red', 'Blue', 'Green', 'Yellow'],
          animals: ['Cat', 'Dog', 'Bird', 'Fish'],
          numbers: ['1', '2', '3', '4']
        },
        clues: [
          'Alice likes the red color',
          'The person with the cat has number 2',
          'Bob does not have the dog',
          'Carol has a number higher than 2',
          'The person with blue color has the bird',
          'David has number 1',
          'The person with green color does not have number 4',
          'Alice does not have the fish'
        ],
        solution: {
          alice: { color: 'Red', animal: 'Cat', number: '2' },
          bob: { color: 'Blue', animal: 'Bird', number: '3' },
          carol: { color: 'Yellow', animal: 'Fish', number: '4' },
          david: { color: 'Green', animal: 'Dog', number: '1' }
        },
        grid: Array.from({ length: 4 }, () => Array.from({ length: 4 }, () => null)),
        stats: {
          clueCount: 8,
          complexity: 6.5,
          educationalValue: 9,
          estimatedSolveTime: 900
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateLogicGrid).mockResolvedValue(mockResult)
      
      const result = await generateLogicGrid(settings)
      
      expect(result).toBeDefined()
      expect(result.type).toBe('logic-grid')
      expect(result.categories).toHaveLength(4)
      expect(result.clues).toHaveLength(8)
      expect(result.solution.alice.color).toBe('Red')
    })
  })

  describe('Math Story Puzzles', () => {
    it('should generate valid math story puzzle', async () => {
      const { generateMathStory } = await import('../../../layers/writer/components/global/book/puzzles/math-story/generator')
      
      const settings = {
        grade: 3 as const,
        mathTopic: 'addition' as const,
        difficulty: 'medium' as const,
        storyTheme: 'adventure',
        includeVisuals: true,
        stepByStep: true,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-math-story-1',
        type: 'math-story',
        grade: 3,
        mathTopic: 'addition',
        difficulty: 'medium' as const,
        story: {
          title: 'The Treasure Hunt Adventure',
          narrative: 'Captain Jake found 15 gold coins in the first chest. Then he discovered 23 more coins in the second chest. How many coins did he find in total?',
          characters: ['Captain Jake'],
          setting: 'Pirate ship',
          theme: 'adventure'
        },
        problem: {
          question: 'How many coins did Captain Jake find in total?',
          numbers: [15, 23],
          operation: 'addition',
          equation: '15 + 23 = ?',
          answer: 38,
          unit: 'coins'
        },
        solution: {
          steps: [
            'First chest: 15 coins',
            'Second chest: 23 coins',
            'Add them together: 15 + 23',
            'Total: 38 coins'
          ],
          workingOut: '15 + 23 = 38',
          finalAnswer: '38 coins'
        },
        visuals: [
          {
            type: 'illustration',
            description: 'Two treasure chests with coins',
            position: 'top'
          },
          {
            type: 'number-line',
            range: [0, 40],
            highlights: [15, 23, 38]
          }
        ],
        hints: [
          'Count the coins from the first chest',
          'Count the coins from the second chest',
          'Add the two amounts together'
        ],
        stats: {
          gradeLevel: 3,
          complexity: 5.0,
          educationalValue: 8,
          estimatedSolveTime: 300
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateMathStory).mockResolvedValue(mockResult)
      
      const result = await generateMathStory(settings)
      
      expect(result).toBeDefined()
      expect(result.type).toBe('math-story')
      expect(result.problem.answer).toBe(38)
      expect(result.story.characters).toContain('Captain Jake')
      expect(result.solution.steps).toHaveLength(4)
    })
  })

  describe('Music Notes Puzzles', () => {
    it('should generate valid music notes puzzle', async () => {
      const { generateMusicNotes } = await import('../../../layers/writer/components/global/book/puzzles/music-notes/generator')
      
      const settings = {
        clef: 'treble' as const,
        difficulty: 'medium' as const,
        noteCount: 8,
        includeRhythm: true,
        showStaff: true,
        timeSignature: '4/4' as const,
        keySignature: 'C major' as const,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-music-notes-1',
        type: 'music-notes',
        clef: 'treble',
        difficulty: 'medium' as const,
        notes: [
          { name: 'C', octave: 4, position: 'line-1', duration: 'quarter' },
          { name: 'D', octave: 4, position: 'space-1', duration: 'quarter' },
          { name: 'E', octave: 4, position: 'line-2', duration: 'half' },
          { name: 'F', octave: 4, position: 'space-2', duration: 'quarter' },
          { name: 'G', octave: 4, position: 'line-3', duration: 'quarter' },
          { name: 'A', octave: 4, position: 'space-3', duration: 'half' },
          { name: 'B', octave: 4, position: 'line-4', duration: 'quarter' },
          { name: 'C', octave: 5, position: 'space-4', duration: 'quarter' }
        ],
        staff: {
          lines: 5,
          clef: 'treble',
          timeSignature: '4/4',
          keySignature: 'C major',
          measures: 2
        },
        rhythm: {
          pattern: ['quarter', 'quarter', 'half', 'quarter', 'quarter', 'half', 'quarter', 'quarter'],
          totalBeats: 8
        },
        questions: [
          {
            type: 'identify-note',
            note: { name: 'E', octave: 4, position: 'line-2' },
            question: 'What is the name of this note?',
            options: ['D', 'E', 'F', 'G'],
            correctAnswer: 'E'
          },
          {
            type: 'count-beats',
            question: 'How many beats does this measure contain?',
            correctAnswer: '4'
          }
        ],
        solution: {
          noteNames: ['C', 'D', 'E', 'F', 'G', 'A', 'B', 'C'],
          totalDuration: 8,
          scalePattern: 'C major scale'
        },
        stats: {
          noteCount: 8,
          complexity: 6.0,
          educationalValue: 9,
          estimatedSolveTime: 420
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateMusicNotes).mockResolvedValue(mockResult)
      
      const result = await generateMusicNotes(settings)
      
      expect(result).toBeDefined()
      expect(result.type).toBe('music-notes')
      expect(result.notes).toHaveLength(8)
      expect(result.staff.clef).toBe('treble')
      expect(result.questions[0].correctAnswer).toBe('E')
    })
  })

  describe('Spelling Bee Puzzles', () => {
    it('should generate valid spelling bee puzzle', async () => {
      const { generateSpellingBee } = await import('../../../layers/writer/components/global/book/puzzles/spelling-bee/generator')
      
      const settings = {
        centerLetter: 'A',
        outerLetters: ['B', 'C', 'D', 'E', 'F', 'G'],
        difficulty: 'medium' as const,
        minWordLength: 4,
        includeHints: true,
        showDefinitions: true,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-spelling-bee-1',
        type: 'spelling-bee',
        centerLetter: 'A',
        outerLetters: ['B', 'C', 'D', 'E', 'F', 'G'],
        difficulty: 'medium' as const,
        validWords: [
          { word: 'BADGE', length: 5, score: 5, isPangram: false, definition: 'A distinctive emblem or mark' },
          { word: 'FACED', length: 5, score: 5, isPangram: false, definition: 'Past tense of face' },
          { word: 'CAGED', length: 5, score: 5, isPangram: false, definition: 'Confined in a cage' },
          { word: 'CAFED', length: 5, score: 5, isPangram: false, definition: 'Relating to a cafe' },
          { word: 'ABCDEFG', length: 7, score: 14, isPangram: true, definition: 'Uses all letters' }
        ],
        rules: [
          'Words must contain the center letter A',
          'Words must be at least 4 letters long',
          'Letters can be used multiple times',
          'Pangrams (using all letters) get bonus points'
        ],
        scoring: {
          fourLetter: 1,
          longerWord: 1, // per letter
          pangram: 7 // bonus points
        },
        hints: [
          'Think of words related to identification',
          'Consider past tense verbs',
          'Look for compound words'
        ],
        maxScore: 34,
        stats: {
          totalWords: 5,
          pangramCount: 1,
          complexity: 6.5,
          educationalValue: 8,
          estimatedSolveTime: 600
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateSpellingBee).mockResolvedValue(mockResult)
      
      const result = await generateSpellingBee(settings)
      
      expect(result).toBeDefined()
      expect(result.type).toBe('spelling-bee')
      expect(result.centerLetter).toBe('A')
      expect(result.validWords).toHaveLength(5)
      expect(result.validWords.some(w => w.isPangram)).toBe(true)
    })
  })

  describe('Vocabulary Match Puzzles', () => {
    it('should generate valid vocabulary match puzzle', async () => {
      const { generateVocabularyMatch } = await import('../../../layers/writer/components/global/book/puzzles/vocabulary-match/generator')
      
      const settings = {
        gradeLevel: 5 as const,
        category: 'science' as const,
        wordCount: 8,
        matchType: 'definition' as const,
        difficulty: 'medium' as const,
        includeDistractors: true,
        showHints: true,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-vocabulary-match-1',
        type: 'vocabulary-match',
        gradeLevel: 5,
        category: 'science',
        difficulty: 'medium' as const,
        words: [
          { id: 1, word: 'PHOTOSYNTHESIS', definition: 'Process by which plants make food using sunlight' },
          { id: 2, word: 'ECOSYSTEM', definition: 'A community of living and non-living things' },
          { id: 3, word: 'MOLECULE', definition: 'The smallest unit of a chemical compound' },
          { id: 4, word: 'ORGANISM', definition: 'Any living thing' },
          { id: 5, word: 'HABITAT', definition: 'The natural home of an animal or plant' },
          { id: 6, word: 'SPECIES', definition: 'A group of similar living things' },
          { id: 7, word: 'PREDATOR', definition: 'An animal that hunts other animals for food' },
          { id: 8, word: 'ADAPTATION', definition: 'A feature that helps an organism survive' }
        ],
        distractors: [
          'A type of rock formation',
          'The study of weather patterns',
          'A mathematical equation'
        ],
        matches: [
          { wordId: 1, definitionId: 1, isCorrect: true },
          { wordId: 2, definitionId: 2, isCorrect: true },
          { wordId: 3, definitionId: 3, isCorrect: true },
          { wordId: 4, definitionId: 4, isCorrect: true },
          { wordId: 5, definitionId: 5, isCorrect: true },
          { wordId: 6, definitionId: 6, isCorrect: true },
          { wordId: 7, definitionId: 7, isCorrect: true },
          { wordId: 8, definitionId: 8, isCorrect: true }
        ],
        hints: [
          'Think about how plants get energy',
          'Consider the relationship between living things',
          'Focus on the smallest parts of matter'
        ],
        stats: {
          wordCount: 8,
          complexity: 6.0,
          educationalValue: 9,
          estimatedSolveTime: 480
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateVocabularyMatch).mockResolvedValue(mockResult)
      
      const result = await generateVocabularyMatch(settings)
      
      expect(result).toBeDefined()
      expect(result.type).toBe('vocabulary-match')
      expect(result.words).toHaveLength(8)
      expect(result.matches).toHaveLength(8)
      expect(result.distractors).toHaveLength(3)
    })
  })

  describe('Word Ladder Puzzles', () => {
    it('should generate valid word ladder puzzle', async () => {
      const { generateWordLadder } = await import('../../../layers/writer/components/global/book/puzzles/word-ladder/generator')
      
      const settings = {
        startWord: 'COLD',
        endWord: 'WARM',
        difficulty: 'medium' as const,
        maxSteps: 6,
        allowReversal: false,
        showHints: true,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-word-ladder-1',
        type: 'word-ladder',
        startWord: 'COLD',
        endWord: 'WARM',
        difficulty: 'medium' as const,
        solution: [
          { word: 'COLD', step: 0, change: '', changeType: 'start' },
          { word: 'CORD', step: 1, change: 'L→R', changeType: 'substitute' },
          { word: 'WORD', step: 2, change: 'C→W', changeType: 'substitute' },
          { word: 'WORM', step: 3, change: 'D→M', changeType: 'substitute' },
          { word: 'WARM', step: 4, change: 'O→A', changeType: 'substitute' }
        ],
        hints: [
          'Change one letter to make a string-like object',
          'Change one letter to make a common noun',
          'Change one letter to make a creature',
          'Change one letter to reach the target'
        ],
        rules: [
          'Change exactly one letter at a time',
          'Each step must form a valid word',
          'Cannot use proper nouns',
          'Work from COLD to WARM'
        ],
        validation: {
          allValidWords: true,
          correctLength: true,
          oneLetterChange: true,
          reachesTarget: true
        },
        stats: {
          steps: 4,
          difficulty: 6.0,
          complexity: 5.5,
          educationalValue: 7,
          estimatedSolveTime: 360
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateWordLadder).mockResolvedValue(mockResult)
      
      const result = await generateWordLadder(settings)
      
      expect(result).toBeDefined()
      expect(result.type).toBe('word-ladder')
      expect(result.startWord).toBe('COLD')
      expect(result.endWord).toBe('WARM')
      expect(result.solution).toHaveLength(5)
      expect(result.validation.reachesTarget).toBe(true)
    })
  })

  describe('Word Scramble Puzzles', () => {
    it('should generate valid word scramble puzzle', async () => {
      const { generateWordScramble } = await import('../../../layers/writer/components/global/book/puzzles/word-scramble/generator')
      
      const settings = {
        category: 'animals' as const,
        difficulty: 'medium' as const,
        wordCount: 6,
        minWordLength: 4,
        maxWordLength: 8,
        showHints: true,
        showCategory: true,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-word-scramble-1',
        type: 'word-scramble',
        category: 'animals',
        difficulty: 'medium' as const,
        words: [
          { original: 'ELEPHANT', scrambled: 'HTNAPLEHE', hint: 'Large African animal with tusks', difficulty: 'medium' },
          { original: 'GIRAFFE', scrambled: 'FFEAGIR', hint: 'Tallest animal in the world', difficulty: 'easy' },
          { original: 'PENGUIN', scrambled: 'NUGNEIP', hint: 'Antarctic bird that cannot fly', difficulty: 'medium' },
          { original: 'BUTTERFLY', scrambled: 'TTERFLUYB', hint: 'Colorful insect with wings', difficulty: 'hard' },
          { original: 'DOLPHIN', scrambled: 'PHLOIDN', hint: 'Intelligent marine mammal', difficulty: 'medium' },
          { original: 'KANGAROO', scrambled: 'GOOARNKA', hint: 'Australian animal that hops', difficulty: 'medium' }
        ],
        rules: [
          'Unscramble each word to find the animal name',
          'Use hints if you get stuck',
          'All words are related to animals'
        ],
        scoring: {
          correct: 10,
          withHint: 5,
          timeBonus: 2
        },
        stats: {
          wordCount: 6,
          averageLength: 7.5,
          complexity: 6.0,
          educationalValue: 7,
          estimatedSolveTime: 300
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateWordScramble).mockResolvedValue(mockResult)
      
      const result = await generateWordScramble(settings)
      
      expect(result).toBeDefined()
      expect(result.type).toBe('word-scramble')
      expect(result.words).toHaveLength(6)
      expect(result.category).toBe('animals')
      expect(result.words[0].original).toBe('ELEPHANT')
    })
  })

  describe('Number Sequence Puzzles', () => {
    it('should generate valid number sequence puzzle', async () => {
      const { generateNumberSequence } = await import('../../../layers/writer/components/global/book/puzzles/number-sequence/generator')
      
      const settings = {
        sequenceType: 'arithmetic' as const,
        difficulty: 'medium' as const,
        sequenceLength: 8,
        includeNegatives: false,
        showPattern: false,
        multipleChoices: true,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-number-sequence-1',
        type: 'number-sequence',
        sequenceType: 'arithmetic',
        difficulty: 'medium' as const,
        sequence: [3, 7, 11, 15, 19, 23, '?', 31],
        pattern: {
          type: 'arithmetic',
          description: 'Add 4 to each number',
          rule: 'n + 4',
          commonDifference: 4
        },
        questions: [
          {
            question: 'What is the missing number in the sequence?',
            answer: 27,
            options: [25, 26, 27, 28],
            explanation: 'The pattern is adding 4 each time: 23 + 4 = 27'
          }
        ],
        hints: [
          'Look at the difference between consecutive numbers',
          'The pattern involves addition',
          'Each number increases by the same amount'
        ],
        validation: {
          isValid: true,
          hasPattern: true,
          consistentRule: true
        },
        stats: {
          sequenceLength: 8,
          complexity: 5.0,
          educationalValue: 8,
          estimatedSolveTime: 240
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateNumberSequence).mockResolvedValue(mockResult)
      
      const result = await generateNumberSequence(settings)
      
      expect(result).toBeDefined()
      expect(result.type).toBe('number-sequence')
      expect(result.sequence).toHaveLength(8)
      expect(result.pattern.commonDifference).toBe(4)
      expect(result.questions[0].answer).toBe(27)
    })
  })

  describe('Spot the Difference Puzzles', () => {
    it('should generate valid spot difference puzzle', async () => {
      const { generateSpotDifference } = await import('../../../layers/writer/components/global/book/puzzles/spot-difference/generator')
      
      const settings = {
        imageTheme: 'park' as const,
        difficulty: 'medium' as const,
        differenceCount: 7,
        showCircles: false,
        timeLimit: 300,
        imageSize: 'large' as const,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-spot-difference-1',
        type: 'spot-difference',
        imageTheme: 'park',
        difficulty: 'medium' as const,
        images: {
          original: 'data:image/png;base64,original-park-scene',
          modified: 'data:image/png;base64,modified-park-scene'
        },
        differences: [
          { id: 1, x: 150, y: 200, width: 30, height: 25, description: 'Missing bird in tree' },
          { id: 2, x: 300, y: 150, width: 20, height: 40, description: 'Different colored flower' },
          { id: 3, x: 450, y: 300, width: 50, height: 30, description: 'Additional bench' },
          { id: 4, x: 100, y: 400, width: 25, height: 25, description: 'Missing dog' },
          { id: 5, x: 520, y: 180, width: 35, height: 20, description: 'Different cloud shape' },
          { id: 6, x: 250, y: 350, width: 40, height: 15, description: 'Additional path' },
          { id: 7, x: 380, y: 250, width: 30, height: 30, description: 'Different tree leaves' }
        ],
        hints: [
          'Look carefully at the trees',
          'Check the flowers and plants',
          'Examine the sky area',
          'Look for missing or extra objects'
        ],
        instructions: [
          'Compare the two images carefully',
          'Find all 7 differences',
          'Click on differences when you spot them',
          'Use hints if you get stuck'
        ],
        stats: {
          differenceCount: 7,
          imageSize: 'large',
          complexity: 6.5,
          educationalValue: 6,
          estimatedSolveTime: 300
        },
        timeLimit: 300,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateSpotDifference).mockResolvedValue(mockResult)
      
      const result = await generateSpotDifference(settings)
      
      expect(result).toBeDefined()
      expect(result.type).toBe('spot-difference')
      expect(result.differences).toHaveLength(7)
      expect(result.imageTheme).toBe('park')
      expect(result.timeLimit).toBe(300)
    })
  })

  describe('Matching Pairs Puzzles', () => {
    it('should generate valid matching pairs puzzle', async () => {
      const { generateMatchingPairs } = await import('../../../layers/writer/components/global/book/puzzles/matching-pairs/generator')
      
      const settings = {
        category: 'countries-capitals' as const,
        difficulty: 'medium' as const,
        pairCount: 8,
        matchType: 'text' as const,
        shuffled: true,
        showHints: true,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-matching-pairs-1',
        type: 'matching-pairs',
        category: 'countries-capitals',
        difficulty: 'medium' as const,
        pairs: [
          { id: 1, left: 'France', right: 'Paris', category: 'Europe' },
          { id: 2, left: 'Japan', right: 'Tokyo', category: 'Asia' },
          { id: 3, left: 'Brazil', right: 'Brasília', category: 'South America' },
          { id: 4, left: 'Egypt', right: 'Cairo', category: 'Africa' },
          { id: 5, left: 'Australia', right: 'Canberra', category: 'Oceania' },
          { id: 6, left: 'Canada', right: 'Ottawa', category: 'North America' },
          { id: 7, left: 'India', right: 'New Delhi', category: 'Asia' },
          { id: 8, left: 'Germany', right: 'Berlin', category: 'Europe' }
        ],
        leftItems: ['France', 'Japan', 'Brazil', 'Egypt', 'Australia', 'Canada', 'India', 'Germany'],
        rightItems: ['Paris', 'Tokyo', 'Brasília', 'Cairo', 'Canberra', 'Ottawa', 'New Delhi', 'Berlin'],
        matches: [
          { leftId: 1, rightId: 1, isCorrect: false },
          { leftId: 1, rightId: 2, isCorrect: true }
        ],
        hints: [
          'Think about European countries first',
          'Consider major Asian cities',
          'Remember that some capitals are not the largest cities'
        ],
        stats: {
          pairCount: 8,
          complexity: 6.0,
          educationalValue: 8,
          estimatedSolveTime: 360
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateMatchingPairs).mockResolvedValue(mockResult)
      
      const result = await generateMatchingPairs(settings)
      
      expect(result).toBeDefined()
      expect(result.type).toBe('matching-pairs')
      expect(result.pairs).toHaveLength(8)
      expect(result.category).toBe('countries-capitals')
      expect(result.leftItems).toContain('France')
      expect(result.rightItems).toContain('Paris')
    })
  })

  describe('Cross-Puzzle Integration Tests', () => {
    it('should handle multiple puzzle types with consistent settings', () => {
      const commonSettings = {
        difficulty: 'medium' as const,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      expect(commonSettings.difficulty).toBe('medium')
      expect(commonSettings.textColor).toBe('#000000')
      expect(commonSettings.backgroundColor).toBe('#ffffff')
    })

    it('should validate difficulty levels across all puzzle types', () => {
      const difficulties = ['easy', 'medium', 'hard'] as const
      
      difficulties.forEach(difficulty => {
        expect(['easy', 'medium', 'hard']).toContain(difficulty)
      })
    })

    it('should validate common color schemes', () => {
      const colorSchemes = {
        classic: { text: '#000000', background: '#ffffff', accent: '#0066cc' },
        dark: { text: '#ffffff', background: '#000000', accent: '#66ccff' },
        colorful: { text: '#333333', background: '#f5f5f5', accent: '#ff6b6b' }
      }

      Object.values(colorSchemes).forEach(scheme => {
        expect(scheme.text).toMatch(/^#[0-9a-fA-F]{6}$/)
        expect(scheme.background).toMatch(/^#[0-9a-fA-F]{6}$/)
        expect(scheme.accent).toMatch(/^#[0-9a-fA-F]{6}$/)
      })
    })

    it('should ensure consistent educational value scoring', () => {
      const educationalValues = [6, 7, 8, 9, 10]
      
      educationalValues.forEach(value => {
        expect(value).toBeGreaterThanOrEqual(5)
        expect(value).toBeLessThanOrEqual(10)
      })
    })

    it('should validate estimated solve times are reasonable', () => {
      const solveTimes = [180, 240, 300, 360, 480, 600, 720, 900, 1200]
      
      solveTimes.forEach(time => {
        expect(time).toBeGreaterThan(0)
        expect(time).toBeLessThanOrEqual(1800) // Max 30 minutes
      })
    })
  })

  describe('Puzzle Statistics and Analytics', () => {
    it('should calculate complexity scores correctly', () => {
      const complexityScores = {
        easy: { min: 1.0, max: 4.0 },
        medium: { min: 4.0, max: 7.0 },
        hard: { min: 7.0, max: 10.0 }
      }

      Object.entries(complexityScores).forEach(([difficulty, range]) => {
        expect(range.min).toBeLessThan(range.max)
        expect(range.min).toBeGreaterThanOrEqual(1.0)
        expect(range.max).toBeLessThanOrEqual(10.0)
      })
    })

    it('should provide age-appropriate content', () => {
      const ageGroups = {
        elementary: { minAge: 6, maxAge: 11, puzzles: ['word-search', 'matching-pairs', 'word-scramble'] },
        middle: { minAge: 12, maxAge: 14, puzzles: ['crossword', 'logic-grid', 'math-story'] },
        high: { minAge: 15, maxAge: 18, puzzles: ['cryptogram', 'tangram', 'code-breaker'] }
      }

      Object.values(ageGroups).forEach(group => {
        expect(group.minAge).toBeLessThan(group.maxAge)
        expect(group.puzzles.length).toBeGreaterThan(0)
      })
    })

    it('should support accessibility features', () => {
      const accessibilityFeatures = [
        'high-contrast-mode',
        'large-font-options',
        'screen-reader-support',
        'keyboard-navigation',
        'colorblind-friendly-palettes'
      ]

      accessibilityFeatures.forEach(feature => {
        expect(feature).toMatch(/^[a-z-]+$/)
        expect(feature.length).toBeGreaterThan(5)
      })
    })
  })
})