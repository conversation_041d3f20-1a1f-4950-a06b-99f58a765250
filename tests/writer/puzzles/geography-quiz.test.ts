import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'

// Mock the generator function
vi.mock('../../../layers/writer/components/global/book/puzzles/geography-quiz/generator', () => ({
  generateGeographyQuiz: vi.fn()
}))

// Mock global composables
globalThis.ref = vi.fn((value) => ({ value }))
globalThis.computed = vi.fn((fn) => ({ value: fn() }))
globalThis.watch = vi.fn()
globalThis.defineEmits = vi.fn(() => vi.fn())
globalThis.defineProps = vi.fn()

describe('Geography Quiz Puzzle Components', () => {
  describe('Geography Quiz Generator', () => {
    it('should generate valid geography quiz', async () => {
      const { generateGeographyQuiz } = await import('../../../layers/writer/components/global/book/puzzles/geography-quiz/generator')
      
      const settings = {
        region: 'world' as const,
        questionType: 'capital' as const,
        difficulty: 'medium' as const,
        questionCount: 10,
        includeFlags: true,
        includeMaps: false,
        showHints: true,
        timeLimit: 300,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-geography-quiz-1',
        type: 'geography-quiz',
        region: 'world',
        questionType: 'capital',
        difficulty: 'medium' as const,
        questions: [
          {
            id: 1,
            question: 'What is the capital of France?',
            country: 'France',
            correctAnswer: 'Paris',
            options: ['Paris', 'London', 'Berlin', 'Madrid'],
            flagUrl: 'https://example.com/flags/france.png',
            hint: 'City of Light',
            explanation: 'Paris has been the capital of France since 987 AD.'
          },
          {
            id: 2,
            question: 'What is the capital of Japan?',
            country: 'Japan',
            correctAnswer: 'Tokyo',
            options: ['Tokyo', 'Kyoto', 'Osaka', 'Hiroshima'],
            flagUrl: 'https://example.com/flags/japan.png',
            hint: 'Formerly called Edo',
            explanation: 'Tokyo became the capital when the emperor moved there in 1868.'
          }
        ],
        timeLimit: 300,
        scoring: {
          correctAnswer: 10,
          partialCredit: 5,
          timeBonus: 2
        },
        stats: {
          questionCount: 2,
          averageDifficulty: 6.5,
          complexity: 5.5,
          educationalValue: 8,
          estimatedSolveTime: 120
        },
        instructions: [
          'Select the correct answer for each question',
          'You have 5 minutes to complete the quiz',
          'Use hints if you need help'
        ],
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateGeographyQuiz).mockResolvedValue(mockResult)
      
      const result = await generateGeographyQuiz(settings)
      
      expect(result).toBeDefined()
      expect(result.type).toBe('geography-quiz')
      expect(result.questions).toHaveLength(2)
      expect(result.questions[0].correctAnswer).toBe('Paris')
      expect(result.questions[0].options).toContain('Paris')
      expect(result.timeLimit).toBe(300)
    })

    it('should handle different regions', async () => {
      const { generateGeographyQuiz } = await import('../../../layers/writer/components/global/book/puzzles/geography-quiz/generator')
      
      const regions = ['world', 'europe', 'asia', 'africa', 'americas', 'oceania'] as const
      
      for (const region of regions) {
        const settings = {
          region,
          questionType: 'capital' as const,
          difficulty: 'medium' as const,
          questionCount: 5,
          includeFlags: true,
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00',
          fontSize: 'medium' as const,
          printLayout: 'compact' as const
        }

        const mockQuestions = region === 'europe' ? [
          { question: 'Capital of Germany?', country: 'Germany', correctAnswer: 'Berlin', continent: 'Europe' }
        ] : region === 'asia' ? [
          { question: 'Capital of China?', country: 'China', correctAnswer: 'Beijing', continent: 'Asia' }
        ] : [
          { question: 'Capital of Brazil?', country: 'Brazil', correctAnswer: 'Brasília', continent: 'South America' }
        ]

        const mockResult = {
          id: `test-geography-quiz-${region}`,
          type: 'geography-quiz',
          region,
          questionType: 'capital',
          difficulty: 'medium' as const,
          questions: mockQuestions.map((q, i) => ({
            id: i + 1,
            question: q.question,
            country: q.country,
            correctAnswer: q.correctAnswer,
            options: [q.correctAnswer, 'Option2', 'Option3', 'Option4'],
            continent: q.continent
          })),
          stats: {
            questionCount: 1,
            averageDifficulty: 5.0,
            complexity: 4.0,
            educationalValue: 7,
            estimatedSolveTime: 60
          },
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00'
        }

        vi.mocked(generateGeographyQuiz).mockResolvedValue(mockResult)
        
        const result = await generateGeographyQuiz(settings)
        expect(result.region).toBe(region)
        expect(result.questions.length).toBeGreaterThan(0)
      }
    })

    it('should handle different question types', async () => {
      const { generateGeographyQuiz } = await import('../../../layers/writer/components/global/book/puzzles/geography-quiz/generator')
      
      const questionTypes = ['capital', 'country', 'flag', 'landmark', 'river', 'mountain'] as const
      
      for (const questionType of questionTypes) {
        const settings = {
          region: 'world' as const,
          questionType,
          difficulty: 'medium' as const,
          questionCount: 3,
          includeFlags: questionType === 'flag',
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00',
          fontSize: 'medium' as const,
          printLayout: 'compact' as const
        }

        let mockQuestion: any = {
          id: 1,
          options: ['Option1', 'Option2', 'Option3', 'Option4']
        }

        switch (questionType) {
          case 'capital':
            mockQuestion = {
              ...mockQuestion,
              question: 'What is the capital of Italy?',
              country: 'Italy',
              correctAnswer: 'Rome'
            }
            break
          case 'country':
            mockQuestion = {
              ...mockQuestion,
              question: 'Which country has this outline?',
              correctAnswer: 'Australia',
              mapOutline: 'australia-outline.svg'
            }
            break
          case 'flag':
            mockQuestion = {
              ...mockQuestion,
              question: 'Which country does this flag belong to?',
              correctAnswer: 'Canada',
              flagUrl: 'canada-flag.png'
            }
            break
          case 'landmark':
            mockQuestion = {
              ...mockQuestion,
              question: 'In which country is the Eiffel Tower?',
              landmark: 'Eiffel Tower',
              correctAnswer: 'France'
            }
            break
          case 'river':
            mockQuestion = {
              ...mockQuestion,
              question: 'Which river flows through Egypt?',
              correctAnswer: 'Nile River'
            }
            break
          case 'mountain':
            mockQuestion = {
              ...mockQuestion,
              question: 'What is the highest mountain in the world?',
              correctAnswer: 'Mount Everest'
            }
            break
        }

        const mockResult = {
          id: `test-geography-quiz-${questionType}`,
          type: 'geography-quiz',
          region: 'world',
          questionType,
          difficulty: 'medium' as const,
          questions: [mockQuestion],
          stats: {
            questionCount: 1,
            averageDifficulty: 5.0,
            complexity: 4.0,
            educationalValue: 7,
            estimatedSolveTime: 60
          },
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00'
        }

        vi.mocked(generateGeographyQuiz).mockResolvedValue(mockResult)
        
        const result = await generateGeographyQuiz(settings)
        expect(result.questionType).toBe(questionType)
        expect(result.questions[0].correctAnswer).toBeDefined()
      }
    })

    it('should handle different difficulty levels', async () => {
      const { generateGeographyQuiz } = await import('../../../layers/writer/components/global/book/puzzles/geography-quiz/generator')
      
      const difficulties = ['easy', 'medium', 'hard'] as const
      
      for (const difficulty of difficulties) {
        const settings = {
          region: 'world' as const,
          questionType: 'capital' as const,
          difficulty,
          questionCount: difficulty === 'easy' ? 5 : difficulty === 'medium' ? 10 : 15,
          includeFlags: false,
          showHints: difficulty === 'easy',
          timeLimit: difficulty === 'easy' ? 600 : difficulty === 'medium' ? 300 : 180,
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00',
          fontSize: 'medium' as const,
          printLayout: 'compact' as const
        }

        const expectedQuestionCount = difficulty === 'easy' ? 5 : difficulty === 'medium' ? 10 : 15
        
        const mockResult = {
          id: `test-geography-quiz-${difficulty}`,
          type: 'geography-quiz',
          region: 'world',
          questionType: 'capital',
          difficulty,
          questions: Array.from({ length: expectedQuestionCount }, (_, i) => ({
            id: i + 1,
            question: `Capital question ${i + 1}`,
            country: `Country${i + 1}`,
            correctAnswer: `Capital${i + 1}`,
            options: [`Capital${i + 1}`, 'Wrong1', 'Wrong2', 'Wrong3'],
            hint: difficulty === 'easy' ? `Hint for capital ${i + 1}` : undefined
          })),
          timeLimit: difficulty === 'easy' ? 600 : difficulty === 'medium' ? 300 : 180,
          stats: {
            questionCount: expectedQuestionCount,
            averageDifficulty: difficulty === 'easy' ? 3.0 : difficulty === 'medium' ? 6.0 : 9.0,
            complexity: difficulty === 'easy' ? 3.0 : difficulty === 'medium' ? 6.0 : 9.0,
            educationalValue: 8,
            estimatedSolveTime: expectedQuestionCount * (difficulty === 'easy' ? 30 : difficulty === 'medium' ? 20 : 15)
          },
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00'
        }

        vi.mocked(generateGeographyQuiz).mockResolvedValue(mockResult)
        
        const result = await generateGeographyQuiz(settings)
        expect(result.difficulty).toBe(difficulty)
        expect(result.questions).toHaveLength(expectedQuestionCount)
        expect(result.stats.averageDifficulty).toBeGreaterThan(0)
      }
    })
  })

  describe('Geography Quiz Components Mock Tests', () => {
    let mockCustomizeComponent: any
    let mockPreviewComponent: any
    let mockSolutionComponent: any

    beforeEach(() => {
      mockCustomizeComponent = {
        template: `
          <div data-testid="geography-quiz-customize">
            <select v-model="region">
              <option value="world">World</option>
              <option value="europe">Europe</option>
              <option value="asia">Asia</option>
              <option value="africa">Africa</option>
              <option value="americas">Americas</option>
              <option value="oceania">Oceania</option>
            </select>
            <select v-model="questionType">
              <option value="capital">Capitals</option>
              <option value="country">Countries</option>
              <option value="flag">Flags</option>
              <option value="landmark">Landmarks</option>
              <option value="river">Rivers</option>
              <option value="mountain">Mountains</option>
            </select>
            <select v-model="difficulty">
              <option value="easy">Easy</option>
              <option value="medium">Medium</option>
              <option value="hard">Hard</option>
            </select>
            <input v-model="questionCount" type="number" min="5" max="20" />
            <label>
              <input type="checkbox" v-model="includeFlags" />
              Include Flag Images
            </label>
            <label>
              <input type="checkbox" v-model="includeMaps" />
              Include Map Outlines
            </label>
            <label>
              <input type="checkbox" v-model="showHints" />
              Show Hints
            </label>
            <input v-model="timeLimit" type="number" min="60" max="900" placeholder="Time limit (seconds)" />
          </div>
        `,
        props: ['modelValue'],
        emits: ['update:modelValue'],
        data() {
          return {
            region: 'world',
            questionType: 'capital',
            difficulty: 'medium',
            questionCount: 10,
            includeFlags: true,
            includeMaps: false,
            showHints: true,
            timeLimit: 300
          }
        }
      }

      mockPreviewComponent = {
        template: `
          <div data-testid="geography-quiz-preview">
            <div class="quiz-header">
              <h3>Geography Quiz: {{ region.charAt(0).toUpperCase() + region.slice(1) }}</h3>
              <div class="quiz-info">
                <span class="difficulty-badge">{{ difficulty.toUpperCase() }}</span>
                <span class="question-type">{{ questionType.charAt(0).toUpperCase() + questionType.slice(1) }}</span>
                <span v-if="timeLimit" class="time-limit">{{ Math.floor(timeLimit / 60) }}:{{ (timeLimit % 60).toString().padStart(2, '0') }}</span>
              </div>
            </div>
            <div class="quiz-content">
              <div v-for="(question, index) in questions" :key="question.id" class="question-item">
                <div class="question-number">{{ index + 1 }}.</div>
                <div class="question-content">
                  <h4>{{ question.question }}</h4>
                  <img v-if="question.flagUrl" :src="question.flagUrl" :alt="'Flag of ' + question.country" class="flag-image" />
                  <div class="answer-options">
                    <label v-for="option in question.options" :key="option" class="option">
                      <input type="radio" :name="'question-' + question.id" :value="option" />
                      {{ option }}
                    </label>
                  </div>
                  <div v-if="question.hint && showHints" class="hint">
                    💡 Hint: {{ question.hint }}
                  </div>
                </div>
              </div>
            </div>
            <div class="quiz-stats">
              <p>Total Questions: {{ stats?.questionCount || 0 }}</p>
              <p>Average Difficulty: {{ stats?.averageDifficulty?.toFixed(1) || 0 }}/10</p>
              <p>Estimated Time: {{ Math.floor((stats?.estimatedSolveTime || 0) / 60) }} minutes</p>
            </div>
            <div v-if="instructions" class="instructions">
              <h4>Instructions:</h4>
              <ul>
                <li v-for="instruction in instructions" :key="instruction">{{ instruction }}</li>
              </ul>
            </div>
          </div>
        `,
        props: ['questions', 'region', 'questionType', 'difficulty', 'timeLimit', 'showHints', 'stats', 'instructions']
      }

      mockSolutionComponent = {
        template: `
          <div data-testid="geography-quiz-solution">
            <div class="solution-header">
              <h3>Geography Quiz Answers</h3>
            </div>
            <div class="solution-content">
              <div v-for="(question, index) in questions" :key="question.id" class="solution-item">
                <div class="question-info">
                  <span class="question-number">{{ index + 1 }}.</span>
                  <span class="question-text">{{ question.question }}</span>
                </div>
                <div class="answer-info">
                  <span class="correct-answer">✓ {{ question.correctAnswer }}</span>
                  <div v-if="question.explanation" class="explanation">
                    {{ question.explanation }}
                  </div>
                </div>
                <div v-if="question.country" class="additional-info">
                  <span class="country">Country: {{ question.country }}</span>
                  <span v-if="question.continent" class="continent">Continent: {{ question.continent }}</span>
                  <span v-if="question.population" class="population">Population: {{ question.population }}</span>
                </div>
              </div>
            </div>
            <div v-if="scoring" class="scoring-info">
              <h4>Scoring System</h4>
              <ul>
                <li>Correct Answer: {{ scoring.correctAnswer }} points</li>
                <li v-if="scoring.partialCredit">Partial Credit: {{ scoring.partialCredit }} points</li>
                <li v-if="scoring.timeBonus">Time Bonus: {{ scoring.timeBonus }} points per second saved</li>
              </ul>
            </div>
          </div>
        `,
        props: ['questions', 'scoring']
      }
    })

    it('should render customize component', () => {
      const wrapper = mount(mockCustomizeComponent, {
        props: { modelValue: { region: 'world', questionType: 'capital', difficulty: 'medium' } }
      })
      
      expect(wrapper.find('[data-testid="geography-quiz-customize"]').exists()).toBe(true)
      expect(wrapper.findAll('select')).toHaveLength(3)
      expect(wrapper.findAll('input[type="number"]')).toHaveLength(2)
      expect(wrapper.findAll('input[type="checkbox"]')).toHaveLength(3)
    })

    it('should render preview component with questions', () => {
      const props = {
        questions: [
          {
            id: 1,
            question: 'What is the capital of France?',
            country: 'France',
            correctAnswer: 'Paris',
            options: ['Paris', 'London', 'Berlin', 'Madrid'],
            flagUrl: 'https://example.com/france.png',
            hint: 'City of Light'
          },
          {
            id: 2,
            question: 'What is the capital of Japan?',
            country: 'Japan',
            correctAnswer: 'Tokyo',
            options: ['Tokyo', 'Kyoto', 'Osaka', 'Hiroshima'],
            hint: 'Formerly called Edo'
          }
        ],
        region: 'world',
        questionType: 'capital',
        difficulty: 'medium',
        timeLimit: 300,
        showHints: true,
        stats: {
          questionCount: 2,
          averageDifficulty: 6.0,
          estimatedSolveTime: 120
        },
        instructions: ['Select the correct answer', 'Use hints if needed']
      }
      
      const wrapper = mount(mockPreviewComponent, { props })
      
      expect(wrapper.find('[data-testid="geography-quiz-preview"]').exists()).toBe(true)
      expect(wrapper.find('.quiz-header').exists()).toBe(true)
      expect(wrapper.findAll('.question-item')).toHaveLength(2)
      expect(wrapper.find('.flag-image').exists()).toBe(true)
      expect(wrapper.text()).toContain('What is the capital of France?')
      expect(wrapper.text()).toContain('City of Light')
      expect(wrapper.text()).toContain('5:00') // 300 seconds = 5:00
    })

    it('should render solution component with answers and explanations', () => {
      const props = {
        questions: [
          {
            id: 1,
            question: 'What is the capital of Australia?',
            correctAnswer: 'Canberra',
            country: 'Australia',
            continent: 'Oceania',
            population: '25.7 million',
            explanation: 'Canberra was chosen as a compromise between Sydney and Melbourne.'
          },
          {
            id: 2,
            question: 'Which river is the longest in the world?',
            correctAnswer: 'Nile River',
            explanation: 'The Nile River is approximately 6,650 km long.'
          }
        ],
        scoring: {
          correctAnswer: 10,
          partialCredit: 5,
          timeBonus: 2
        }
      }
      
      const wrapper = mount(mockSolutionComponent, { props })
      
      expect(wrapper.find('[data-testid="geography-quiz-solution"]').exists()).toBe(true)
      expect(wrapper.find('.solution-content').exists()).toBe(true)
      expect(wrapper.findAll('.solution-item')).toHaveLength(2)
      expect(wrapper.text()).toContain('Canberra')
      expect(wrapper.text()).toContain('Nile River')
      expect(wrapper.text()).toContain('compromise between Sydney and Melbourne')
      expect(wrapper.find('.scoring-info').exists()).toBe(true)
    })
  })

  describe('Geography Quiz Settings Validation', () => {
    it('should validate question count constraints', () => {
      const validCounts = [5, 10, 15, 20]
      const invalidCounts = [1, 3, 25, 50]
      
      validCounts.forEach(count => {
        expect(count).toBeGreaterThanOrEqual(5)
        expect(count).toBeLessThanOrEqual(20)
      })
      
      invalidCounts.forEach(count => {
        expect(count < 5 || count > 20).toBe(true)
      })
    })

    it('should validate region options', () => {
      const validRegions = ['world', 'europe', 'asia', 'africa', 'americas', 'oceania']
      const invalidRegions = ['invalid', '', 'antarctica']
      
      validRegions.forEach(region => {
        expect(['world', 'europe', 'asia', 'africa', 'americas', 'oceania']).toContain(region)
      })
      
      invalidRegions.forEach(region => {
        expect(['world', 'europe', 'asia', 'africa', 'americas', 'oceania']).not.toContain(region)
      })
    })

    it('should validate question type options', () => {
      const validTypes = ['capital', 'country', 'flag', 'landmark', 'river', 'mountain']
      const invalidTypes = ['invalid', '', 'ocean']
      
      validTypes.forEach(type => {
        expect(['capital', 'country', 'flag', 'landmark', 'river', 'mountain']).toContain(type)
      })
      
      invalidTypes.forEach(type => {
        expect(['capital', 'country', 'flag', 'landmark', 'river', 'mountain']).not.toContain(type)
      })
    })

    it('should validate time limit constraints', () => {
      const validTimeLimits = [60, 180, 300, 600, 900]
      const invalidTimeLimits = [30, 1200, 3600]
      
      validTimeLimits.forEach(limit => {
        expect(limit).toBeGreaterThanOrEqual(60)
        expect(limit).toBeLessThanOrEqual(900)
      })
      
      invalidTimeLimits.forEach(limit => {
        expect(limit < 60 || limit > 900).toBe(true)
      })
    })
  })

  describe('Geography Quiz Edge Cases', () => {
    it('should handle minimum question count', async () => {
      const { generateGeographyQuiz } = await import('../../../layers/writer/components/global/book/puzzles/geography-quiz/generator')
      
      const settings = {
        region: 'world' as const,
        questionType: 'capital' as const,
        difficulty: 'easy' as const,
        questionCount: 5, // Minimum
        includeFlags: false,
        showHints: true,
        timeLimit: 600,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-geography-quiz-min',
        type: 'geography-quiz',
        region: 'world',
        questionType: 'capital',
        difficulty: 'easy' as const,
        questions: Array.from({ length: 5 }, (_, i) => ({
          id: i + 1,
          question: `What is the capital of Country${i + 1}?`,
          country: `Country${i + 1}`,
          correctAnswer: `Capital${i + 1}`,
          options: [`Capital${i + 1}`, 'Wrong1', 'Wrong2', 'Wrong3'],
          hint: `Easy hint for country ${i + 1}`
        })),
        timeLimit: 600,
        stats: {
          questionCount: 5,
          averageDifficulty: 2.0,
          complexity: 2.0,
          educationalValue: 6,
          estimatedSolveTime: 150
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateGeographyQuiz).mockResolvedValue(mockResult)
      
      const result = await generateGeographyQuiz(settings)
      expect(result.questions).toHaveLength(5)
      expect(result.questions.every(q => q.hint)).toBe(true)
      expect(result.timeLimit).toBe(600)
    })

    it('should handle flag-only questions', async () => {
      const { generateGeographyQuiz } = await import('../../../layers/writer/components/global/book/puzzles/geography-quiz/generator')
      
      const settings = {
        region: 'europe' as const,
        questionType: 'flag' as const,
        difficulty: 'hard' as const,
        questionCount: 8,
        includeFlags: true,
        includeMaps: false,
        showHints: false,
        timeLimit: 240,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-geography-quiz-flags',
        type: 'geography-quiz',
        region: 'europe',
        questionType: 'flag',
        difficulty: 'hard' as const,
        questions: [
          {
            id: 1,
            question: 'Which country does this flag belong to?',
            correctAnswer: 'Netherlands',
            options: ['Netherlands', 'Luxembourg', 'France', 'Belgium'],
            flagUrl: 'https://example.com/flags/netherlands.png',
            flagColors: ['red', 'white', 'blue'],
            continent: 'Europe'
          },
          {
            id: 2,
            question: 'Which country does this flag belong to?',
            correctAnswer: 'Estonia',
            options: ['Estonia', 'Latvia', 'Lithuania', 'Finland'],
            flagUrl: 'https://example.com/flags/estonia.png',
            flagColors: ['blue', 'black', 'white'],
            continent: 'Europe'
          }
        ],
        timeLimit: 240,
        stats: {
          questionCount: 2,
          averageDifficulty: 8.5,
          complexity: 8.0,
          educationalValue: 7,
          estimatedSolveTime: 120
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateGeographyQuiz).mockResolvedValue(mockResult)
      
      const result = await generateGeographyQuiz(settings)
      expect(result.questionType).toBe('flag')
      expect(result.questions.every(q => q.flagUrl)).toBe(true)
      expect(result.questions.every(q => q.flagColors)).toBe(true)
    })

    it('should handle landmark questions with cultural context', async () => {
      const { generateGeographyQuiz } = await import('../../../layers/writer/components/global/book/puzzles/geography-quiz/generator')
      
      const settings = {
        region: 'world' as const,
        questionType: 'landmark' as const,
        difficulty: 'medium' as const,
        questionCount: 6,
        includeFlags: false,
        includeMaps: true,
        showHints: true,
        timeLimit: 360,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-geography-quiz-landmarks',
        type: 'geography-quiz',
        region: 'world',
        questionType: 'landmark',
        difficulty: 'medium' as const,
        questions: [
          {
            id: 1,
            question: 'In which country is Machu Picchu located?',
            landmark: 'Machu Picchu',
            correctAnswer: 'Peru',
            options: ['Peru', 'Bolivia', 'Ecuador', 'Colombia'],
            hint: 'Ancient Inca citadel in South America',
            explanation: 'Machu Picchu is a 15th-century Inca citadel in the Andes Mountains.',
            culturalSignificance: 'UNESCO World Heritage Site',
            coordinates: { lat: -13.1631, lng: -72.5450 }
          }
        ],
        timeLimit: 360,
        stats: {
          questionCount: 1,
          averageDifficulty: 6.0,
          complexity: 6.5,
          educationalValue: 9,
          estimatedSolveTime: 90
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateGeographyQuiz).mockResolvedValue(mockResult)
      
      const result = await generateGeographyQuiz(settings)
      expect(result.questionType).toBe('landmark')
      expect(result.questions[0].landmark).toBe('Machu Picchu')
      expect(result.questions[0].culturalSignificance).toBeDefined()
      expect(result.questions[0].coordinates).toBeDefined()
    })
  })
})