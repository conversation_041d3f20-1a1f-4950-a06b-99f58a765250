import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'

// Mock the generator function
vi.mock('../../../layers/writer/components/global/book/puzzles/maze/generator', () => ({
  generateMaze: vi.fn()
}))

// Mock global composables
globalThis.ref = vi.fn((value) => ({ value }))
globalThis.computed = vi.fn((fn) => ({ value: fn() }))
globalThis.watch = vi.fn()
globalThis.defineEmits = vi.fn(() => vi.fn())
globalThis.defineProps = vi.fn()

describe('Maze Puzzle Components', () => {
  describe('Maze Generator', () => {
    it('should generate valid maze puzzle', async () => {
      const { generateMaze } = await import('../../../layers/writer/components/global/book/puzzles/maze/generator')
      
      const settings = {
        size: 15,
        difficulty: 'medium' as const,
        mazeType: 'traditional' as const,
        pathWidth: 1,
        includeDeadEnds: true,
        showSolution: false,
        theme: 'classic',
        multipleExits: false,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-maze-1',
        type: 'maze',
        size: 15,
        difficulty: 'medium' as const,
        mazeType: 'traditional',
        grid: Array.from({ length: 15 }, (_, row) => 
          Array.from({ length: 15 }, (_, col) => ({
            x: col,
            y: row,
            isWall: (row === 0 || row === 14 || col === 0 || col === 14) && !(row === 1 && col === 0) && !(row === 13 && col === 14),
            isPath: !((row === 0 || row === 14 || col === 0 || col === 14) && !(row === 1 && col === 0) && !(row === 13 && col === 14)),
            isStart: row === 1 && col === 0,
            isEnd: row === 13 && col === 14,
            isDeadEnd: false,
            neighbors: []
          }))
        ),
        startPosition: { x: 0, y: 1 },
        endPosition: { x: 14, y: 13 },
        solution: [
          { x: 0, y: 1 },
          { x: 1, y: 1 },
          { x: 2, y: 1 },
          { x: 14, y: 13 }
        ],
        deadEnds: [
          { x: 5, y: 7 },
          { x: 9, y: 3 }
        ],
        stats: {
          pathLength: 42,
          deadEndCount: 2,
          complexity: 6.5,
          solutionSteps: 4,
          educationalValue: 7,
          estimatedSolveTime: 300
        },
        instructions: [
          'Find the path from start to finish',
          'You cannot pass through walls',
          'Use logic to avoid dead ends'
        ],
        theme: 'classic',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateMaze).mockResolvedValue(mockResult)
      
      const result = await generateMaze(settings)
      
      expect(result).toBeDefined()
      expect(result.type).toBe('maze')
      expect(result.size).toBe(15)
      expect(result.grid).toHaveLength(15)
      expect(result.grid[0]).toHaveLength(15)
      expect(result.startPosition).toEqual({ x: 0, y: 1 })
      expect(result.endPosition).toEqual({ x: 14, y: 13 })
      expect(result.solution).toHaveLength(4)
    })

    it('should handle different maze sizes', async () => {
      const { generateMaze } = await import('../../../layers/writer/components/global/book/puzzles/maze/generator')
      
      const sizes = [10, 15, 20, 25]
      
      for (const size of sizes) {
        const settings = {
          size,
          difficulty: 'medium' as const,
          mazeType: 'traditional' as const,
          pathWidth: 1,
          includeDeadEnds: true,
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00',
          fontSize: 'medium' as const,
          printLayout: 'compact' as const
        }

        const mockResult = {
          id: `test-maze-size-${size}`,
          type: 'maze',
          size,
          difficulty: 'medium' as const,
          mazeType: 'traditional',
          grid: Array.from({ length: size }, (_, row) => 
            Array.from({ length: size }, (_, col) => ({
              x: col,
              y: row,
              isWall: Math.random() > 0.7,
              isPath: Math.random() <= 0.7,
              isStart: row === 1 && col === 0,
              isEnd: row === size - 2 && col === size - 1
            }))
          ),
          startPosition: { x: 0, y: 1 },
          endPosition: { x: size - 1, y: size - 2 },
          solution: [],
          stats: {
            pathLength: size * 2,
            deadEndCount: Math.floor(size / 5),
            complexity: size / 3,
            solutionSteps: size,
            educationalValue: 7,
            estimatedSolveTime: size * 20
          },
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00'
        }

        vi.mocked(generateMaze).mockResolvedValue(mockResult)
        
        const result = await generateMaze(settings)
        expect(result.size).toBe(size)
        expect(result.grid).toHaveLength(size)
        expect(result.grid[0]).toHaveLength(size)
      }
    })

    it('should handle different maze types', async () => {
      const { generateMaze } = await import('../../../layers/writer/components/global/book/puzzles/maze/generator')
      
      const mazeTypes = ['traditional', 'circular', 'triangular', 'hexagonal', 'recursive'] as const
      
      for (const mazeType of mazeTypes) {
        const settings = {
          size: 15,
          difficulty: 'medium' as const,
          mazeType,
          pathWidth: 1,
          includeDeadEnds: true,
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00',
          fontSize: 'medium' as const,
          printLayout: 'compact' as const
        }

        const mockResult = {
          id: `test-maze-${mazeType}`,
          type: 'maze',
          size: 15,
          difficulty: 'medium' as const,
          mazeType,
          grid: Array.from({ length: 15 }, (_, row) => 
            Array.from({ length: 15 }, (_, col) => ({
              x: col,
              y: row,
              isWall: Math.random() > 0.6,
              isPath: Math.random() <= 0.6,
              shape: mazeType === 'hexagonal' ? 'hexagon' : 
                     mazeType === 'circular' ? 'circle' :
                     mazeType === 'triangular' ? 'triangle' : 'square'
            }))
          ),
          startPosition: { x: 0, y: 1 },
          endPosition: { x: 14, y: 13 },
          solution: [],
          algorithm: mazeType === 'recursive' ? 'recursive-backtracking' :
                    mazeType === 'circular' ? 'polar-coordinate' :
                    'depth-first-search',
          stats: {
            pathLength: 30,
            complexity: mazeType === 'hexagonal' ? 8.0 : 
                       mazeType === 'circular' ? 7.5 :
                       mazeType === 'triangular' ? 7.0 : 6.0,
            educationalValue: 7,
            estimatedSolveTime: 400
          },
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00'
        }

        vi.mocked(generateMaze).mockResolvedValue(mockResult)
        
        const result = await generateMaze(settings)
        expect(result.mazeType).toBe(mazeType)
        expect(result.algorithm).toBeDefined()
      }
    })

    it('should handle different difficulty levels', async () => {
      const { generateMaze } = await import('../../../layers/writer/components/global/book/puzzles/maze/generator')
      
      const difficulties = ['easy', 'medium', 'hard'] as const
      
      for (const difficulty of difficulties) {
        const settings = {
          size: difficulty === 'easy' ? 10 : difficulty === 'medium' ? 15 : 20,
          difficulty,
          mazeType: 'traditional' as const,
          pathWidth: difficulty === 'easy' ? 2 : 1,
          includeDeadEnds: difficulty !== 'easy',
          showSolution: difficulty === 'easy',
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00',
          fontSize: 'medium' as const,
          printLayout: 'compact' as const
        }

        const expectedSize = difficulty === 'easy' ? 10 : difficulty === 'medium' ? 15 : 20
        
        const mockResult = {
          id: `test-maze-${difficulty}`,
          type: 'maze',
          size: expectedSize,
          difficulty,
          mazeType: 'traditional',
          grid: Array.from({ length: expectedSize }, () => 
            Array.from({ length: expectedSize }, () => ({
              isWall: Math.random() > (difficulty === 'easy' ? 0.3 : difficulty === 'medium' ? 0.5 : 0.7),
              isPath: true
            }))
          ),
          startPosition: { x: 0, y: 1 },
          endPosition: { x: expectedSize - 1, y: expectedSize - 2 },
          solution: difficulty === 'easy' ? [{ x: 0, y: 1 }, { x: expectedSize - 1, y: expectedSize - 2 }] : [],
          stats: {
            pathLength: expectedSize * (difficulty === 'easy' ? 1.5 : difficulty === 'medium' ? 2.0 : 2.5),
            deadEndCount: difficulty === 'easy' ? 0 : difficulty === 'medium' ? 3 : 8,
            complexity: difficulty === 'easy' ? 3.0 : difficulty === 'medium' ? 6.0 : 9.0,
            solutionSteps: expectedSize,
            educationalValue: 7,
            estimatedSolveTime: difficulty === 'easy' ? 180 : difficulty === 'medium' ? 360 : 600
          },
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00'
        }

        vi.mocked(generateMaze).mockResolvedValue(mockResult)
        
        const result = await generateMaze(settings)
        expect(result.difficulty).toBe(difficulty)
        expect(result.size).toBe(expectedSize)
        expect(result.stats.complexity).toBeGreaterThan(0)
      }
    })

    it('should handle multiple exits maze', async () => {
      const { generateMaze } = await import('../../../layers/writer/components/global/book/puzzles/maze/generator')
      
      const settings = {
        size: 20,
        difficulty: 'hard' as const,
        mazeType: 'traditional' as const,
        pathWidth: 1,
        includeDeadEnds: true,
        multipleExits: true,
        numberOfExits: 3,
        theme: 'adventure',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-maze-multiple-exits',
        type: 'maze',
        size: 20,
        difficulty: 'hard' as const,
        mazeType: 'traditional',
        grid: Array.from({ length: 20 }, () => 
          Array.from({ length: 20 }, () => ({
            isWall: Math.random() > 0.6,
            isPath: true
          }))
        ),
        startPosition: { x: 0, y: 1 },
        endPositions: [
          { x: 19, y: 18, label: 'Exit A', reward: 'Treasure' },
          { x: 9, y: 19, label: 'Exit B', reward: 'Key' },
          { x: 19, y: 9, label: 'Exit C', reward: 'Map' }
        ],
        solutions: [
          { exitLabel: 'Exit A', path: [{ x: 0, y: 1 }, { x: 19, y: 18 }], steps: 35 },
          { exitLabel: 'Exit B', path: [{ x: 0, y: 1 }, { x: 9, y: 19 }], steps: 28 },
          { exitLabel: 'Exit C', path: [{ x: 0, y: 1 }, { x: 19, y: 9 }], steps: 32 }
        ],
        stats: {
          pathLength: 50,
          deadEndCount: 12,
          complexity: 9.5,
          solutionSteps: 28, // Shortest path
          educationalValue: 9,
          estimatedSolveTime: 900
        },
        theme: 'adventure',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateMaze).mockResolvedValue(mockResult)
      
      const result = await generateMaze(settings)
      expect(result.endPositions).toHaveLength(3)
      expect(result.solutions).toHaveLength(3)
      expect(result.endPositions[0].label).toBe('Exit A')
      expect(result.endPositions[0].reward).toBe('Treasure')
    })
  })

  describe('Maze Components Mock Tests', () => {
    let mockCustomizeComponent: any
    let mockPreviewComponent: any
    let mockSolutionComponent: any

    beforeEach(() => {
      mockCustomizeComponent = {
        template: `
          <div data-testid="maze-customize">
            <input v-model="size" type="number" min="10" max="30" />
            <select v-model="difficulty">
              <option value="easy">Easy</option>
              <option value="medium">Medium</option>
              <option value="hard">Hard</option>
            </select>
            <select v-model="mazeType">
              <option value="traditional">Traditional</option>
              <option value="circular">Circular</option>
              <option value="triangular">Triangular</option>
              <option value="hexagonal">Hexagonal</option>
              <option value="recursive">Recursive</option>
            </select>
            <input v-model="pathWidth" type="number" min="1" max="3" />
            <label>
              <input type="checkbox" v-model="includeDeadEnds" />
              Include Dead Ends
            </label>
            <label>
              <input type="checkbox" v-model="showSolution" />
              Show Solution
            </label>
            <label>
              <input type="checkbox" v-model="multipleExits" />
              Multiple Exits
            </label>
            <input v-if="multipleExits" v-model="numberOfExits" type="number" min="2" max="5" />
            <select v-model="theme">
              <option value="classic">Classic</option>
              <option value="adventure">Adventure</option>
              <option value="fantasy">Fantasy</option>
              <option value="sci-fi">Sci-Fi</option>
            </select>
          </div>
        `,
        props: ['modelValue'],
        emits: ['update:modelValue'],
        data() {
          return {
            size: 15,
            difficulty: 'medium',
            mazeType: 'traditional',
            pathWidth: 1,
            includeDeadEnds: true,
            showSolution: false,
            multipleExits: false,
            numberOfExits: 2,
            theme: 'classic'
          }
        }
      }

      mockPreviewComponent = {
        template: `
          <div data-testid="maze-preview">
            <div class="maze-header">
              <h3>Maze Challenge</h3>
              <div class="maze-info">
                <span class="difficulty-badge">{{ difficulty.toUpperCase() }}</span>
                <span class="size-info">{{ size }}×{{ size }}</span>
                <span class="type-info">{{ mazeType.charAt(0).toUpperCase() + mazeType.slice(1) }}</span>
              </div>
            </div>
            <div class="maze-container" :style="{ gridTemplateColumns: 'repeat(' + size + ', 1fr)' }">
              <div v-for="(row, rowIndex) in grid" :key="rowIndex" class="maze-row">
                <div v-for="(cell, colIndex) in row" :key="colIndex" 
                     class="maze-cell"
                     :class="{
                       'wall': cell.isWall,
                       'path': cell.isPath,
                       'start': cell.isStart,
                       'end': cell.isEnd,
                       'dead-end': cell.isDeadEnd,
                       'solution': showSolution && isSolutionCell(rowIndex, colIndex)
                     }">
                  <span v-if="cell.isStart">S</span>
                  <span v-else-if="cell.isEnd">E</span>
                  <span v-else-if="endPositions && endPositions.some(pos => pos.x === colIndex && pos.y === rowIndex)">
                    {{ getExitLabel(colIndex, rowIndex) }}
                  </span>
                </div>
              </div>
            </div>
            <div class="maze-legend">
              <div class="legend-item">
                <span class="legend-symbol start">S</span>
                <span>Start</span>
              </div>
              <div class="legend-item">
                <span class="legend-symbol end">E</span>
                <span>End</span>
              </div>
              <div class="legend-item">
                <span class="legend-symbol wall">█</span>
                <span>Wall</span>
              </div>
              <div class="legend-item">
                <span class="legend-symbol path">·</span>
                <span>Path</span>
              </div>
            </div>
            <div v-if="endPositions" class="exits-info">
              <h4>Multiple Exits Available:</h4>
              <ul>
                <li v-for="exit in endPositions" :key="exit.label">
                  {{ exit.label }}: {{ exit.reward }}
                </li>
              </ul>
            </div>
            <div class="maze-stats">
              <p>Size: {{ size }}×{{ size }}</p>
              <p>Complexity: {{ stats?.complexity?.toFixed(1) || 0 }}/10</p>
              <p>Dead Ends: {{ stats?.deadEndCount || 0 }}</p>
              <p>Estimated Time: {{ Math.floor((stats?.estimatedSolveTime || 0) / 60) }} minutes</p>
            </div>
            <div v-if="instructions" class="instructions">
              <h4>Instructions:</h4>
              <ul>
                <li v-for="instruction in instructions" :key="instruction">{{ instruction }}</li>
              </ul>
            </div>
          </div>
        `,
        props: ['grid', 'size', 'difficulty', 'mazeType', 'showSolution', 'solution', 'endPositions', 'stats', 'instructions'],
        methods: {
          isSolutionCell(row: number, col: number) {
            return this.solution && this.solution.some((pos: any) => pos.x === col && pos.y === row)
          },
          getExitLabel(x: number, y: number) {
            const exit = this.endPositions?.find((pos: any) => pos.x === x && pos.y === y)
            return exit ? exit.label.split(' ')[1] : 'E'
          }
        }
      }

      mockSolutionComponent = {
        template: `
          <div data-testid="maze-solution">
            <div class="solution-header">
              <h3>Maze Solution</h3>
            </div>
            <div class="solution-content">
              <div class="solved-maze">
                <div class="maze-grid" :style="{ gridTemplateColumns: 'repeat(' + size + ', 1fr)' }">
                  <div v-for="(row, rowIndex) in grid" :key="rowIndex" class="maze-row">
                    <div v-for="(cell, colIndex) in row" :key="colIndex" 
                         class="maze-cell solved"
                         :class="{
                           'wall': cell.isWall,
                           'path': cell.isPath,
                           'start': cell.isStart,
                           'end': cell.isEnd,
                           'solution-path': isSolutionCell(rowIndex, colIndex)
                         }">
                      <span v-if="cell.isStart">S</span>
                      <span v-else-if="cell.isEnd">E</span>
                      <span v-else-if="isSolutionCell(rowIndex, colIndex)">●</span>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="solutions" class="multiple-solutions">
                <h4>All Solutions:</h4>
                <div v-for="sol in solutions" :key="sol.exitLabel" class="solution-path">
                  <h5>{{ sol.exitLabel }} ({{ sol.steps }} steps)</h5>
                  <p>Shortest path to {{ sol.exitLabel.toLowerCase() }}</p>
                </div>
              </div>
              <div v-else class="single-solution">
                <h4>Solution Path:</h4>
                <p>{{ solution?.length || 0 }} steps from start to finish</p>
                <p>Optimal path highlighted in the maze above</p>
              </div>
              <div class="solution-analysis">
                <h4>Maze Analysis:</h4>
                <ul>
                  <li>Algorithm used: {{ algorithm || 'Depth-first search' }}</li>
                  <li>Total path length: {{ stats?.pathLength || 0 }}</li>
                  <li>Dead ends: {{ stats?.deadEndCount || 0 }}</li>
                  <li>Complexity rating: {{ stats?.complexity?.toFixed(1) || 0 }}/10</li>
                </ul>
              </div>
            </div>
          </div>
        `,
        props: ['grid', 'size', 'solution', 'solutions', 'stats', 'algorithm'],
        methods: {
          isSolutionCell(row: number, col: number) {
            if (this.solution) {
              return this.solution.some((pos: any) => pos.x === col && pos.y === row)
            }
            return false
          }
        }
      }
    })

    it('should render customize component', () => {
      const wrapper = mount(mockCustomizeComponent, {
        props: { modelValue: { size: 15, difficulty: 'medium', mazeType: 'traditional' } }
      })
      
      expect(wrapper.find('[data-testid="maze-customize"]').exists()).toBe(true)
      expect(wrapper.findAll('select')).toHaveLength(3)
      expect(wrapper.findAll('input[type="number"]')).toHaveLength(2)
      expect(wrapper.findAll('input[type="checkbox"]')).toHaveLength(3)
    })

    it('should render preview component with maze grid', () => {
      const props = {
        grid: Array.from({ length: 5 }, (_, row) => 
          Array.from({ length: 5 }, (_, col) => ({
            isWall: (row === 0 || row === 4 || col === 0 || col === 4) && !(row === 1 && col === 0) && !(row === 3 && col === 4),
            isPath: !((row === 0 || row === 4 || col === 0 || col === 4) && !(row === 1 && col === 0) && !(row === 3 && col === 4)),
            isStart: row === 1 && col === 0,
            isEnd: row === 3 && col === 4,
            isDeadEnd: false
          }))
        ),
        size: 5,
        difficulty: 'easy',
        mazeType: 'traditional',
        showSolution: true,
        solution: [{ x: 0, y: 1 }, { x: 1, y: 1 }, { x: 4, y: 3 }],
        stats: {
          complexity: 3.5,
          deadEndCount: 1,
          estimatedSolveTime: 180
        },
        instructions: ['Find the path from S to E', 'You cannot pass through walls']
      }
      
      const wrapper = mount(mockPreviewComponent, { props })
      
      expect(wrapper.find('[data-testid="maze-preview"]').exists()).toBe(true)
      expect(wrapper.find('.maze-container').exists()).toBe(true)
      expect(wrapper.find('.maze-legend').exists()).toBe(true)
      expect(wrapper.findAll('.maze-cell')).toHaveLength(25) // 5x5 grid
      expect(wrapper.text()).toContain('EASY')
      expect(wrapper.text()).toContain('5×5')
      expect(wrapper.text()).toContain('Traditional')
    })

    it('should render solution component with solved maze', () => {
      const props = {
        grid: Array.from({ length: 3 }, (_, row) => 
          Array.from({ length: 3 }, (_, col) => ({
            isWall: row === 1 && col === 1,
            isPath: !(row === 1 && col === 1),
            isStart: row === 0 && col === 0,
            isEnd: row === 2 && col === 2
          }))
        ),
        size: 3,
        solution: [{ x: 0, y: 0 }, { x: 1, y: 0 }, { x: 2, y: 0 }, { x: 2, y: 1 }, { x: 2, y: 2 }],
        stats: {
          pathLength: 8,
          deadEndCount: 0,
          complexity: 2.5
        },
        algorithm: 'depth-first-search'
      }
      
      const wrapper = mount(mockSolutionComponent, { props })
      
      expect(wrapper.find('[data-testid="maze-solution"]').exists()).toBe(true)
      expect(wrapper.find('.solved-maze').exists()).toBe(true)
      expect(wrapper.find('.solution-analysis').exists()).toBe(true)
      expect(wrapper.text()).toContain('5 steps from start to finish')
      expect(wrapper.text()).toContain('depth-first-search')
      expect(wrapper.text()).toContain('Total path length: 8')
    })
  })

  describe('Maze Settings Validation', () => {
    it('should validate size constraints', () => {
      const validSizes = [10, 15, 20, 25, 30]
      const invalidSizes = [5, 8, 35, 50]
      
      validSizes.forEach(size => {
        expect(size).toBeGreaterThanOrEqual(10)
        expect(size).toBeLessThanOrEqual(30)
      })
      
      invalidSizes.forEach(size => {
        expect(size < 10 || size > 30).toBe(true)
      })
    })

    it('should validate maze type options', () => {
      const validTypes = ['traditional', 'circular', 'triangular', 'hexagonal', 'recursive']
      const invalidTypes = ['invalid', '', 'diamond']
      
      validTypes.forEach(type => {
        expect(['traditional', 'circular', 'triangular', 'hexagonal', 'recursive']).toContain(type)
      })
      
      invalidTypes.forEach(type => {
        expect(['traditional', 'circular', 'triangular', 'hexagonal', 'recursive']).not.toContain(type)
      })
    })

    it('should validate path width constraints', () => {
      const validWidths = [1, 2, 3]
      const invalidWidths = [0, 4, 5]
      
      validWidths.forEach(width => {
        expect(width).toBeGreaterThanOrEqual(1)
        expect(width).toBeLessThanOrEqual(3)
      })
      
      invalidWidths.forEach(width => {
        expect(width < 1 || width > 3).toBe(true)
      })
    })
  })

  describe('Maze Edge Cases', () => {
    it('should handle minimum size maze', async () => {
      const { generateMaze } = await import('../../../layers/writer/components/global/book/puzzles/maze/generator')
      
      const settings = {
        size: 10, // Minimum
        difficulty: 'easy' as const,
        mazeType: 'traditional' as const,
        pathWidth: 2,
        includeDeadEnds: false,
        showSolution: true,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-maze-min-size',
        type: 'maze',
        size: 10,
        difficulty: 'easy' as const,
        mazeType: 'traditional',
        grid: Array.from({ length: 10 }, () => 
          Array.from({ length: 10 }, () => ({
            isWall: Math.random() > 0.8, // Fewer walls for easy maze
            isPath: true
          }))
        ),
        startPosition: { x: 0, y: 1 },
        endPosition: { x: 9, y: 8 },
        solution: [{ x: 0, y: 1 }, { x: 9, y: 8 }],
        deadEnds: [],
        stats: {
          pathLength: 18,
          deadEndCount: 0,
          complexity: 2.0,
          solutionSteps: 2,
          educationalValue: 5,
          estimatedSolveTime: 120
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateMaze).mockResolvedValue(mockResult)
      
      const result = await generateMaze(settings)
      expect(result.size).toBe(10)
      expect(result.deadEnds).toHaveLength(0)
      expect(result.stats.complexity).toBeLessThan(3)
    })

    it('should handle circular maze generation', async () => {
      const { generateMaze } = await import('../../../layers/writer/components/global/book/puzzles/maze/generator')
      
      const settings = {
        size: 20,
        difficulty: 'hard' as const,
        mazeType: 'circular' as const,
        pathWidth: 1,
        includeDeadEnds: true,
        theme: 'fantasy',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-maze-circular',
        type: 'maze',
        size: 20,
        difficulty: 'hard' as const,
        mazeType: 'circular',
        grid: Array.from({ length: 20 }, (_, row) => 
          Array.from({ length: 20 }, (_, col) => ({
            x: col,
            y: row,
            isWall: Math.random() > 0.5,
            isPath: Math.random() <= 0.5,
            radius: Math.sqrt((col - 10) ** 2 + (row - 10) ** 2), // Distance from center
            angle: Math.atan2(row - 10, col - 10), // Angle from center
            inBounds: Math.sqrt((col - 10) ** 2 + (row - 10) ** 2) <= 9 // Within circle
          }))
        ),
        startPosition: { x: 10, y: 1 }, // Top of circle
        endPosition: { x: 10, y: 19 }, // Bottom of circle
        solution: [],
        algorithm: 'polar-coordinate',
        stats: {
          pathLength: 45,
          deadEndCount: 8,
          complexity: 8.5,
          solutionSteps: 38,
          educationalValue: 9,
          estimatedSolveTime: 720
        },
        theme: 'fantasy',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateMaze).mockResolvedValue(mockResult)
      
      const result = await generateMaze(settings)
      expect(result.mazeType).toBe('circular')
      expect(result.algorithm).toBe('polar-coordinate')
      expect(result.grid[0][0].radius).toBeDefined()
      expect(result.grid[0][0].angle).toBeDefined()
    })

    it('should handle hexagonal maze generation', async () => {
      const { generateMaze } = await import('../../../layers/writer/components/global/book/puzzles/maze/generator')
      
      const settings = {
        size: 15,
        difficulty: 'medium' as const,
        mazeType: 'hexagonal' as const,
        pathWidth: 1,
        includeDeadEnds: true,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-maze-hexagonal',
        type: 'maze',
        size: 15,
        difficulty: 'medium' as const,
        mazeType: 'hexagonal',
        grid: Array.from({ length: 15 }, (_, row) => 
          Array.from({ length: 15 }, (_, col) => ({
            x: col,
            y: row,
            isWall: Math.random() > 0.6,
            isPath: Math.random() <= 0.6,
            shape: 'hexagon',
            neighbors: [], // Hexagonal cells have 6 neighbors instead of 4
            hexCoords: { q: col - Math.floor(row / 2), r: row, s: -col + Math.floor(row / 2) - row }
          }))
        ),
        startPosition: { x: 0, y: 7 }, // Left middle
        endPosition: { x: 14, y: 7 }, // Right middle
        solution: [],
        algorithm: 'hexagonal-recursive-backtracking',
        stats: {
          pathLength: 28,
          deadEndCount: 5,
          complexity: 7.0,
          solutionSteps: 21,
          educationalValue: 8,
          estimatedSolveTime: 420
        },
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateMaze).mockResolvedValue(mockResult)
      
      const result = await generateMaze(settings)
      expect(result.mazeType).toBe('hexagonal')
      expect(result.algorithm).toBe('hexagonal-recursive-backtracking')
      expect(result.grid[0][0].shape).toBe('hexagon')
      expect(result.grid[0][0].hexCoords).toBeDefined()
    })
  })
})