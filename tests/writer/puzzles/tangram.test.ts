import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'

// Mock the generator function
vi.mock('../../../layers/writer/components/global/book/puzzles/tangram/generator', () => ({
  generateTangram: vi.fn()
}))

// Mock global composables
globalThis.ref = vi.fn((value) => ({ value }))
globalThis.computed = vi.fn((fn) => ({ value: fn() }))
globalThis.watch = vi.fn()
globalThis.defineEmits = vi.fn(() => vi.fn())
globalThis.defineProps = vi.fn()

describe('Tangram Puzzle Components', () => {
  describe('Tangram Generator', () => {
    it('should generate valid tangram puzzle', async () => {
      const { generateTangram } = await import('../../../layers/writer/components/global/book/puzzles/tangram/generator')
      
      const settings = {
        shape: 'house' as const,
        difficulty: 'medium' as const,
        showOutline: true,
        showPieces: true,
        allowRotation: true,
        allowFlipping: false,
        gridSize: 10,
        theme: 'classic',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-tangram-1',
        type: 'tangram',
        shape: 'house',
        difficulty: 'medium' as const,
        targetShape: {
          name: 'house',
          outline: [
            { x: 5, y: 2 },
            { x: 7, y: 4 },
            { x: 9, y: 2 },
            { x: 9, y: 8 },
            { x: 5, y: 8 },
            { x: 5, y: 2 }
          ],
          area: 24,
          category: 'buildings'
        },
        pieces: [
          {
            id: 1,
            type: 'large-triangle',
            vertices: [{ x: 0, y: 0 }, { x: 4, y: 0 }, { x: 2, y: 2 }],
            color: '#FF6B6B',
            position: { x: 0, y: 0 },
            rotation: 0,
            isFlipped: false,
            area: 4
          },
          {
            id: 2,
            type: 'large-triangle',
            vertices: [{ x: 0, y: 0 }, { x: 4, y: 0 }, { x: 2, y: 2 }],
            color: '#4ECDC4',
            position: { x: 4, y: 0 },
            rotation: 0,
            isFlipped: false,
            area: 4
          },
          {
            id: 3,
            type: 'medium-triangle',
            vertices: [{ x: 0, y: 0 }, { x: 3, y: 0 }, { x: 1.5, y: 1.5 }],
            color: '#45B7D1',
            position: { x: 2, y: 2 },
            rotation: 0,
            isFlipped: false,
            area: 2.25
          },
          {
            id: 4,
            type: 'small-triangle',
            vertices: [{ x: 0, y: 0 }, { x: 2, y: 0 }, { x: 1, y: 1 }],
            color: '#96CEB4',
            position: { x: 6, y: 2 },
            rotation: 0,
            isFlipped: false,
            area: 1
          },
          {
            id: 5,
            type: 'small-triangle',
            vertices: [{ x: 0, y: 0 }, { x: 2, y: 0 }, { x: 1, y: 1 }],
            color: '#FFEAA7',
            position: { x: 6, y: 4 },
            rotation: 0,
            isFlipped: false,
            area: 1
          },
          {
            id: 6,
            type: 'square',
            vertices: [{ x: 0, y: 0 }, { x: 2, y: 0 }, { x: 2, y: 2 }, { x: 0, y: 2 }],
            color: '#DDA0DD',
            position: { x: 7, y: 6 },
            rotation: 0,
            isFlipped: false,
            area: 4
          },
          {
            id: 7,
            type: 'parallelogram',
            vertices: [{ x: 0, y: 0 }, { x: 3, y: 0 }, { x: 4, y: 1 }, { x: 1, y: 1 }],
            color: '#FDA7DF',
            position: { x: 5, y: 6 },
            rotation: 0,
            isFlipped: false,
            area: 3
          }
        ],
        solution: {
          pieces: [
            { id: 1, position: { x: 5, y: 2 }, rotation: 45, isFlipped: false },
            { id: 2, position: { x: 7, y: 2 }, rotation: 135, isFlipped: false },
            { id: 3, position: { x: 6, y: 4 }, rotation: 0, isFlipped: false },
            { id: 4, position: { x: 5, y: 6 }, rotation: 0, isFlipped: false },
            { id: 5, position: { x: 9, y: 6 }, rotation: 270, isFlipped: false },
            { id: 6, position: { x: 6, y: 6 }, rotation: 0, isFlipped: false },
            { id: 7, position: { x: 7, y: 7 }, rotation: 0, isFlipped: false }
          ],
          isComplete: true,
          completionTime: 0
        },
        hints: [
          'Start with the large triangles for the roof',
          'The square piece fits well in the middle section',
          'Use the parallelogram for the base'
        ],
        instructions: [
          'Drag pieces to form the target shape',
          'Rotate pieces by clicking and dragging corners',
          'All pieces must be used exactly once'
        ],
        stats: {
          totalPieces: 7,
          targetArea: 24,
          complexity: 6.5,
          educationalValue: 8,
          estimatedSolveTime: 480
        },
        gridSize: 10,
        theme: 'classic',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateTangram).mockResolvedValue(mockResult)
      
      const result = await generateTangram(settings)
      
      expect(result).toBeDefined()
      expect(result.type).toBe('tangram')
      expect(result.shape).toBe('house')
      expect(result.pieces).toHaveLength(7)
      expect(result.targetShape.name).toBe('house')
      expect(result.solution.pieces).toHaveLength(7)
      expect(result.stats.totalPieces).toBe(7)
    })

    it('should handle different target shapes', async () => {
      const { generateTangram } = await import('../../../layers/writer/components/global/book/puzzles/tangram/generator')
      
      const shapes = ['house', 'cat', 'bird', 'boat', 'tree', 'person', 'fish', 'butterfly'] as const
      
      for (const shape of shapes) {
        const settings = {
          shape,
          difficulty: 'medium' as const,
          showOutline: true,
          showPieces: true,
          allowRotation: true,
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00',
          fontSize: 'medium' as const,
          printLayout: 'compact' as const
        }

        const shapeData = {
          house: { category: 'buildings', area: 24, complexity: 6.0 },
          cat: { category: 'animals', area: 20, complexity: 7.5 },
          bird: { category: 'animals', area: 18, complexity: 7.0 },
          boat: { category: 'vehicles', area: 22, complexity: 5.5 },
          tree: { category: 'nature', area: 26, complexity: 6.5 },
          person: { category: 'people', area: 28, complexity: 8.0 },
          fish: { category: 'animals', area: 19, complexity: 6.0 },
          butterfly: { category: 'animals', area: 21, complexity: 8.5 }
        }

        const mockResult = {
          id: `test-tangram-${shape}`,
          type: 'tangram',
          shape,
          difficulty: 'medium' as const,
          targetShape: {
            name: shape,
            outline: Array.from({ length: 6 }, (_, i) => ({ x: i, y: i })),
            area: shapeData[shape].area,
            category: shapeData[shape].category
          },
          pieces: Array.from({ length: 7 }, (_, i) => ({
            id: i + 1,
            type: i < 2 ? 'large-triangle' : i === 2 ? 'medium-triangle' : i < 5 ? 'small-triangle' : i === 5 ? 'square' : 'parallelogram',
            vertices: [{ x: 0, y: 0 }, { x: 2, y: 0 }, { x: 1, y: 1 }],
            color: '#FF0000',
            position: { x: 0, y: 0 },
            rotation: 0,
            isFlipped: false,
            area: i < 2 ? 4 : i === 2 ? 2.25 : i < 5 ? 1 : i === 5 ? 4 : 3
          })),
          solution: {
            pieces: Array.from({ length: 7 }, (_, i) => ({
              id: i + 1,
              position: { x: i, y: i },
              rotation: 0,
              isFlipped: false
            })),
            isComplete: true
          },
          stats: {
            totalPieces: 7,
            targetArea: shapeData[shape].area,
            complexity: shapeData[shape].complexity,
            educationalValue: 8,
            estimatedSolveTime: 360
          },
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00'
        }

        vi.mocked(generateTangram).mockResolvedValue(mockResult)
        
        const result = await generateTangram(settings)
        expect(result.shape).toBe(shape)
        expect(result.targetShape.category).toBe(shapeData[shape].category)
        expect(result.stats.complexity).toBeGreaterThan(0)
      }
    })

    it('should handle different difficulty levels', async () => {
      const { generateTangram } = await import('../../../layers/writer/components/global/book/puzzles/tangram/generator')
      
      const difficulties = ['easy', 'medium', 'hard'] as const
      
      for (const difficulty of difficulties) {
        const settings = {
          shape: 'cat' as const,
          difficulty,
          showOutline: difficulty === 'easy',
          showPieces: difficulty !== 'hard',
          allowRotation: true,
          allowFlipping: difficulty === 'hard',
          gridSize: difficulty === 'easy' ? 8 : difficulty === 'medium' ? 10 : 12,
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00',
          fontSize: 'medium' as const,
          printLayout: 'compact' as const
        }

        const mockResult = {
          id: `test-tangram-${difficulty}`,
          type: 'tangram',
          shape: 'cat',
          difficulty,
          targetShape: {
            name: 'cat',
            outline: Array.from({ length: 8 }, (_, i) => ({ x: i, y: Math.sin(i) * 2 })),
            area: 20,
            category: 'animals',
            showOutline: difficulty === 'easy'
          },
          pieces: Array.from({ length: 7 }, (_, i) => ({
            id: i + 1,
            type: ['large-triangle', 'large-triangle', 'medium-triangle', 'small-triangle', 'small-triangle', 'square', 'parallelogram'][i],
            vertices: [{ x: 0, y: 0 }, { x: 2, y: 0 }, { x: 1, y: 1 }],
            color: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#FDA7DF'][i],
            position: { x: i * 2, y: 0 },
            rotation: 0,
            isFlipped: false,
            area: [4, 4, 2.25, 1, 1, 4, 3][i]
          })),
          solution: {
            pieces: Array.from({ length: 7 }, (_, i) => ({
              id: i + 1,
              position: { x: i + 2, y: i + 1 },
              rotation: i * 45,
              isFlipped: difficulty === 'hard' && i % 2 === 0
            })),
            isComplete: true
          },
          hints: difficulty === 'easy' ? [
            'Start with the largest pieces',
            'The cat\'s body uses the large triangles',
            'The tail is made with the parallelogram'
          ] : difficulty === 'medium' ? [
            'Focus on the overall shape first',
            'The head uses smaller pieces'
          ] : [],
          stats: {
            totalPieces: 7,
            targetArea: 20,
            complexity: difficulty === 'easy' ? 4.0 : difficulty === 'medium' ? 7.0 : 9.0,
            educationalValue: 8,
            estimatedSolveTime: difficulty === 'easy' ? 240 : difficulty === 'medium' ? 480 : 720
          },
          gridSize: difficulty === 'easy' ? 8 : difficulty === 'medium' ? 10 : 12,
          textColor: '#000000',
          backgroundColor: '#ffffff',
          accentColor: '#0066cc',
          highlightColor: '#ffff00'
        }

        vi.mocked(generateTangram).mockResolvedValue(mockResult)
        
        const result = await generateTangram(settings)
        expect(result.difficulty).toBe(difficulty)
        expect(result.targetShape.showOutline).toBe(difficulty === 'easy')
        expect(result.stats.complexity).toBeGreaterThan(0)
        expect(result.solution.pieces.some(p => p.isFlipped)).toBe(difficulty === 'hard')
      }
    })

    it('should handle custom piece configurations', async () => {
      const { generateTangram } = await import('../../../layers/writer/components/global/book/puzzles/tangram/generator')
      
      const settings = {
        shape: 'butterfly' as const,
        difficulty: 'hard' as const,
        showOutline: false,
        showPieces: false,
        allowRotation: true,
        allowFlipping: true,
        gridSize: 12,
        customPieces: true,
        pieceSet: 'extended' as const, // Use extended set with additional pieces
        theme: 'colorful',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-tangram-custom',
        type: 'tangram',
        shape: 'butterfly',
        difficulty: 'hard' as const,
        targetShape: {
          name: 'butterfly',
          outline: [
            { x: 6, y: 2 }, { x: 4, y: 4 }, { x: 2, y: 2 }, { x: 2, y: 6 },
            { x: 4, y: 8 }, { x: 6, y: 6 }, { x: 8, y: 8 }, { x: 10, y: 6 },
            { x: 10, y: 2 }, { x: 8, y: 4 }, { x: 6, y: 2 }
          ],
          area: 32,
          category: 'animals',
          symmetry: 'bilateral'
        },
        pieces: [
          {
            id: 1,
            type: 'large-triangle',
            vertices: [{ x: 0, y: 0 }, { x: 4, y: 0 }, { x: 2, y: 2 }],
            color: '#FF6B6B',
            position: { x: 0, y: 0 },
            rotation: 0,
            isFlipped: false,
            area: 4
          },
          {
            id: 2,
            type: 'large-triangle',
            vertices: [{ x: 0, y: 0 }, { x: 4, y: 0 }, { x: 2, y: 2 }],
            color: '#4ECDC4',
            position: { x: 4, y: 0 },
            rotation: 0,
            isFlipped: true, // Flipped for symmetry
            area: 4
          },
          {
            id: 8,
            type: 'diamond',
            vertices: [{ x: 1, y: 0 }, { x: 2, y: 1 }, { x: 1, y: 2 }, { x: 0, y: 1 }],
            color: '#9B59B6',
            position: { x: 6, y: 4 },
            rotation: 0,
            isFlipped: false,
            area: 2
          },
          {
            id: 9,
            type: 'hexagon',
            vertices: Array.from({ length: 6 }, (_, i) => ({
              x: Math.cos(i * Math.PI / 3),
              y: Math.sin(i * Math.PI / 3)
            })),
            color: '#E67E22',
            position: { x: 6, y: 6 },
            rotation: 0,
            isFlipped: false,
            area: 2.6
          }
        ],
        solution: {
          pieces: [
            { id: 1, position: { x: 2, y: 4 }, rotation: 45, isFlipped: false },
            { id: 2, position: { x: 10, y: 4 }, rotation: 135, isFlipped: true },
            { id: 8, position: { x: 6, y: 5 }, rotation: 0, isFlipped: false },
            { id: 9, position: { x: 6, y: 3 }, rotation: 30, isFlipped: false }
          ],
          isComplete: true,
          symmetryMaintained: true
        },
        hints: [],
        stats: {
          totalPieces: 4, // Extended set uses fewer but more complex pieces
          targetArea: 32,
          complexity: 9.5,
          educationalValue: 9,
          estimatedSolveTime: 900
        },
        pieceSet: 'extended',
        theme: 'colorful',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateTangram).mockResolvedValue(mockResult)
      
      const result = await generateTangram(settings)
      expect(result.pieces.some(p => p.type === 'diamond')).toBe(true)
      expect(result.pieces.some(p => p.type === 'hexagon')).toBe(true)
      expect(result.solution.symmetryMaintained).toBe(true)
      expect(result.pieceSet).toBe('extended')
    })
  })

  describe('Tangram Components Mock Tests', () => {
    let mockCustomizeComponent: any
    let mockPreviewComponent: any
    let mockSolutionComponent: any

    beforeEach(() => {
      mockCustomizeComponent = {
        template: `
          <div data-testid="tangram-customize">
            <select v-model="shape">
              <option value="house">House</option>
              <option value="cat">Cat</option>
              <option value="bird">Bird</option>
              <option value="boat">Boat</option>
              <option value="tree">Tree</option>
              <option value="person">Person</option>
              <option value="fish">Fish</option>
              <option value="butterfly">Butterfly</option>
            </select>
            <select v-model="difficulty">
              <option value="easy">Easy</option>
              <option value="medium">Medium</option>
              <option value="hard">Hard</option>
            </select>
            <input v-model="gridSize" type="number" min="8" max="16" />
            <label>
              <input type="checkbox" v-model="showOutline" />
              Show Target Outline
            </label>
            <label>
              <input type="checkbox" v-model="showPieces" />
              Show Piece Positions
            </label>
            <label>
              <input type="checkbox" v-model="allowRotation" />
              Allow Rotation
            </label>
            <label>
              <input type="checkbox" v-model="allowFlipping" />
              Allow Flipping
            </label>
            <select v-model="theme">
              <option value="classic">Classic</option>
              <option value="colorful">Colorful</option>
              <option value="monochrome">Monochrome</option>
              <option value="pastel">Pastel</option>
            </select>
            <select v-model="pieceSet">
              <option value="traditional">Traditional (7 pieces)</option>
              <option value="extended">Extended (10 pieces)</option>
              <option value="minimal">Minimal (5 pieces)</option>
            </select>
          </div>
        `,
        props: ['modelValue'],
        emits: ['update:modelValue'],
        data() {
          return {
            shape: 'house',
            difficulty: 'medium',
            gridSize: 10,
            showOutline: true,
            showPieces: true,
            allowRotation: true,
            allowFlipping: false,
            theme: 'classic',
            pieceSet: 'traditional'
          }
        }
      }

      mockPreviewComponent = {
        template: `
          <div data-testid="tangram-preview">
            <div class="tangram-header">
              <h3>Tangram: {{ targetShape?.name?.charAt(0)?.toUpperCase() + targetShape?.name?.slice(1) || 'Shape' }}</h3>
              <div class="tangram-info">
                <span class="difficulty-badge">{{ difficulty?.toUpperCase() }}</span>
                <span class="category-info">{{ targetShape?.category?.charAt(0)?.toUpperCase() + targetShape?.category?.slice(1) }}</span>
                <span class="grid-info">{{ gridSize }}×{{ gridSize }}</span>
              </div>
            </div>
            <div class="tangram-workspace">
              <div class="target-area">
                <h4>Target Shape</h4>
                <svg :width="gridSize * 30" :height="gridSize * 30" class="target-shape">
                  <polygon v-if="showOutline && targetShape?.outline" 
                           :points="targetShape.outline.map(p => (p.x * 30) + ',' + (p.y * 30)).join(' ')"
                           fill="none" 
                           stroke="#333" 
                           stroke-width="2" 
                           stroke-dasharray="5,5" />
                  <text x="10" y="25" font-size="12">{{ targetShape?.area || 0 }} sq units</text>
                </svg>
              </div>
              <div class="pieces-area">
                <h4>Tangram Pieces</h4>
                <div class="pieces-container">
                  <div v-for="piece in pieces" :key="piece.id" 
                       class="tangram-piece"
                       :style="{ 
                         backgroundColor: piece.color,
                         transform: 'rotate(' + piece.rotation + 'deg) ' + (piece.isFlipped ? 'scaleX(-1)' : ''),
                         left: piece.position.x * 20 + 'px',
                         top: piece.position.y * 20 + 'px'
                       }">
                    <span class="piece-label">{{ piece.type }}</span>
                    <span class="piece-area">{{ piece.area }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="tangram-controls">
              <div class="control-group">
                <label>
                  <input type="checkbox" v-model="showOutline" />
                  Show Outline
                </label>
                <label>
                  <input type="checkbox" v-model="showPieces" />
                  Show Pieces
                </label>
              </div>
              <div class="rotation-controls" v-if="allowRotation">
                <button @click="rotatePiece(-45)">Rotate Left</button>
                <button @click="rotatePiece(45)">Rotate Right</button>
              </div>
              <div class="flip-controls" v-if="allowFlipping">
                <button @click="flipPiece">Flip Piece</button>
              </div>
            </div>
            <div v-if="hints && hints.length > 0" class="hints">
              <h4>Hints:</h4>
              <ul>
                <li v-for="hint in hints" :key="hint">{{ hint }}</li>
              </ul>
            </div>
            <div class="tangram-stats">
              <p>Total Pieces: {{ stats?.totalPieces || 0 }}</p>
              <p>Target Area: {{ stats?.targetArea || 0 }} square units</p>
              <p>Complexity: {{ stats?.complexity?.toFixed(1) || 0 }}/10</p>
              <p>Estimated Time: {{ Math.floor((stats?.estimatedSolveTime || 0) / 60) }} minutes</p>
            </div>
          </div>
        `,
        props: ['targetShape', 'pieces', 'difficulty', 'gridSize', 'showOutline', 'showPieces', 'allowRotation', 'allowFlipping', 'hints', 'stats'],
        data() {
          return {
            selectedPiece: null
          }
        },
        methods: {
          rotatePiece(angle: number) {
            if (this.selectedPiece) {
              this.selectedPiece.rotation += angle
            }
          },
          flipPiece() {
            if (this.selectedPiece) {
              this.selectedPiece.isFlipped = !this.selectedPiece.isFlipped
            }
          }
        }
      }

      mockSolutionComponent = {
        template: `
          <div data-testid="tangram-solution">
            <div class="solution-header">
              <h3>Tangram Solution: {{ targetShape?.name?.charAt(0)?.toUpperCase() + targetShape?.name?.slice(1) || 'Shape' }}</h3>
            </div>
            <div class="solution-content">
              <div class="solved-tangram">
                <h4>Completed Shape</h4>
                <svg :width="gridSize * 30" :height="gridSize * 30" class="solution-display">
                  <g v-for="piece in solution?.pieces" :key="piece.id" class="solution-piece">
                    <polygon :points="getPiecePoints(piece)" 
                             :fill="getPieceColor(piece.id)"
                             stroke="#333" 
                             stroke-width="1" />
                    <text :x="piece.position.x * 30 + 15" :y="piece.position.y * 30 + 15" 
                          font-size="10" 
                          text-anchor="middle" 
                          fill="white">
                      {{ piece.id }}
                    </text>
                  </g>
                  <polygon v-if="targetShape?.outline" 
                           :points="targetShape.outline.map(p => (p.x * 30) + ',' + (p.y * 30)).join(' ')"
                           fill="none" 
                           stroke="#000" 
                           stroke-width="3" />
                </svg>
              </div>
              <div class="piece-placement">
                <h4>Piece Positions</h4>
                <div class="placement-list">
                  <div v-for="piece in solution?.pieces" :key="piece.id" class="placement-item">
                    <span class="piece-id">Piece {{ piece.id }}:</span>
                    <span class="position">{{ piece.position.x }}, {{ piece.position.y }}</span>
                    <span class="rotation">{{ piece.rotation }}°</span>
                    <span v-if="piece.isFlipped" class="flipped">Flipped</span>
                  </div>
                </div>
              </div>
              <div class="solution-analysis">
                <h4>Solution Analysis</h4>
                <ul>
                  <li>Total area used: {{ stats?.targetArea || 0 }} square units</li>
                  <li>Pieces required: {{ stats?.totalPieces || 0 }}</li>
                  <li>Rotations needed: {{ countRotations() }}</li>
                  <li v-if="solution?.symmetryMaintained">Shape maintains bilateral symmetry</li>
                  <li>Shape category: {{ targetShape?.category || 'unknown' }}</li>
                </ul>
              </div>
              <div v-if="targetShape?.category" class="educational-content">
                <h4>About {{ targetShape.category.charAt(0).toUpperCase() + targetShape.category.slice(1) }}</h4>
                <p v-if="targetShape.category === 'animals'">
                  Animal tangrams help develop spatial reasoning while learning about different creatures and their characteristics.
                </p>
                <p v-else-if="targetShape.category === 'buildings'">
                  Building tangrams introduce basic architectural concepts and geometric relationships in structures.
                </p>
                <p v-else-if="targetShape.category === 'vehicles'">
                  Vehicle tangrams combine spatial skills with understanding of transportation and movement.
                </p>
                <p v-else>
                  Tangrams develop spatial reasoning, problem-solving skills, and geometric understanding.
                </p>
              </div>
            </div>
          </div>
        `,
        props: ['targetShape', 'solution', 'stats', 'gridSize', 'pieces'],
        methods: {
          getPiecePoints(piece: any) {
            // Mock method to calculate piece vertices based on position and rotation
            return '0,0 30,0 15,15'
          },
          getPieceColor(pieceId: number) {
            const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#FDA7DF']
            return colors[(pieceId - 1) % colors.length]
          },
          countRotations() {
            return this.solution?.pieces?.filter((p: any) => p.rotation !== 0).length || 0
          }
        }
      }
    })

    it('should render customize component', () => {
      const wrapper = mount(mockCustomizeComponent, {
        props: { modelValue: { shape: 'house', difficulty: 'medium', gridSize: 10 } }
      })
      
      expect(wrapper.find('[data-testid="tangram-customize"]').exists()).toBe(true)
      expect(wrapper.findAll('select')).toHaveLength(4)
      expect(wrapper.find('input[type="number"]').exists()).toBe(true)
      expect(wrapper.findAll('input[type="checkbox"]')).toHaveLength(4)
    })

    it('should render preview component with target shape and pieces', () => {
      const props = {
        targetShape: {
          name: 'cat',
          outline: [{ x: 2, y: 1 }, { x: 4, y: 1 }, { x: 5, y: 3 }, { x: 3, y: 5 }, { x: 1, y: 3 }],
          area: 12,
          category: 'animals'
        },
        pieces: [
          {
            id: 1,
            type: 'large-triangle',
            color: '#FF6B6B',
            position: { x: 0, y: 0 },
            rotation: 0,
            isFlipped: false,
            area: 4
          },
          {
            id: 2,
            type: 'medium-triangle',
            color: '#4ECDC4',
            position: { x: 2, y: 0 },
            rotation: 45,
            isFlipped: false,
            area: 2.25
          }
        ],
        difficulty: 'medium',
        gridSize: 10,
        showOutline: true,
        showPieces: true,
        allowRotation: true,
        allowFlipping: false,
        hints: ['Start with the large triangle', 'The cat\'s head uses smaller pieces'],
        stats: {
          totalPieces: 2,
          targetArea: 12,
          complexity: 6.5,
          estimatedSolveTime: 360
        }
      }
      
      const wrapper = mount(mockPreviewComponent, { props })
      
      expect(wrapper.find('[data-testid="tangram-preview"]').exists()).toBe(true)
      expect(wrapper.find('.target-area').exists()).toBe(true)
      expect(wrapper.find('.pieces-area').exists()).toBe(true)
      expect(wrapper.findAll('.tangram-piece')).toHaveLength(2)
      expect(wrapper.text()).toContain('Cat')
      expect(wrapper.text()).toContain('Animals')
      expect(wrapper.text()).toContain('MEDIUM')
      expect(wrapper.text()).toContain('Start with the large triangle')
    })

    it('should render solution component with completed tangram', () => {
      const props = {
        targetShape: {
          name: 'house',
          outline: [{ x: 2, y: 1 }, { x: 4, y: 3 }, { x: 6, y: 1 }, { x: 6, y: 5 }, { x: 2, y: 5 }],
          area: 16,
          category: 'buildings'
        },
        solution: {
          pieces: [
            { id: 1, position: { x: 2, y: 1 }, rotation: 45, isFlipped: false },
            { id: 2, position: { x: 4, y: 1 }, rotation: 135, isFlipped: false },
            { id: 3, position: { x: 3, y: 3 }, rotation: 0, isFlipped: false }
          ],
          isComplete: true,
          symmetryMaintained: false
        },
        stats: {
          totalPieces: 3,
          targetArea: 16,
          complexity: 6.0,
          estimatedSolveTime: 240
        },
        gridSize: 10,
        pieces: [
          { id: 1, type: 'large-triangle', color: '#FF6B6B' },
          { id: 2, type: 'large-triangle', color: '#4ECDC4' },
          { id: 3, type: 'medium-triangle', color: '#45B7D1' }
        ]
      }
      
      const wrapper = mount(mockSolutionComponent, { props })
      
      expect(wrapper.find('[data-testid="tangram-solution"]').exists()).toBe(true)
      expect(wrapper.find('.solved-tangram').exists()).toBe(true)
      expect(wrapper.find('.piece-placement').exists()).toBe(true)
      expect(wrapper.find('.solution-analysis').exists()).toBe(true)
      expect(wrapper.text()).toContain('House')
      expect(wrapper.text()).toContain('Total area used: 16 square units')
      expect(wrapper.text()).toContain('Pieces required: 3')
      expect(wrapper.text()).toContain('Building tangrams introduce')
    })
  })

  describe('Tangram Settings Validation', () => {
    it('should validate grid size constraints', () => {
      const validSizes = [8, 10, 12, 14, 16]
      const invalidSizes = [6, 7, 18, 20]
      
      validSizes.forEach(size => {
        expect(size).toBeGreaterThanOrEqual(8)
        expect(size).toBeLessThanOrEqual(16)
      })
      
      invalidSizes.forEach(size => {
        expect(size < 8 || size > 16).toBe(true)
      })
    })

    it('should validate shape options', () => {
      const validShapes = ['house', 'cat', 'bird', 'boat', 'tree', 'person', 'fish', 'butterfly']
      const invalidShapes = ['invalid', '', 'dragon']
      
      validShapes.forEach(shape => {
        expect(['house', 'cat', 'bird', 'boat', 'tree', 'person', 'fish', 'butterfly']).toContain(shape)
      })
      
      invalidShapes.forEach(shape => {
        expect(['house', 'cat', 'bird', 'boat', 'tree', 'person', 'fish', 'butterfly']).not.toContain(shape)
      })
    })

    it('should validate piece set options', () => {
      const validSets = ['traditional', 'extended', 'minimal']
      const invalidSets = ['invalid', '', 'custom']
      
      validSets.forEach(set => {
        expect(['traditional', 'extended', 'minimal']).toContain(set)
      })
      
      invalidSets.forEach(set => {
        expect(['traditional', 'extended', 'minimal']).not.toContain(set)
      })
    })
  })

  describe('Tangram Edge Cases', () => {
    it('should handle minimal piece set', async () => {
      const { generateTangram } = await import('../../../layers/writer/components/global/book/puzzles/tangram/generator')
      
      const settings = {
        shape: 'boat' as const,
        difficulty: 'easy' as const,
        showOutline: true,
        showPieces: true,
        allowRotation: false,
        allowFlipping: false,
        gridSize: 8,
        pieceSet: 'minimal' as const,
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-tangram-minimal',
        type: 'tangram',
        shape: 'boat',
        difficulty: 'easy' as const,
        targetShape: {
          name: 'boat',
          outline: [{ x: 1, y: 4 }, { x: 6, y: 4 }, { x: 7, y: 5 }, { x: 6, y: 6 }, { x: 1, y: 6 }],
          area: 10,
          category: 'vehicles'
        },
        pieces: [
          {
            id: 1,
            type: 'large-triangle',
            vertices: [{ x: 0, y: 0 }, { x: 4, y: 0 }, { x: 2, y: 2 }],
            color: '#FF6B6B',
            position: { x: 0, y: 0 },
            rotation: 0,
            isFlipped: false,
            area: 4
          },
          {
            id: 2,
            type: 'large-triangle',
            vertices: [{ x: 0, y: 0 }, { x: 4, y: 0 }, { x: 2, y: 2 }],
            color: '#4ECDC4',
            position: { x: 4, y: 0 },
            rotation: 0,
            isFlipped: false,
            area: 4
          },
          {
            id: 3,
            type: 'square',
            vertices: [{ x: 0, y: 0 }, { x: 2, y: 0 }, { x: 2, y: 2 }, { x: 0, y: 2 }],
            color: '#45B7D1',
            position: { x: 2, y: 2 },
            rotation: 0,
            isFlipped: false,
            area: 4
          }
        ],
        solution: {
          pieces: [
            { id: 1, position: { x: 1, y: 4 }, rotation: 0, isFlipped: false },
            { id: 2, position: { x: 5, y: 4 }, rotation: 0, isFlipped: false },
            { id: 3, position: { x: 3, y: 5 }, rotation: 0, isFlipped: false }
          ],
          isComplete: true
        },
        stats: {
          totalPieces: 3,
          targetArea: 10,
          complexity: 3.0,
          educationalValue: 6,
          estimatedSolveTime: 180
        },
        pieceSet: 'minimal',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateTangram).mockResolvedValue(mockResult)
      
      const result = await generateTangram(settings)
      expect(result.pieces).toHaveLength(3)
      expect(result.pieceSet).toBe('minimal')
      expect(result.stats.complexity).toBeLessThan(4)
    })

    it('should handle symmetrical shapes with flipping enabled', async () => {
      const { generateTangram } = await import('../../../layers/writer/components/global/book/puzzles/tangram/generator')
      
      const settings = {
        shape: 'butterfly' as const,
        difficulty: 'hard' as const,
        showOutline: false,
        showPieces: false,
        allowRotation: true,
        allowFlipping: true,
        gridSize: 14,
        theme: 'colorful',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-tangram-symmetrical',
        type: 'tangram',
        shape: 'butterfly',
        difficulty: 'hard' as const,
        targetShape: {
          name: 'butterfly',
          outline: [
            { x: 7, y: 2 }, { x: 5, y: 4 }, { x: 3, y: 2 }, { x: 3, y: 8 },
            { x: 5, y: 10 }, { x: 7, y: 8 }, { x: 9, y: 10 }, { x: 11, y: 8 },
            { x: 11, y: 2 }, { x: 9, y: 4 }, { x: 7, y: 2 }
          ],
          area: 40,
          category: 'animals',
          symmetry: 'bilateral'
        },
        pieces: Array.from({ length: 7 }, (_, i) => ({
          id: i + 1,
          type: ['large-triangle', 'large-triangle', 'medium-triangle', 'small-triangle', 'small-triangle', 'square', 'parallelogram'][i],
          vertices: [{ x: 0, y: 0 }, { x: 2, y: 0 }, { x: 1, y: 1 }],
          color: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#FDA7DF'][i],
          position: { x: i * 2, y: 0 },
          rotation: 0,
          isFlipped: false,
          area: [4, 4, 2.25, 1, 1, 4, 3][i]
        })),
        solution: {
          pieces: [
            { id: 1, position: { x: 3, y: 4 }, rotation: 45, isFlipped: false },
            { id: 2, position: { x: 11, y: 4 }, rotation: 135, isFlipped: true },
            { id: 3, position: { x: 7, y: 6 }, rotation: 0, isFlipped: false },
            { id: 4, position: { x: 5, y: 8 }, rotation: 90, isFlipped: false },
            { id: 5, position: { x: 9, y: 8 }, rotation: 270, isFlipped: true },
            { id: 6, position: { x: 6, y: 7 }, rotation: 0, isFlipped: false },
            { id: 7, position: { x: 7, y: 9 }, rotation: 0, isFlipped: false }
          ],
          isComplete: true,
          symmetryMaintained: true
        },
        stats: {
          totalPieces: 7,
          targetArea: 40,
          complexity: 9.5,
          educationalValue: 10,
          estimatedSolveTime: 900
        },
        theme: 'colorful',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateTangram).mockResolvedValue(mockResult)
      
      const result = await generateTangram(settings)
      expect(result.targetShape.symmetry).toBe('bilateral')
      expect(result.solution.symmetryMaintained).toBe(true)
      expect(result.solution.pieces.some(p => p.isFlipped)).toBe(true)
      expect(result.stats.complexity).toBeGreaterThan(9)
    })

    it('should handle large grid sizes with complex shapes', async () => {
      const { generateTangram } = await import('../../../layers/writer/components/global/book/puzzles/tangram/generator')
      
      const settings = {
        shape: 'person' as const,
        difficulty: 'hard' as const,
        showOutline: false,
        showPieces: false,
        allowRotation: true,
        allowFlipping: true,
        gridSize: 16, // Maximum
        theme: 'monochrome',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00',
        fontSize: 'medium' as const,
        printLayout: 'compact' as const
      }

      const mockResult = {
        id: 'test-tangram-large-grid',
        type: 'tangram',
        shape: 'person',
        difficulty: 'hard' as const,
        targetShape: {
          name: 'person',
          outline: [
            { x: 8, y: 2 }, { x: 6, y: 4 }, { x: 10, y: 4 }, // Head
            { x: 7, y: 6 }, { x: 9, y: 6 }, // Shoulders
            { x: 6, y: 10 }, { x: 10, y: 10 }, // Arms
            { x: 7, y: 12 }, { x: 9, y: 12 }, // Body
            { x: 6, y: 16 }, { x: 8, y: 16 }, { x: 10, y: 16 } // Legs
          ],
          area: 56,
          category: 'people'
        },
        pieces: Array.from({ length: 7 }, (_, i) => ({
          id: i + 1,
          type: ['large-triangle', 'large-triangle', 'medium-triangle', 'small-triangle', 'small-triangle', 'square', 'parallelogram'][i],
          vertices: [{ x: 0, y: 0 }, { x: 2, y: 0 }, { x: 1, y: 1 }],
          color: '#666666', // Monochrome theme
          position: { x: i * 2, y: 0 },
          rotation: 0,
          isFlipped: false,
          area: [4, 4, 2.25, 1, 1, 4, 3][i]
        })),
        solution: {
          pieces: [
            { id: 1, position: { x: 8, y: 2 }, rotation: 0, isFlipped: false }, // Head
            { id: 2, position: { x: 7, y: 6 }, rotation: 90, isFlipped: false }, // Torso
            { id: 3, position: { x: 6, y: 10 }, rotation: 45, isFlipped: false }, // Left arm
            { id: 4, position: { x: 10, y: 10 }, rotation: 135, isFlipped: true }, // Right arm
            { id: 5, position: { x: 6, y: 16 }, rotation: 0, isFlipped: false }, // Left leg
            { id: 6, position: { x: 8, y: 12 }, rotation: 0, isFlipped: false }, // Body center
            { id: 7, position: { x: 10, y: 16 }, rotation: 0, isFlipped: true } // Right leg
          ],
          isComplete: true
        },
        stats: {
          totalPieces: 7,
          targetArea: 56,
          complexity: 9.8,
          educationalValue: 10,
          estimatedSolveTime: 1200
        },
        gridSize: 16,
        theme: 'monochrome',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        accentColor: '#0066cc',
        highlightColor: '#ffff00'
      }

      vi.mocked(generateTangram).mockResolvedValue(mockResult)
      
      const result = await generateTangram(settings)
      expect(result.gridSize).toBe(16)
      expect(result.targetShape.area).toBe(56)
      expect(result.stats.complexity).toBeGreaterThan(9.5)
      expect(result.theme).toBe('monochrome')
    })
  })
})